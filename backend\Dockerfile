# Build stage
FROM node:20-alpine AS builder

# Définir le répertoire de travail
WORKDIR /app

# Installer TypeScript globalement
RUN npm install -g typescript

# Copier les fichiers de dépendances
COPY backend/package*.json ./

# Copier le reste des fichiers du projet
COPY backend/ .

# Installer Sharp spécifiquement pour Alpine Linux
RUN npm install --platform=linuxmusl --arch=x64 sharp

# Installer toutes les dépendances (y compris devDependencies)
RUN npm install

# Compiler le TypeScript
RUN npm run build

# Production stage
FROM node:20-alpine

WORKDIR /app

# Installer curl pour le healthcheck et les polices système pour Sharp
RUN apk update && apk add --no-cache curl fontconfig ttf-dejavu

# Copier package.json et package-lock.json
COPY backend/package*.json ./

# Installer Sharp spécifiquement pour Alpine Linux
RUN npm install --platform=linuxmusl --arch=x64 sharp

# Installer les dépendances de production
RUN npm ci --only=production

# Copier les fichiers compilés depuis l'étape de build
COPY --from=builder /app/dist ./dist

# Copier le script flush-redis.js pour le vidage du cache Redis au démarrage
COPY backend/flush-redis.js ./flush-redis.js

# Définir des valeurs par défaut pour les variables d'environnement non sensibles
ENV NODE_ENV=production \
    PORT=3001

# Les variables sensibles doivent être fournies lors du déploiement
ARG SUPABASE_URL
ARG SUPABASE_SERVICE_ROLE_KEY
ARG JWT_SECRET
ARG PASSWORD_SALT
ARG SMTP_USER
ARG SMTP_PASS
ARG FRONTEND_URL

ENV SUPABASE_URL=${SUPABASE_URL} \
    SUPABASE_SERVICE_ROLE_KEY=${SUPABASE_SERVICE_ROLE_KEY} \
    JWT_SECRET=${JWT_SECRET} \
    PASSWORD_SALT=${PASSWORD_SALT} \
    SMTP_HOST=hc-justfawn-eu.n0c.com \
    SMTP_PORT=465 \
    SMTP_SECURE=true \
    SMTP_USER=${SMTP_USER} \
    SMTP_PASS=${SMTP_PASS} \
    SMTP_FROM=<EMAIL> \
    FRONTEND_URL=${FRONTEND_URL}

# Exposer le port 3001
EXPOSE 3001

# Add healthcheck via l'api
HEALTHCHECK --interval=30s --timeout=3s --start-period=1s --retries=10 \
    CMD curl -f http://localhost:3001/api/health || exit 1

# Démarrer l'application
CMD ["node", "dist/server.js"]
