import { Request, Response } from 'express';
import { supabase } from '../config/supabase';
import { redis } from '../config/redis';
import logger from '../utils/logger';
import { logUserActivity, getIpFromRequest } from '../utils/activityLogger';
import { sendAiCreditsEmail } from '../services/emailAiCredits';
import { stripe } from '../config/stripe';
import { subscriptions } from '../config/ConfigSubscriptions';
import { decryptUserDataAsync } from '../utils/encryption';

// Constantes pour les crédits IA
const CACHE_PREFIX = 'ai_credits:';
const NOTIFICATIONS_CACHE_PREFIX = 'notifications:'; // Préfixe pour le cache des notifications
const CACHE_DURATION = 60 * 60; // 1 heure
const CREDIT_MAX = 500; // Plafond maximum de crédits IA par utilisateur

/**
 * Enregistre une opération dans l'historique des crédits IA
 */
export const logAiCreditsOperation = async (
  userId: string,
  operationType: 'achat_jobi' | 'achat_stripe' | 'utilisation' | 'offert_abonnement' | 'offert_admin' | 'autre',
  montant: number,
  soldeAvant: number,
  soldeApres: number,
  description?: string,
  reference?: string,
  ipAddress?: string,
  extraPurpose?: string // Ajout d'un paramètre optionnel pour le type d'utilisation IA (ex: profile_picture)
): Promise<boolean> => {
  try {
    // Générer une description automatique si non fournie et si c'est une utilisation IA
    let finalDescription = description;
    if (!finalDescription && operationType === 'utilisation') {
      switch (extraPurpose) {
        case 'profile_picture':
          finalDescription = "Image de profil générée par IA";
          break;
        case 'banner_picture':
          finalDescription = "Bannière de profil générée par IA";
          break;
        case 'mission_image':
          finalDescription = "Image de mission générée par IA";
          break;
        case 'gallery_photo':
          finalDescription = "Image de galerie générée par IA";
          break;
        case 'featured_photo':
          finalDescription = "Image mise en avant générée par IA";
          break;
        case 'biography':
          finalDescription = "Biographie générée par IA";
          break;
        case 'service_description':
          finalDescription = "Description de service générée par IA";
          break;
        case 'mission_post':
          finalDescription = "Publication de mission générée par IA";
          break;
        case 'review_response':
          finalDescription = "Réponse à un avis générée par IA";
          break;
        case 'mission_offer':
          finalDescription = "Offre de mission générée par IA";
          break;
        case 'comment':
          finalDescription = "Commentaire généré par IA";
          break;
        case 'default_prompt':
          finalDescription = "Contenu personnalisé généré par IA";
          break;
        case 'card_editor':
          finalDescription = "Carte de visite/flyer générée par IA";
          break;
        default:
          finalDescription = "Crédit IA utilisé pour une génération de contenu";
      }
    }
    // Enregistrer dans la table spécifique d'historique des crédits IA
    const { error } = await supabase
      .from('user_ai_credits_historique')
      .insert({
        user_id: userId,
        operation_type: operationType,
        montant,
        solde_avant: soldeAvant,
        solde_apres: soldeApres,
        description: finalDescription,
        reference,
        created_at: new Date().toISOString()
      });

    if (error) {
      logger.error('Erreur lors de l\'enregistrement de l\'historique des crédits IA:', error);
      return false;
    }

    // Mapper le type d'opération vers un type d'action pour l'historique général
    let actionType = '';
    switch (operationType) {
      case 'achat_jobi':
        actionType = 'ai_credits_purchase_jobi';
        break;
      case 'achat_stripe':
        actionType = 'ai_credits_purchase_stripe';
        break;
      case 'utilisation':
        actionType = 'ai_credits_used';
        break;
      case 'offert_abonnement':
        actionType = 'ai_credits_subscription_gift';
        break;
      case 'offert_admin':
        actionType = 'ai_credits_admin_gift';
        break;
      default:
        actionType = 'ai_credits_other';
    }

    // Enregistrer dans l'historique général des activités utilisateur
    await logUserActivity(
      userId,
      actionType,
      undefined,
      'ai_credits',
      JSON.stringify({
        operation_type: operationType,
        montant,
        solde_avant: soldeAvant,
        solde_apres: soldeApres,
        description
      }),
      ipAddress
    );

    return true;
  } catch (error) {
    logger.error('Erreur lors de l\'enregistrement de l\'historique des crédits IA:', error);
    return false;
  }
};

/**
 * Récupère le nombre de crédits IA d'un utilisateur
 */
export const getAiCredits = async (req: Request, res: Response) => {
  try {
    // Récupérer l'ID utilisateur depuis req.user.id ou req.user.userId
    const userId = req.user?.id || req.user?.userId;

    if (!userId) {
      return res.status(401).json({
        success: false,
        message: 'Utilisateur non authentifié',
        toastType: 'error'
      });
    }

    // Vérifier si les crédits sont en cache
    const cacheKey = `${CACHE_PREFIX}${userId}`;
    const cachedCredits = await redis.get(cacheKey);

    if (cachedCredits) {
      logger.info('Crédits IA récupérés depuis le cache:', cachedCredits);
      return res.status(200).json({
        success: true,
        credits: parseInt(cachedCredits),
        toastType: 'success'
      });
    }

    // Étape 1: Assurer l'existence de l'enregistrement (insérer si absent)
    const { error: upsertError } = await supabase
      .from('user_ai_credits')
      .upsert(
        {
          user_id: userId,
          // Pas besoin de spécifier les crédits ici si on veut juste assurer l'existence
          // ou on peut mettre à jour la date `updated_at` si on le souhaite
          updated_at: new Date().toISOString()
        },
        {
          onConflict: 'user_id',
          ignoreDuplicates: false, // Ne pas ignorer pour pouvoir mettre à jour `updated_at`
          // Si on veut juste insérer sans MAJ si existe: ignoreDuplicates: true et retirer updated_at
        }
      );

    // Gérer les erreurs potentielles de l'upsert (ex: violation de contrainte non liée à user_id)
    // L'erreur 23505 (clé dupliquée) ne devrait pas arriver grâce à onConflict
    if (upsertError) {
      logger.error('Erreur lors de l\'upsert initial des crédits IA:', upsertError);
      return res.status(500).json({
        success: false,
        message: 'Erreur lors de l\'initialisation des crédits IA',
        toastType: 'error'
      });
    }

    // Étape 2: Récupérer les crédits après s'être assuré de l'existence de la ligne
    const { data: creditsData, error: selectError } = await supabase
      .from('user_ai_credits')
      .select('credits')
      .eq('user_id', userId)
      .single();

    if (selectError) {
      // Gérer le cas (improbable) où la ligne n'est toujours pas trouvée ou autre erreur select
      logger.error('Erreur lors de la récupération des crédits IA après upsert:', selectError);
      return res.status(500).json({
        success: false,
        message: 'Erreur lors de la récupération des crédits IA',
        toastType: 'error'
      });
    }

    const credits = creditsData?.credits ?? 0;

    // Mettre en cache
    await redis.setex(cacheKey, CACHE_DURATION, credits.toString());

    return res.status(200).json({
      success: true,
      credits,
      toastType: 'success'
    });
  } catch (error) {
    logger.error('Erreur lors de la récupération des crédits IA:', error);
    return res.status(500).json({
      success: false,
      message: 'Erreur lors de la récupération des crédits IA',
      toastType: 'error'
    });
  }
};

/**
 * Utilise un crédit IA
 */
export const useAiCredit = async (req: Request, res: Response) => {
  try {
    // Récupérer l'ID utilisateur depuis req.user.id ou req.user.userId
    const userId = req.user?.id || req.user?.userId;

    if (!userId) {
      return res.status(401).json({
        success: false,
        message: 'Utilisateur non authentifié',
        toastType: 'error'
      });
    }

    // Récupérer les crédits actuels
    const { data, error } = await supabase
      .from('user_ai_credits')
      .select('credits')
      .eq('user_id', userId)
      .single();

    if (error && error.code !== 'PGRST116') {
      logger.error('Erreur lors de la récupération des crédits IA:', error);
      return res.status(500).json({
        success: false,
        message: 'Erreur lors de la récupération des crédits IA',
        toastType: 'error'
      });
    }

    const currentCredits = data?.credits || 0;

    // Vérifier si l'utilisateur a suffisamment de crédits
    if (currentCredits < 1) {
      return res.status(400).json({
        success: false,
        message: 'Vous n\'avez pas assez de crédits IA, veuillez en acheter dans le menu "Intelligence Artificielle"',
        toastType: 'error'
      });
    }

    // Mettre à jour les crédits
    const newCredits = currentCredits - 1;

    if (data) {
      // Mettre à jour l'entrée existante
      const { error: updateError } = await supabase
        .from('user_ai_credits')
        .update({
          credits: newCredits,
          updated_at: new Date().toISOString()
        })
        .eq('user_id', userId);

      if (updateError) {
        logger.error('Erreur lors de la mise à jour des crédits IA:', updateError);
        return res.status(500).json({
          success: false,
          message: 'Erreur lors de la mise à jour des crédits IA',
          toastType: 'error'
        });
      }
    } else {
      // Créer une nouvelle entrée
      const { error: insertError } = await supabase
        .from('user_ai_credits')
        .insert({
          user_id: userId,
          credits: newCredits,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        });

      if (insertError) {
        logger.error('Erreur lors de la création des crédits IA:', insertError);
        return res.status(500).json({
          success: false,
          message: 'Erreur lors de la création des crédits IA',
          toastType: 'error'
        });
      }
    }

    // Mettre à jour le cache
    const cacheKey = `${CACHE_PREFIX}${userId}`;
    await redis.setex(cacheKey, CACHE_DURATION, newCredits.toString());

    // Enregistrer l'activité
    await logUserActivity(
      userId,
      'ai_credit_used',
      undefined,
      'ai_credits',
      JSON.stringify({ credits_remaining: newCredits }),
      getIpFromRequest(req)
    );

    // Enregistrer dans l'historique des crédits IA
    await logAiCreditsOperation(
      userId,
      'utilisation',
      1, // Montant (1 crédit utilisé)
      currentCredits,
      newCredits,
      'Utilisation d\'un crédit IA',
      undefined,
      getIpFromRequest(req)
    );

    return res.status(200).json({
      success: true,
      credits: newCredits,
      message: 'Crédit IA utilisé avec succès',
      toastType: 'success'
    });
  } catch (error) {
    logger.error('Erreur lors de l\'utilisation d\'un crédit IA:', error);
    return res.status(500).json({
      success: false,
      message: 'Erreur lors de l\'utilisation d\'un crédit IA',
      toastType: 'error'
    });
  }
};

/**
 * Achète un ou plusieurs packs de crédits IA
 */
export const buyAiCredits = async (req: Request, res: Response) => {
  try {
    // Récupérer l'ID utilisateur depuis req.user.id ou req.user.userId
    const userId = req.user?.id || req.user?.userId;

    if (!userId) {
      return res.status(401).json({
        success: false,
        message: 'Utilisateur non authentifié',
        toastType: 'error'
      });
    }

    // Récupérer le nombre de packs à acheter depuis la requête
    const packQuantity = Math.min(Math.max(parseInt(req.body.packQuantity) || 1, 1), 50);

    // Récupérer le solde Jobi de l'utilisateur
    const { data: jobiData, error: jobiError } = await supabase
      .from('user_jobi')
      .select('montant')
      .eq('user_id', userId)
      .single();

    if (jobiError) {
      logger.error('Erreur lors de la récupération du solde Jobi:', jobiError);
      return res.status(500).json({
        success: false,
        message: 'Erreur lors de la récupération du solde Jobi',
        toastType: 'error'
      });
    }

    const jobiBalance = jobiData?.montant || 0;

    // Récupérer les valeurs de configuration pour les crédits IA
    const pricePerPack = subscriptions.gratuit.aiCredits.additionalCostJobi;
    const creditsPerPack = subscriptions.gratuit.aiCredits.packs;

    // Calculer le coût total et les crédits totaux
    const totalPrice = pricePerPack * packQuantity;
    const totalCredits = creditsPerPack * packQuantity;

    // Vérifier si l'utilisateur a suffisamment de Jobi
    if (jobiBalance < totalPrice) {
      return res.status(400).json({
        success: false,
        message: `Vous n'avez pas assez de Jobi. Il vous faut ${totalPrice} Jobi pour acheter ${packQuantity} pack${packQuantity > 1 ? 's' : ''} de ${creditsPerPack} crédits IA.`,
        toastType: 'error'
      });
    }

    // Récupérer les crédits actuels
    const { data: creditsData, error: creditsError } = await supabase
      .from('user_ai_credits')
      .select('credits')
      .eq('user_id', userId)
      .single();

    if (creditsError && creditsError.code !== 'PGRST116') {
      logger.error('Erreur lors de la récupération des crédits IA:', creditsError);
      return res.status(500).json({
        success: false,
        message: 'Erreur lors de la récupération des crédits IA',
        toastType: 'error'
      });
    }

    const currentCredits = creditsData?.credits || 0;
    const newCredits = currentCredits + totalCredits;

    // Vérifier que le total des crédits ne dépasse pas le plafond
    if (newCredits > CREDIT_MAX) {
      return res.status(400).json({
        success: false,
        message: `Vous ne pouvez pas avoir plus de ${CREDIT_MAX} crédits IA. Vous avez actuellement ${currentCredits} crédits et essayez d'en acheter ${totalCredits}.`,
        toastType: 'error'
      });
    }

    // Déduire le coût en Jobi
    const newJobiBalance = jobiBalance - totalPrice;
    const { error: updateJobiError } = await supabase
      .from('user_jobi')
      .update({ montant: newJobiBalance })
      .eq('user_id', userId);

    if (updateJobiError) {
      logger.error('Erreur lors de la mise à jour du solde Jobi:', updateJobiError);
      return res.status(500).json({
        success: false,
        message: 'Erreur lors de la mise à jour du solde Jobi',
        toastType: 'error'
      });
    }

    // Ajouter l'historique de transaction Jobi
    const { error: historyError } = await supabase
      .from('user_jobi_historique')
      .insert({
        user_id: userId,
        titre: 'Achat de crédits IA',
        description: `Achat de ${packQuantity} pack${packQuantity > 1 ? 's' : ''} de ${creditsPerPack} crédits IA`,
        montant: -totalPrice,
        date_creation: new Date().toISOString()
      });

    if (historyError) {
      logger.error('Erreur lors de l\'ajout de l\'historique Jobi:', historyError);
      // Continuer malgré l'erreur
    }

    // Mettre à jour les crédits IA
    if (creditsData) {
      // Mettre à jour l'entrée existante
      const { error: updateError } = await supabase
        .from('user_ai_credits')
        .update({
          credits: newCredits,
          updated_at: new Date().toISOString()
        })
        .eq('user_id', userId);

      if (updateError) {
        logger.error('Erreur lors de la mise à jour des crédits IA:', updateError);
        return res.status(500).json({
          success: false,
          message: 'Erreur lors de la mise à jour des crédits IA',
          toastType: 'error'
        });
      }
    } else {
      // Créer une nouvelle entrée
      const { error: insertError } = await supabase
        .from('user_ai_credits')
        .insert({
          user_id: userId,
          credits: newCredits,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        });

      if (insertError) {
        logger.error('Erreur lors de la création des crédits IA:', insertError);
        return res.status(500).json({
          success: false,
          message: 'Erreur lors de la création des crédits IA',
          toastType: 'error'
        });
      }
    }

    // Mettre à jour les caches
    const creditsCacheKey = `${CACHE_PREFIX}${userId}`;
    await redis.setex(creditsCacheKey, CACHE_DURATION, newCredits.toString());

    const jobiCacheKey = `jobi_balance_${userId}`;
    await redis.del(jobiCacheKey);

    // Enregistrer l'activité
    await logUserActivity(
      userId,
      'ai_credits_purchased',
      undefined,
      'ai_credits',
      JSON.stringify({
        packs_purchased: packQuantity,
        credits_per_pack: creditsPerPack,
        total_credits_purchased: totalCredits,
        jobi_spent: totalPrice,
        new_credits_balance: newCredits,
        new_jobi_balance: newJobiBalance
      }),
      getIpFromRequest(req)
    );

    // Enregistrer dans l'historique des crédits IA
    await logAiCreditsOperation(
      userId,
      'achat_jobi',
      totalCredits,
      currentCredits,
      newCredits,
      `Achat de ${packQuantity} pack${packQuantity > 1 ? 's' : ''} de ${creditsPerPack} crédits IA pour ${totalPrice} Jobi`,
      `jobi_${new Date().getTime()}`,
      getIpFromRequest(req)
    );

    // Envoyer un email de confirmation
    try {
      const { data: userData, error: userError } = await supabase
        .from('users')
        .select('email')
        .eq('id', userId)
        .single();

      if (!userError && userData) {
        // Déchiffrer les données utilisateur pour obtenir l'email
        const decryptedUserData = await decryptUserDataAsync(userData);
        await sendAiCreditsEmail(
          decryptedUserData.email,
          totalCredits,
          totalPrice,
          currentCredits,
          newCredits,
          false
        );
      }
    } catch (emailError) {
      logger.error('Erreur lors de l\'envoi de l\'email de confirmation:', emailError);
      // Continuer malgré l'erreur
    }

    // Créer une notification utilisateur
    try {
      await supabase.from('user_notifications').insert({
        user_id: userId,
        type: 'system',
        title: 'Achat de Crédits IA réussi',
        content: `Vous avez acheté ${totalCredits} crédits IA pour ${totalPrice} Jobi. Votre nouveau solde est de ${newCredits} crédits.`,
        link: '/dashboard/ai-credits',
        is_read: false,
        is_archived: false
      });

      // Invalider le cache Redis pour le compteur de notifications et les notifications non lues
      const countCacheKey = `${NOTIFICATIONS_CACHE_PREFIX}count:${userId}`;
      const unreadCacheKey = `${NOTIFICATIONS_CACHE_PREFIX}unread:${userId}`;
      await redis.del(countCacheKey);
      await redis.del(unreadCacheKey);
      // Invalider potentiellement le cache des listes de notifications (si utilisé)
      const listCacheKeys = await redis.keys(`${NOTIFICATIONS_CACHE_PREFIX}${userId}:*`);
      if (listCacheKeys.length > 0) {
        await Promise.all(listCacheKeys.map(key => redis.del(key)));
      }
      logger.info(`Notification d'achat de crédits IA (Jobi) créée pour l'utilisateur ${userId}`);

    } catch (notifError) {
      logger.error('Erreur lors de la création de la notification utilisateur:', notifError);
      // Continuer même si la notification échoue
    }

    return res.status(200).json({
      success: true,
      credits: newCredits,
      jobiBalance: newJobiBalance,
      message: `Vous avez acheté ${packQuantity} pack${packQuantity > 1 ? 's' : ''} de ${creditsPerPack} crédits IA pour ${totalPrice} Jobi`,
      toastType: 'success'
    });
  } catch (error) {
    logger.error('Erreur lors de l\'achat de crédits IA:', error);
    return res.status(500).json({
      success: false,
      message: 'Erreur lors de l\'achat de crédits IA',
      toastType: 'error'
    });
  }
};

/**
 * Ajoute des crédits IA gratuits à un utilisateur (lors de l'abonnement)
 */
export const addFreeAiCredits = async (userId: string, credits: number): Promise<boolean> => {
  try {
    // Récupérer les crédits actuels
    const { data, error } = await supabase
      .from('user_ai_credits')
      .select('credits')
      .eq('user_id', userId)
      .single();

    if (error && error.code !== 'PGRST116') {
      logger.error('Erreur lors de la récupération des crédits IA:', error);
      return false;
    }

    // Si l'utilisateur a déjà plus ou autant de crédits que ce que son abonnement lui permet, ne pas ajouter de crédits
    if (data && data.credits >= credits) {
      logger.info(`L'utilisateur ${userId} a déjà ${data.credits} crédits IA (>= ${credits} crédits de son abonnement), pas d'ajout de crédits gratuits`);
      return true;
    }

    const currentCredits = data?.credits || 0;
    // Si l'utilisateur a déjà des crédits mais moins que ce que son abonnement permet,
    // on complète jusqu'au montant défini par l'abonnement au lieu d'ajouter le montant complet
    const newCredits = Math.max(credits, currentCredits);

    if (data) {
      // Mettre à jour l'entrée existante
      const { error: updateError } = await supabase
        .from('user_ai_credits')
        .update({
          credits: newCredits,
          updated_at: new Date().toISOString()
        })
        .eq('user_id', userId);

      if (updateError) {
        logger.error('Erreur lors de la mise à jour des crédits IA gratuits:', updateError);
        return false;
      }
    } else {
      // Créer une nouvelle entrée
      const { error: insertError } = await supabase
        .from('user_ai_credits')
        .insert({
          user_id: userId,
          credits: newCredits,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        });

      if (insertError) {
        logger.error('Erreur lors de la création des crédits IA gratuits:', insertError);
        return false;
      }
    }

    // Mettre à jour le cache
    const cacheKey = `${CACHE_PREFIX}${userId}`;
    await redis.setex(cacheKey, CACHE_DURATION, newCredits.toString());

    // Calculer le nombre réel de crédits ajoutés
    const creditsAdded = newCredits - currentCredits;

    // Enregistrer dans l'historique des crédits IA seulement si des crédits ont été ajoutés
    if (creditsAdded > 0) {
      await logAiCreditsOperation(
        userId,
        'offert_abonnement',
        creditsAdded,
        currentCredits,
        newCredits,
        `${creditsAdded} crédits IA gratuits ajoutés avec l'abonnement (remise à ${credits})`,
        undefined,
        'system'
      );

      logger.info(`${creditsAdded} crédits IA gratuits ajoutés pour l'utilisateur ${userId} (plan premium)`);
    } else {
      logger.info(`Aucun crédit IA ajouté pour l'utilisateur ${userId}, déjà au niveau de l'abonnement (${credits} crédits)`);
    }
    return true;
  } catch (error) {
    logger.error('Erreur lors de l\'ajout de crédits IA gratuits:', error);
    return false;
  }
};

/**
 * Vérifie si un utilisateur a suffisamment de crédits IA
 */
export const checkAiCredits = async (userId: string): Promise<{ hasCredits: boolean, credits: number }> => {
  try {
    // Vérifier si les crédits sont en cache
    const cacheKey = `${CACHE_PREFIX}${userId}`;
    const cachedCredits = await redis.get(cacheKey);

    if (cachedCredits) {
      const credits = parseInt(cachedCredits);
      return { hasCredits: credits > 0, credits };
    }

    // Récupérer les crédits depuis la base de données
    const { data, error } = await supabase
      .from('user_ai_credits')
      .select('credits')
      .eq('user_id', userId)
      .single();

    if (error && error.code !== 'PGRST116') {
      logger.error('Erreur lors de la vérification des crédits IA:', error);
      return { hasCredits: false, credits: 0 };
    }

    const credits = data?.credits || 0;

    // Mettre en cache
    await redis.setex(cacheKey, CACHE_DURATION, credits.toString());

    return { hasCredits: credits > 0, credits };
  } catch (error) {
    logger.error('Erreur lors de la vérification des crédits IA:', error);
    return { hasCredits: false, credits: 0 };
  }
};

/**
 * Crée une session de paiement Stripe pour l'achat de crédits IA
 */
export const createStripeCheckoutSession = async (req: Request, res: Response) => {
  try {
    // Récupérer l'ID utilisateur depuis req.user.id ou req.user.userId
    const userId = req.user?.id || req.user?.userId;

    if (!userId) {
      return res.status(401).json({
        success: false,
        message: 'Utilisateur non authentifié',
        toastType: 'error'
      });
    }

    // Récupérer les informations de l'utilisateur
    const { data: userData, error: userError } = await supabase
      .from('users')
      .select('email')
      .eq('id', userId)
      .single();

    if (userError || !userData) {
      logger.error('Erreur lors de la récupération des informations utilisateur:', userError);
      return res.status(500).json({
        success: false,
        message: 'Erreur lors de la récupération des informations utilisateur',
        toastType: 'error'
      });
    }

    // Déchiffrer les données utilisateur
    const decryptedUserData = await decryptUserDataAsync(userData);

    // Récupérer les valeurs de configuration pour les crédits IA
    const pricePerPack = subscriptions.gratuit.aiCredits.additionalCost; // Prix en euros
    const creditsPerPack = subscriptions.gratuit.aiCredits.packs;

    // Récupérer le nombre de packs à acheter depuis la requête
    const packQuantity = Math.min(Math.max(parseInt(req.body.packQuantity) || 1, 1), 50);
    const totalCredits = creditsPerPack * packQuantity;

    // Vérifier si l'utilisateur atteindrait le plafond de crédits

    // Récupérer les crédits actuels de l'utilisateur
    const { data: creditsData, error: creditsError } = await supabase
      .from('user_ai_credits')
      .select('credits')
      .eq('user_id', userId)
      .single();

    if (creditsError && creditsError.code !== 'PGRST116') {
      logger.error('Erreur lors de la récupération des crédits IA:', creditsError);
      return res.status(500).json({
        success: false,
        message: 'Erreur lors de la récupération des crédits IA',
        toastType: 'error'
      });
    }

    const currentCredits = creditsData?.credits || 0;
    const newCredits = currentCredits + totalCredits;

    if (newCredits > CREDIT_MAX) {
      return res.status(400).json({
        success: false,
        message: `Vous ne pouvez pas avoir plus de ${CREDIT_MAX} crédits IA. Vous avez actuellement ${currentCredits} crédits et essayez d'en acheter ${totalCredits}.`,
        toastType: 'error'
      });
    }

    // Créer une session de paiement Stripe
    const session = await stripe.checkout.sessions.create({
      payment_method_types: ['card'],
      line_items: [
        {
          price_data: {
            currency: 'eur',
            product_data: {
              name: `${totalCredits} crédits IA`,
              description: `Achat de ${packQuantity} pack${packQuantity > 1 ? 's' : ''} de ${creditsPerPack} crédits IA pour utiliser l'intelligence artificielle sur JobPartiel`,
              images: ['https://jobpartiel.fr/assets/images/logo.png']
            },
            unit_amount: pricePerPack * 100 * packQuantity, // Prix en centimes pour tous les packs
          },
          quantity: 1,
        },
      ],
      mode: 'payment',
      success_url: `${process.env.FRONTEND_URL}/dashboard/ai-credits?success=true`,
      cancel_url: `${process.env.FRONTEND_URL}/dashboard/ai-credits?canceled=true`,
      customer_email: decryptedUserData.email,
      metadata: {
        userId,
        creditsAmount: totalCredits,
        packQuantity: packQuantity,
        creditsPerPack: creditsPerPack,
        totalPrice: pricePerPack * packQuantity,
        type: 'ai_credits'
      },
    });

    // Enregistrer l'activité
    await logUserActivity(
      userId,
      'ai_credits_checkout_created',
      undefined,
      'ai_credits',
      JSON.stringify({
        session_id: session.id,
        credits_to_purchase: totalCredits,
        pack_quantity: packQuantity,
        credits_per_pack: creditsPerPack,
        total_price: pricePerPack * packQuantity
      }),
      getIpFromRequest(req)
    );

    return res.status(200).json({
      success: true,
      sessionId: session.id,
      url: session.url
    });
  } catch (error) {
    logger.error('Erreur lors de la création de la session de paiement Stripe:', error);
    return res.status(500).json({
      success: false,
      message: 'Erreur lors de la création de la session de paiement',
      toastType: 'error'
    });
  }
};

/**
 * Webhook Stripe pour traiter les paiements réussis
 */
export const handleStripeWebhook = async (req: Request, res: Response) => {
  console.log('2 Stripe webhook reçu');
  const sig = req.headers['stripe-signature'] as string;
  const endpointSecret = process.env.STRIPE_WEBHOOK_SECRET;

  if (!endpointSecret) {
    logger.error('STRIPE_WEBHOOK_SECRET non défini');
    return res.status(500).json({ success: false, message: 'Configuration incorrecte' });
  }

  let event;

  try {
    event = stripe.webhooks.constructEvent(req.body, sig, endpointSecret);
  } catch (err: any) {
    logger.error(`Erreur de signature Stripe: ${err.message}`);
    return res.status(400).json({ success: false, message: `Signature webhook: ${err.message}` });
  }

  // Traiter l'événement
  if (event.type === 'checkout.session.completed') {
    const session = event.data.object as any;

    // Vérifier que c'est bien un achat de crédits IA
    if (session.metadata?.type === 'ai_credits') {
      const userId = session.metadata.userId;
      // Récupérer le nombre de crédits depuis les métadonnées
      let credits = parseInt(session.metadata.creditsAmount) || 0;

      // Si les crédits ne sont pas définis dans les métadonnées, utiliser la configuration
      if (credits === 0) {
        const creditsPerPack = subscriptions.gratuit.aiCredits.packs;
        credits = creditsPerPack;
      }

      const userEmail = session.customer_email;

      // Récupérer les crédits IA actuels
      const { data: aiCreditsData, error: aiCreditsError } = await supabase
        .from('user_ai_credits')
        .select('credits')
        .eq('user_id', userId)
        .single();

      let currentCredits = 0;

      if (aiCreditsError && aiCreditsError.code === 'PGRST116') {
        // Si l'utilisateur n'a pas encore de crédits, créer une entrée
        try {
          // Vérifier d'abord si l'entrée existe déjà (pour éviter les erreurs de duplication)
          const { data: existingData, error: existingError } = await supabase
            .from('user_ai_credits')
            .select('credits')
            .eq('user_id', userId);

          if (existingError) {
            logger.error('Erreur lors de la vérification des crédits IA existants:', existingError);
          }

          if (existingData && existingData.length > 0) {
            // L'entrée existe déjà, mettre à jour
            const { data: updatedData, error: updateError } = await supabase
              .from('user_ai_credits')
              .update({
                credits: existingData[0].credits + credits,
                updated_at: new Date().toISOString()
              })
              .eq('user_id', userId)
              .select('credits')
              .single();

            if (updateError) {
              logger.error('Erreur lors de la mise à jour des crédits IA:', updateError);
              return res.status(500).json({
                success: false,
                message: 'Erreur lors de la mise à jour des crédits IA'
              });
            }

            currentCredits = updatedData.credits;
          } else {
            // L'entrée n'existe pas, créer une nouvelle
            const { data: newData, error: newError } = await supabase
              .from('user_ai_credits')
              .insert({
                user_id: userId,
                credits,
                created_at: new Date().toISOString(),
                updated_at: new Date().toISOString()
              })
              .select('credits')
              .single();

            if (newError) {
              logger.error('Erreur lors de la création des crédits IA:', newError);
              return res.status(500).json({
                success: false,
                message: 'Erreur lors de la création des crédits IA'
              });
            }

            currentCredits = newData.credits;
          }
        } catch (error) {
          logger.error('Exception lors de la gestion des crédits IA:', error);
          return res.status(500).json({
            success: false,
            message: 'Erreur lors de la gestion des crédits IA'
          });
        }
      } else if (aiCreditsError) {
        logger.error('Erreur lors de la récupération des crédits IA:', aiCreditsError);
        return res.status(500).json({
          success: false,
          message: 'Erreur lors de la récupération des crédits IA'
        });
      } else {
        // Mettre à jour les crédits IA
        const { data: updatedData, error: updateError } = await supabase
          .from('user_ai_credits')
          .update({
            credits: aiCreditsData.credits + credits,
            updated_at: new Date().toISOString()
          })
          .eq('user_id', userId)
          .select('credits')
          .single();

        if (updateError) {
          logger.error('Erreur lors de la mise à jour des crédits IA:', updateError);
          return res.status(500).json({
            success: false,
            message: 'Erreur lors de la mise à jour des crédits IA'
          });
        }

        currentCredits = updatedData.credits;
      }

      // Mettre à jour le cache des crédits IA
      const aiCreditsCacheKey = `${CACHE_PREFIX}${userId}`;
      await redis.setex(aiCreditsCacheKey, CACHE_DURATION, currentCredits.toString());

      // Récupérer les informations sur les packs depuis les métadonnées
      const packQuantity = parseInt(session.metadata.packQuantity) || 1;
      const creditsPerPack = parseInt(session.metadata.creditsPerPack) || credits;

      // Récupérer le prix total depuis les métadonnées
      let totalPrice = parseInt(session.metadata.totalPrice) || 0;

      // Si le prix total n'est pas défini dans les métadonnées, le calculer
      if (totalPrice === 0) {
        const pricePerPack = subscriptions.gratuit.aiCredits.additionalCost;
        totalPrice = pricePerPack * packQuantity;
      }

      // Enregistrer l'activité
      await logUserActivity(
        userId,
        'ai_credits_purchased_stripe',
        undefined,
        'ai_credits',
        JSON.stringify({
          credits_purchased: credits,
          pack_quantity: packQuantity,
          credits_per_pack: creditsPerPack,
          new_credits_balance: currentCredits,
          payment_id: session.id
        }),
        'stripe_webhook'
      );

      // Enregistrer dans l'historique des crédits IA
      const soldeAvant = currentCredits - credits;
      await logAiCreditsOperation(
        userId,
        'achat_stripe',
        credits,
        soldeAvant,
        currentCredits,
        `Achat de ${packQuantity > 1 ? `${packQuantity} packs de ${creditsPerPack}` : `${credits}`} crédits IA via Stripe pour ${totalPrice}€`,
        session.id,
        'stripe_webhook'
      );

      // Envoyer un email de confirmation
      if (userEmail) {
        try {
          // Récupérer le prix total depuis les métadonnées
          let totalPrice = parseInt(session.metadata.totalPrice) || 0;
          const packQuantity = parseInt(session.metadata.packQuantity) || 1;

          // Si le prix total n'est pas défini dans les métadonnées, le calculer
          if (totalPrice === 0) {
            const pricePerPack = subscriptions.gratuit.aiCredits.additionalCost;
            totalPrice = pricePerPack * packQuantity;
          }

          await sendAiCreditsEmail(userEmail, credits, totalPrice, currentCredits - credits, currentCredits, true);
        } catch (emailError) {
          logger.error('Erreur lors de l\'envoi de l\'email de confirmation:', emailError);
          // Ne pas bloquer le processus si l'email échoue
        }
      }

      // Créer une notification utilisateur
      try {
        await supabase.from('user_notifications').insert({
          user_id: userId,
          type: 'system',
          title: 'Achat de Crédits IA réussi',
          content: `Vous avez acheté ${credits} crédits IA pour ${totalPrice} €. Votre nouveau solde est de ${currentCredits} crédits.`,
          link: '/dashboard/ai-credits',
          is_read: false,
          is_archived: false
        });

        // Invalider le cache Redis pour le compteur de notifications et les notifications non lues
        const countCacheKey = `${NOTIFICATIONS_CACHE_PREFIX}count:${userId}`;
        const unreadCacheKey = `${NOTIFICATIONS_CACHE_PREFIX}unread:${userId}`;
        await redis.del(countCacheKey);
        await redis.del(unreadCacheKey);
        // Invalider potentiellement le cache des listes de notifications (si utilisé)
        const listCacheKeys = await redis.keys(`${NOTIFICATIONS_CACHE_PREFIX}${userId}:*`);
        if (listCacheKeys.length > 0) {
          await Promise.all(listCacheKeys.map(key => redis.del(key)));
        }
        logger.info(`Notification d'achat de crédits IA (Stripe) créée pour l'utilisateur ${userId}`);

      } catch (notifError) {
        logger.error('Erreur lors de la création de la notification utilisateur (Stripe):', notifError);
        // Continuer même si la notification échoue
      }
    }
  }

  return res.status(200).json({ received: true });
};

/**
 * Récupère l'historique des crédits IA d'un utilisateur
 */
export const getAiCreditsHistory = async (req: Request, res: Response) => {
  try {
    // Récupérer l'ID utilisateur depuis req.user.id ou req.user.userId
    const userId = req.user?.id || req.user?.userId;

    if (!userId) {
      return res.status(401).json({
        success: false,
        message: 'Utilisateur non authentifié',
        toastType: 'error'
      });
    }

    // Récupérer les paramètres de pagination
    const page = parseInt(req.query.page as string) || 1;
    const limit = parseInt(req.query.limit as string) || 10;
    const offset = (page - 1) * limit;

    // Récupérer l'historique des crédits IA
    const { data, error, count } = await supabase
      .from('user_ai_credits_historique')
      .select('*', { count: 'exact' })
      .eq('user_id', userId)
      .order('created_at', { ascending: false })
      .range(offset, offset + limit - 1);

    if (error) {
      logger.error('Erreur lors de la récupération de l\'historique des crédits IA:', error);
      return res.status(500).json({
        success: false,
        message: 'Erreur lors de la récupération de l\'historique des crédits IA',
        toastType: 'error'
      });
    }

    // Calculer le nombre total de pages
    const totalPages = count ? Math.ceil(count / limit) : 0;

    return res.status(200).json({
      success: true,
      history: data,
      pagination: {
        page,
        limit,
        totalItems: count,
        totalPages
      }
    });
  } catch (error) {
    logger.error('Erreur lors de la récupération de l\'historique des crédits IA:', error);
    return res.status(500).json({
      success: false,
      message: 'Erreur lors de la récupération de l\'historique des crédits IA',
      toastType: 'error'
    });
  }
};
