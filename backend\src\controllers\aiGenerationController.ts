import { Request, Response } from 'express';
import axios from 'axios';
import logger from '../utils/logger';
import { redis } from '../config/redis';
import { supabase } from '../config/supabase';
import { getIpFromRequest } from '../utils/activityLogger';
import { logUserActivity } from '../utils/activityLogger';
import { logOpenRouterUsage } from '../services/contentModerationService';
import { hasUserConsented } from './aiConsent';
import { selectAIModel } from './openRouterController';
import { decryptProfilDataAsync, decryptUserDataAsync } from '../utils/encryption';

// URL et clé API OpenRouter
const AI_API_URL = process.env.MODERATION_API_URL || 'https://api.openrouter.ai/api/v1/chat/completions';
const AI_API_KEY = process.env.MODERATION_API_KEY || '';
const AI_API_MODEL_FREE = process.env.MODERATION_API_MODEL_FREE || 'meta-llama/llama-3.3-70b-instruct:free';
const AI_API_MODEL_PAYANT = process.env.MODERATION_API_MODEL_PAYANT || 'google/gemini-2.5-flash-lite';
const DAILY_CALLS_LIMIT = 999;

// Constantes pour le rate limiting du modèle gratuit
const RATE_API_LIMIT_GRATUITE = 10; // 10 requêtes par seconde pour les générations gratuites, au-delà on passe au payant

// Constantes pour le cache Redis
const CACHE_PREFIX = 'ai_generation:';
const USER_PROMPTS_CACHE_PREFIX = `${CACHE_PREFIX}user_prompts:`;
const USER_CREDITS_CACHE_PREFIX = `ai_credits:`; // Format existant pour la compatibilité
const CACHE_TTL_SHORT = 60 * 5; // 5 minutes
const CACHE_TTL_MEDIUM = 60 * 30; // 30 minutes
const CACHE_TTL_LONG = 60 * 60 * 24; // 24 heures

/**
 * Fonction utilitaire pour restituer les crédits IA en cas d'erreur
 */
const restoreAiCredits = async (userId: string, originalCredits: number, creditsCost: number = 1): Promise<void> => {
  try {
    const creditsCacheKey = `${USER_CREDITS_CACHE_PREFIX}${userId}`;

    // Restaurer les crédits dans la base de données
    const { error: restoreError } = await supabase
      .from('user_ai_credits')
      .update({ credits: originalCredits })
      .eq('user_id', userId);

    if (restoreError) {
      logger.error('Erreur lors de la restitution des crédits IA:', restoreError);
    } else {
      logger.info(`Crédits IA restitués pour l'utilisateur ${userId}: ${originalCredits}`);
    }

    // Restaurer le cache
    await redis.setex(creditsCacheKey, CACHE_TTL_SHORT, originalCredits.toString());

    // Enregistrer la restitution dans l'historique
    try {
      const { logAiCreditsOperation } = require('./aiCreditsController');
      await logAiCreditsOperation(
        userId,
        'autre',
        creditsCost,
        originalCredits - creditsCost,
        originalCredits,
        `Restitution suite à erreur de génération de contenu IA`,
        undefined,
        'system',
        undefined
      );
    } catch (historyError) {
      logger.error("Erreur lors de l'enregistrement de la restitution dans l'historique:", historyError);
    }
  } catch (error) {
    logger.error('Erreur critique lors de la restitution des crédits IA:', error);
  }
};

/**
 * Valide une entrée dans le cache Redis
 * @param key Clé du cache
 * @param data Données à mettre en cache
 * @param ttl Durée de vie en secondes (TTL)
 */
async function validateCache(key: string, data: any, ttl: number = CACHE_TTL_MEDIUM): Promise<void> {
  try {
    await redis.setex(key, ttl, typeof data === 'string' ? data : JSON.stringify(data));
    logger.info(`Cache validé pour la clé: ${key}`);
  } catch (error) {
    logger.error('Erreur lors de la validation du cache:', error);
  }
}

/**
 * Invalide une entrée dans le cache Redis
 * @param key Clé du cache à invalider
 */
async function invalidateCache(key: string): Promise<void> {
  try {
    await redis.del(key);
    logger.info(`Cache invalidé pour la clé: ${key}`);
  } catch (error) {
    logger.error('Erreur lors de l\'invalidation du cache:', error);
  }
}

/**
 * Récupère une entrée du cache Redis
 * @param key Clé du cache
 * @returns Données du cache ou null si non trouvé
 */
async function getFromCache<T>(key: string): Promise<T | null> {
  try {
    const cachedData = await redis.get(key);
    if (!cachedData) return null;

    try {
      return JSON.parse(cachedData) as T;
    } catch {
      return cachedData as unknown as T;
    }
  } catch (error) {
    logger.error('Erreur lors de la récupération du cache:', error);
    return null;
  }
}

// Timeout pour les appels API
const TIMEOUT_API = 15000;

// Types de génération supportés
export type GenerationType = 'biography' | 'service_description' | 'mission_post' | 'review_response' | 'mission_offer' | 'default_prompt' | 'comment' | 'slogan' | 'support_user_assistance' | 'support_staff_assistance' | 'support_comment';

// Interface pour les prompts personnalisés
interface CustomPrompt {
  id: string;
  user_id: string;
  type: GenerationType;
  prompt: string;
  created_at: string;
  updated_at: string;
}

/**
 * Récupère les prompts personnalisés d'un utilisateur
 */
export const getUserPrompts = async (req: Request, res: Response) => {
  try {
    // Récupérer l'ID utilisateur depuis req.user
    const userId = req.user?.id || req.user?.userId;

    if (!userId) {
      return res.status(401).json({
        success: false,
        message: 'Utilisateur non authentifié',
        toastType: 'error'
      });
    }

    // Vérifier si les prompts sont en cache
    const cacheKey = `${USER_PROMPTS_CACHE_PREFIX}${userId}`;
    const cachedPrompts = await getFromCache<any[]>(cacheKey);

    if (cachedPrompts) {
      logger.info('Prompts récupérés depuis le cache Redis pour l\'utilisateur:', userId);
      return res.status(200).json({
        success: true,
        prompts: cachedPrompts,
        fromCache: true,
        toastType: 'success'
      });
    }

    // Récupérer les prompts personnalisés depuis Supabase
    const { data: prompts, error } = await supabase
      .from('user_ai_prompts')
      .select('*')
      .eq('user_id', userId);

    if (error) {
      logger.error('Erreur lors de la récupération des prompts personnalisés:', error);
      return res.status(500).json({
        success: false,
        message: 'Erreur lors de la récupération des prompts personnalisés',
        toastType: 'error'
      });
    }

    // Mettre en cache les prompts
    await validateCache(cacheKey, prompts || [], CACHE_TTL_MEDIUM);

    // Retourner les prompts
    return res.status(200).json({
      success: true,
      prompts: prompts || [],
      toastType: 'success'
    });
  } catch (error: any) {
    logger.error('Erreur lors de la récupération des prompts personnalisés:', error);
    return res.status(500).json({
      success: false,
      message: 'Erreur lors de la récupération des prompts personnalisés',
      toastType: 'error'
    });
  }
};

/**
 * Enregistre ou met à jour un prompt personnalisé
 */
export const saveUserPrompt = async (req: Request, res: Response) => {
  try {
    // Récupérer l'ID utilisateur depuis req.user
    const userId = req.user?.id || req.user?.userId;

    if (!userId) {
      return res.status(401).json({
        success: false,
        message: 'Utilisateur non authentifié',
        toastType: 'error'
      });
    }

    // Valider les données de la requête
    const { type, prompt } = req.body;

    if (!type || !prompt || typeof prompt !== 'string') {
      return res.status(400).json({
        success: false,
        message: 'Données invalides',
        toastType: 'error'
      });
    }

    // Vérifier si le prompt existe déjà
    const { data: existingPrompt, error: fetchError } = await supabase
      .from('user_ai_prompts')
      .select('*')
      .eq('user_id', userId)
      .eq('type', type)
      .single();

    if (fetchError && fetchError.code !== 'PGRST116') { // PGRST116 = not found
      logger.error('Erreur lors de la vérification du prompt existant:', fetchError);
      return res.status(500).json({
        success: false,
        message: 'Erreur lors de la vérification du prompt existant',
        toastType: 'error'
      });
    }

    let result;
    const now = new Date().toISOString();

    if (existingPrompt) {
      // Mettre à jour le prompt existant
      result = await supabase
        .from('user_ai_prompts')
        .update({
          prompt,
          updated_at: now
        })
        .eq('id', existingPrompt.id)
        .eq('user_id', userId);
    } else {
      // Créer un nouveau prompt
      result = await supabase
        .from('user_ai_prompts')
        .insert({
          user_id: userId,
          type,
          prompt,
          created_at: now,
          updated_at: now
        });
    }

    if (result.error) {
      logger.error('Erreur lors de l\'enregistrement du prompt personnalisé:', result.error);
      return res.status(500).json({
        success: false,
        message: 'Erreur lors de l\'enregistrement du prompt personnalisé',
        toastType: 'error'
      });
    }

    // Invalider le cache des prompts de l'utilisateur
    const cacheKey = `${USER_PROMPTS_CACHE_PREFIX}${userId}`;
    await invalidateCache(cacheKey);
    logger.info(`Cache des prompts invalidé pour l'utilisateur ${userId} après modification`);

    // Enregistrer l'activité
    await logUserActivity(
      userId,
      'ai_prompt_saved',
      undefined,
      'ai_prompts',
      JSON.stringify({ type }),
      getIpFromRequest(req)
    );

    // Retourner le succès
    return res.status(200).json({
      success: true,
      message: 'Prompt personnalisé enregistré avec succès',
      toastType: 'success'
    });
  } catch (error: any) {
    logger.error('Erreur lors de l\'enregistrement du prompt personnalisé:', error);
    return res.status(500).json({
      success: false,
      message: 'Erreur lors de l\'enregistrement du prompt personnalisé',
      toastType: 'error'
    });
  }
};

/**
 * Supprime un prompt personnalisé
 */
export const deleteUserPrompt = async (req: Request, res: Response) => {
  try {
    // Récupérer l'ID utilisateur depuis req.user
    const userId = req.user?.id || req.user?.userId;

    if (!userId) {
      return res.status(401).json({
        success: false,
        message: 'Utilisateur non authentifié',
        toastType: 'error'
      });
    }

    // Récupérer le type de prompt à supprimer
    const { type } = req.params;

    if (!type) {
      return res.status(400).json({
        success: false,
        message: 'Type de prompt non spécifié',
        toastType: 'error'
      });
    }

    // Supprimer le prompt
    const { error } = await supabase
      .from('user_ai_prompts')
      .delete()
      .eq('user_id', userId)
      .eq('type', type);

    if (error) {
      logger.error('Erreur lors de la suppression du prompt personnalisé:', error);
      return res.status(500).json({
        success: false,
        message: 'Erreur lors de la suppression du prompt personnalisé',
        toastType: 'error'
      });
    }

    // Invalider le cache des prompts de l'utilisateur
    const cacheKey = `${USER_PROMPTS_CACHE_PREFIX}${userId}`;
    await invalidateCache(cacheKey);
    logger.info(`Cache des prompts invalidé pour l'utilisateur ${userId} après suppression`);

    // Enregistrer l'activité
    await logUserActivity(
      userId,
      'ai_prompt_deleted',
      undefined,
      'ai_prompts',
      JSON.stringify({ type }),
      getIpFromRequest(req)
    );

    return res.status(200).json({
      success: true,
      message: 'Prompt personnalisé supprimé avec succès',
      toastType: 'success'
    });
  } catch (error: any) {
    logger.error('Erreur lors de la suppression du prompt personnalisé:', error);
    return res.status(500).json({
      success: false,
      message: 'Erreur lors de la suppression du prompt personnalisé',
      toastType: 'error'
    });
  }
};

/**
 * Génère du contenu avec l'IA
 */
export const generateContent = async (req: Request, res: Response) => {
  // Déclarer les variables au niveau de la fonction pour qu'elles soient accessibles dans le catch
  let userId: string | undefined;
  let currentCredits: number | undefined;
  let creditsDebited = false;

  try {
    // Récupérer l'ID utilisateur depuis req.user
    userId = req.user?.id || req.user?.userId;

    if (!userId) {
      return res.status(401).json({
        success: false,
        message: 'Utilisateur non authentifié',
        toastType: 'error'
      });
    }

    // Vérifier si l'utilisateur a donné son consentement pour l'utilisation de l'IA
    const hasConsent = await hasUserConsented(userId);
    if (!hasConsent) {
      return res.status(403).json({
        success: false,
        message: 'Vous devez accepter les conditions d\'utilisation de l\'IA avant de pouvoir générer du contenu',
        requiresConsent: true,
        toastType: 'error'
      });
    }

    // Valider les données de la requête
    const { type, context, customPrompt } = req.body;

    if (!type || !context) {
      return res.status(400).json({
        success: false,
        message: 'Données invalides',
        toastType: 'error'
      });
    }

    logger.info('Génération IA : Début de génération pour l\'utilisateur:', userId);

    // Vérifier si l'utilisateur a des crédits IA (dans le cache)
    const creditsCacheKey = `${USER_CREDITS_CACHE_PREFIX}${userId}`;

    // Essayer de récupérer les crédits depuis le cache
    const cachedCredits = await redis.get(creditsCacheKey);

    if (cachedCredits !== null) {
      // Utiliser les crédits du cache
      currentCredits = parseInt(cachedCredits, 10);
      logger.info(`Crédits IA récupérés depuis le cache pour l'utilisateur ${userId}: ${currentCredits}`);
    } else {
      // Récupérer les crédits depuis la base de données
      const { data: creditsData, error: creditsError } = await supabase
        .from('user_ai_credits')
        .select('credits')
        .eq('user_id', userId)
        .single();

      if (creditsError && creditsError.code !== 'PGRST116') {
        logger.error('Erreur lors de la vérification des crédits IA:', creditsError);
        return res.status(500).json({
          success: false,
          message: 'Erreur lors de la vérification des crédits IA',
          toastType: 'error'
        });
      }

      currentCredits = creditsData?.credits || 0;

      // Mettre en cache les crédits
      await redis.setex(creditsCacheKey, CACHE_TTL_SHORT, currentCredits!.toString());
      logger.info(`Crédits IA mis en cache pour l'utilisateur ${userId}: ${currentCredits}`);
    }

    if (!currentCredits || currentCredits <= 0) {
      return res.status(400).json({
        success: false,
        message: 'Vous n\'avez pas assez de crédits IA. Veuillez en acheter dans le menu "Intelligence Artificielle"',
        toastType: 'error'
      });
    }

    // Déduire les crédits IA AVANT la génération pour éviter les abus
    const newCredits = currentCredits - 1;

    // Mettre à jour les crédits dans la base de données
    const { error: updateError } = await supabase
      .from('user_ai_credits')
      .update({
        credits: newCredits,
        updated_at: new Date().toISOString()
      })
      .eq('user_id', userId);

    if (updateError) {
      logger.error('Erreur lors de la mise à jour des crédits IA:', updateError);
      return res.status(500).json({
        success: false,
        message: 'Erreur lors de la déduction des crédits IA',
        toastType: 'error'
      });
    }

    // Mettre à jour le cache
    await redis.setex(creditsCacheKey, CACHE_TTL_SHORT, newCredits.toString());

    // Enregistrer immédiatement la déduction dans l'historique
    try {
      const { logAiCreditsOperation } = require('./aiCreditsController');
      await logAiCreditsOperation(
        userId,
        'utilisation',
        1,
        currentCredits,
        newCredits,
        undefined,
        undefined,
        getIpFromRequest(req),
        type
      );
    } catch (historyError) {
      logger.error("Erreur lors de l'enregistrement de la déduction dans l'historique:", historyError);
      // Continuer malgré l'erreur
    }

    // Marquer que les crédits ont été débités
    creditsDebited = true;

    // Récupérer le prompt personnalisé de l'utilisateur
    const { data: userPrompt, error: promptError } = await supabase
      .from('user_ai_prompts')
      .select('prompt')
      .eq('user_id', userId)
      .eq('type', type)
      .single();

    if (promptError && promptError.code !== 'PGRST116') {
      logger.error('Erreur lors de la récupération du prompt personnalisé:', promptError);
      return res.status(500).json({
        success: false,
        message: 'Erreur lors de la récupération du prompt personnalisé',
        toastType: 'error'
      });
    }

    // Récupérer les données de base de l'utilisateur
    const { data: userData, error: userError } = await supabase
      .from('users')
      .select('email, user_type')
      .eq('id', userId)
      .single();

    if (userError) {
      logger.error('Erreur lors de la récupération des données utilisateur:', userError);
      return res.status(500).json({
        success: false,
        message: 'Erreur lors de la récupération des données utilisateur',
        toastType: 'error'
      });
    }

    // Déchiffrer les données utilisateur
    const decryptedUserData = userData ? await decryptUserDataAsync(userData) : null;

    // Récupérer les données du profil détaillé
    const { data: userProfile, error: profileError } = await supabase
      .from('user_profil')
      .select('*')
      .eq('user_id', userId)
      .single();

    if (profileError && profileError.code !== 'PGRST116') {
      logger.error('Erreur lors de la récupération du profil utilisateur:', profileError);
      // Continuer même si le profil n'est pas trouvé
    }

    // Déchiffrer les données du profil
    const decryptedUserProfile = userProfile ? await decryptProfilDataAsync(userProfile) : null;

    // Récupérer les badges de l'utilisateur
    const { data: userBadges, error: badgesError } = await supabase
      .from('user_badges')
      .select('badge_id')
      .eq('user_id', userId);

    if (badgesError) {
      logger.error('Erreur lors de la récupération des badges utilisateur:', badgesError);
      // Continuer même si les badges ne sont pas trouvés
    }

    // Récupérer la note moyenne et le nombre d'avis
    const { data: reviewStats, error: reviewError } = await supabase
      .from('user_reviews')
      .select('note')
      .eq('target_user_id', userId);

    let averageRating = 0;
    let reviewCount = 0;

    if (!reviewError && reviewStats && reviewStats.length > 0) {
      reviewCount = reviewStats.length;
      averageRating = reviewStats.reduce((sum, review) => sum + review.note, 0) / reviewCount;
      averageRating = Math.round(averageRating * 10) / 10; // Arrondir à 1 décimale
    }

    // Récupérer les services de l'utilisateur
    const { data: userServices, error: servicesError } = await supabase
      .from('user_services')
      .select('titre, categorie, category_id, subcategory_id, tarif_horaire, description, statut')
      .eq('user_id', userId);

    if (servicesError) {
      logger.error('Erreur lors de la récupération des services utilisateur:', servicesError);
      // Continuer même si les services ne sont pas trouvés
    }

    // Filtrer les services actifs
    const activeServices = userServices ? userServices.filter(service => service.statut === 'actif') : [];

    // Mapping des catégories
    const categories: { [key: string]: string } = {
      '1': 'Jardinage',
      '2': 'Bricolage',
      '3': 'Garde d\'animaux',
      '4': 'Services à la personne',
      '5': 'Événementiel & Restauration',
      '6': 'Services administratifs',
      '7': 'Transport & Logistique',
      '8': 'Communication & Marketing',
      '9': 'Éducation & Formation',
      '10': 'Informatique',
      '11': 'Arts & Divertissement',
      '12': 'Bien-être & Santé',
      '13': 'Services aux entreprises',
      '14': 'Artisanat & Création',
      '15': 'Sport & Loisirs',
      '16': 'Immobilier & Habitat',
      '17': 'Automobile & Transport',
      '18': 'Décoration & Design',
      '19': 'Services financiers',
      '20': 'Tourisme & Voyages',
      '21': 'Rénovation & Travaux',
      '22': 'Piscine & Spa',
      '23': 'Mode & Beauté',
      '24': 'Sécurité & Protection'
    };

    // Préparer les informations détaillées sur les services pour la biographie
    let servicesDetails = '';
    if (activeServices && activeServices.length > 0) {
      servicesDetails = activeServices.map(service => {
        // Récupérer le nom de la catégorie
        const categoryName = service.category_id ? categories[service.category_id] || 'Catégorie inconnue' : 'Catégorie non spécifiée';

        // Limiter la description à 200 caractères et ajouter "..." si elle est plus longue
        const shortDescription = service.description && service.description.length > 200
          ? service.description.substring(0, 200).trim() + '...'
          : (service.description || 'Pas de description');

        return `- ${service.titre} (${categoryName}, ${service.tarif_horaire}€/h): ${shortDescription}`;
      }).join('\n');
    }

    // Extraire les catégories uniques des services actifs
    const uniqueCategories = activeServices && activeServices.length > 0
      ? [...new Set(activeServices.map(service => service.category_id))]
          .filter(id => id && categories[id])
          .map(id => categories[id])
      : [];

    // Préparer les données du profil pour le remplacement dans le prompt (utiliser les données déchiffrées)
    const profileData = {
      nom: decryptedUserProfile?.nom || '',
      prenom: decryptedUserProfile?.prenom || '',
      email: decryptedUserData?.email || '',
      type_profil: decryptedUserData?.user_type || '',
      metier: decryptedUserProfile?.profession || '',
      ville: decryptedUserProfile?.ville || '',
      code_postal: decryptedUserProfile?.code_postal || '',
      disponibilite: decryptedUserProfile?.availability || '',
      badges: userBadges?.map(badge => badge.badge_id).join(', ') || '',
      note_moyenne: averageRating,
      nombre_avis: reviewCount,
      services_similaires: activeServices && activeServices.length > 0 ? activeServices.map(service => service.titre).join(', ') : '',
      services_details: servicesDetails,
      categories_services: uniqueCategories.join(', '),
      tarif_habituel: activeServices && activeServices.length > 0 ? activeServices[0].tarif_horaire : '',
      nombre_services: activeServices ? activeServices.length : 0,
      nombre_categories: uniqueCategories.length,
      bio: decryptedUserProfile?.bio || ''
    };

    // logger.info('Géneration IA : Données de profil utilisateur recupérées profileData :', profileData);

    // Prompts par défaut selon le type
    const defaultPrompts = {
      slogan: `RÉPONSE ATTENDUE : UNIQUEMENT le slogan, SANS AUCUN commentaire, explication ou texte additionnel.

      Tu es LE MEILLEUR COPYWRITER du monde spécialisé en marketing digital - créateur des campagnes virales pour Nike, Apple et Coca-Cola.

      ⚠️ MISSION : Créer un slogan INOUBLIABLE, CAPTIVANT et INCROYABLEMENT PERSONNEL pour mon profil JobPartiel qui fera dire "WOW! Ce slogan me représente à 100% !" Absolument PAS de formules génériques ou banales.

      ⚠️ Le dernier slogan généré "Créateur de sites !" était TERRIBLEMENT GÉNÉRIQUE et NON-VENDEUR. Je veux quelque chose de 100x meilleur.

      📊 ANALYSE DE MON PROFIL :
      Prénom: ${profileData.prenom} | Nom: ${profileData.nom}
      Activité principale: ${profileData.metier || 'aucun, propose quelques choses aléatoires'}
      Ma zone: ${profileData.ville}, ${profileData.code_postal}
      Domaine d'expertise principale: ${profileData.categories_services || 'aucun, propose quelques choses aléatoires'}
      Mes services: ${profileData.services_similaires || 'aucun, propose quelques choses aléatoires'}

      🔥 INGRÉDIENTS OBLIGATOIRES POUR MON SLOGAN PARFAIT:
      1. Une ÉMOTION FORTE liée à mon expertise (confiance, fierté, satisfaction du client...)
      2. Un BÉNÉFICE CONCRET pour mes clients (valeur réelle, résultat tangible)
      3. Une TOUCHE D'ORIGINALITÉ ou de créativité qui me démarque totalement
      4. Un élément de STYLE (rime, allitération, jeu de mots brillant) qui le rend mémorable
      5. Une PROMESSE IMPLICITE de qualité ou de satisfaction

      🎯 EXEMPLES DE NIVEAU D'EXCELLENCE ATTENDU (à adapter à mon profil):
      ✓ "Votre vision web, mon code magique - des sites qui transforment votre business"
      ✓ "Je donne vie à vos idées digitales, pixel par pixel, client par client"
      ✓ "Architecte du web qui transforme vos rêves en sites d'exception"
      ✓ "L'artisan digital qui tisse votre succès dans chaque ligne de code"
      ❌ JAMAIS: "Créateur de sites!" (trop banal)
      ❌ JAMAIS: "Expert en création web" (trop générique)

      📝 FORMAT: Maximum 80 caractères, sans guillemets, percutant et mémorable.

      <span style="color: red;">⚠️ ATTENTION: Le slogan DOIT être extrêmement ATTRACTIF et UNIQUE, pas une formule qu'on pourrait appliquer à n'importe quel professionnel. Il doit capturer l'essence de qui je suis et ce que j'apporte à mes clients de façon EXTRAORDINAIRE. C'est ma carte de visite principale!</span>

      RÉPONSE STRICTEMENT LIMITÉE AU SLOGAN. Ne commence pas par "Voici votre slogan :" ou similaire.
      `,

      default_prompt: `Voici mon canevas général pour générer du contenu sur JobPartiel. L'objectif est que chaque texte soit à la fois professionnel, accessible et teinté de ma personnalité.

      Qui suis-je ? (Informations de base à intégrer naturellement) :
      - Nom: ${profileData.nom}
      - Prénom: ${profileData.prenom}
      - Mon rôle / Ce que je fais : ${profileData.metier}
      - Mon secteur (Localisation) : ${profileData.ville}, ${profileData.code_postal}
      - Mes disponibilités : ${profileData.disponibilite}
      - Mes reconnaissances (Badges obtenus) : ${profileData.badges}
      - Ce qu'on dit de moi (Note moyenne) : ${profileData.note_moyenne}/5 (sur ${profileData.nombre_avis} avis)
      - Ma présentation actuelle (pour inspiration) : ${profileData.bio}
      - Mes domaines d'expertise (${profileData.nombre_categories} catégories): ${profileData.categories_services}

      Mes services (${profileData.nombre_services} au total):
      ${profileData.services_details}

      Principes clés pour la génération :
      1.  **Ton Humain et Chaleureux** : Parle comme une vraie personne, pas un robot. Adopte un ton amical et professionnel.
      2.  **Clarté et Conciseneté** : Va droit au but, avec des phrases simples et bien structurées.
      3.  **Adaptabilité** : Le style doit pouvoir s'ajuster légèrement au contexte spécifique de la demande (description de service, réponse à un avis, etc.) tout en restant fidèle à ma personnalité.
      4.  **Mise en Valeur** : Souligne discrètement mes points forts (compétences, expérience, badges) sans en faire une liste indigeste.
      5.  **Authenticité** : Évite le jargon excessif et les formulations trop génériques. L'idée est de montrer qui je suis vraiment et ce qui me rend unique.
      6.  **Intégration des Services** : Mentionne mes services de manière naturelle, en fonction du contexte, sans les lister tous systématiquement.

      Format HTML simple : Utilise des balises comme <p>, <strong>, <em> pour structurer et mettre en valeur, mais sans excès. Pas d'astérisques ni de markdown. N'utilise pas de listes à puces avec des astérisques ou des tirets, utilise plutôt le HTML approprié.

      Pense à varier les formulations pour que chaque génération soit fraîche et engageante. L'objectif est de créer une connexion avec l'utilisateur.`,

      comment: `Rédige un commentaire professionnel, court et interrogatif (entre 50 et 120 caractères) pour une mission sur JobPartiel. L'objectif est de poser une question pertinente ou faire une remarque constructive qui montre mon intérêt.

      Voici quelques informations sur moi (à utiliser si pertinent) :
      - Nom: ${profileData.nom}
      - Prénom: ${profileData.prenom}
      - Ce que je fais (Métier/Profession): ${profileData.metier}
      - Mon coin (Localisation): ${profileData.ville}, ${profileData.code_postal}
      - Quand je suis dispo (Disponibilité): ${profileData.disponibilite}
      - Mes badges de reconnaissance: ${profileData.badges}
      - Ce que les autres pensent de moi (Note moyenne: ${profileData.note_moyenne}/5 sur ${profileData.nombre_avis} avis)

      Instructions spécifiques :
      1. Pose une question précise sur la mission (détails manquants, clarifications, etc.)
      2. OU fais une remarque suivie d'une question qui montre ton expertise
      3. La phrase DOIT se terminer par une formulation interrogative
      4. Reste courtois et professionnel tout en montrant ton intérêt
      5. Évite les formulations génériques et les banalités
      6. N'utilise JAMAIS de guillemets (") dans le commentaire

      Exemples de formulations efficaces :
      - Pourriez-vous préciser si... ?
      - Je me demande si... ?
      - Serait-il possible de... ?
      - J'ai l'habitude de [compétence], comment souhaitez-vous que... ?

      <span style="color: red;">IMPORTANT : Le commentaire final DOIT faire entre 50 et 120 caractères (espaces compris), DOIT se terminer par une question, et NE DOIT PAS contenir de guillemets ni de balises HTML.</span>`,

      biography: `Rédige une biographie pour mon profil JobPartiel, qui DOIT faire entre 500 et 800 caractères (espaces compris), qui soit à la fois professionnelle, engageante et avec une touche personnelle. Je veux que ça sonne comme si c'était vraiment moi qui parlais ! Soit fun !

      Voici quelques informations à mon sujet (utilise celles qui sont pertinentes et intègre-les naturellement dans un récit, pas une liste !) :
      - Nom: ${profileData.nom}
      - Prénom: ${profileData.prenom}
      - Ce que je fais (Métier/Profession): ${profileData.metier}
      - Mon coin (Localisation): ${profileData.ville}, ${profileData.code_postal}
      - Quand je suis dispo (Disponibilité): ${profileData.disponibilite}
      - Mes badges de reconnaissance: ${profileData.badges}
      - Ce que les autres pensent de moi (Note moyenne: ${profileData.note_moyenne}/5 sur ${profileData.nombre_avis} avis)
      - Mes domaines d'expertise (${profileData.nombre_categories} catégories): ${profileData.categories_services}

      Mes services (${profileData.nombre_services} au total):
      ${profileData.services_details}

      Objectifs de la bio :
      1.  **Authenticité avant tout** : Utilise un ton chaleureux, amical et direct (tutoiement si approprié pour la plateforme, sinon vouvoiement respectueux mais pas distant). Que ça respire la bonne humeur et la passion pour ce que je fais !
      2.  **Raconte une histoire** : Au lieu d'énumérer, tisse une petite histoire. Par exemple, comment j'en suis venu à faire ce métier, ce qui me motive.
      3.  **Mets en avant ce qui me rend unique** : Qu'est-ce qui me différencie ? Peut-être une anecdote, une approche particulière ?
      4.  **Sois concis mais impactant** : Environ 600-900 caractères. Chaque mot compte !
      5.  **Intègre naturellement mes points forts** : Si j'ai des badges (comme 'super-reactif' ou 'membre-premium'), explique brièvement ce que ça signifie pour le client, sans que ça fasse "liste de courses".
      6.  **Mentionne la diversité de mes services** : Si j'ai plusieurs services, évoque cette polyvalence sans les lister tous. Si j'ai un seul service, mets en avant ma spécialisation.
      7.  **Appel à l'action subtil** : Donne envie de me contacter, de discuter d'un projet.
      8.  **Format HTML simple** : Utilise des balises comme <p>, <strong>, <em> pour structurer et mettre en valeur, mais sans excès. Le but est la lisibilité et une touche de dynamisme.

      Ce qu'il faut **ABSOLUMENT ÉVITER** :
      -   Les phrases toutes faites, le jargon d'entreprise impersonnel.
      -   Un ton robotique ou trop formel.
      -   Les listes à puces brutes dans la bio finale.
      -   Copier/coller ma bio actuelle.
      -   Lister tous mes services un par un dans la bio.
      -   L'ajout d'adresse et de code postal dans la bio.

      Pense à quelqu'un qui lit mon profil et qui doit se dire : "Tiens, cette personne a l'air sympa et compétente, j'ai envie de lui parler !" Fais preuve de créativité !

      Format HTML simple : Utilise des balises comme <p>, <strong>, <em> pour structurer et mettre en valeur, mais sans excès. Pas d'astérisques ni de markdown. N'utilise pas de listes à puces avec des astérisques ou des tirets, utilise plutôt le HTML approprié.

      <span style="color: red;">IMPORTANT : La biographie finale DOIT faire entre 700 et 800 caractères (espaces compris). Toute réponse en dehors de cette fourchette sera rejetée. Compte bien les caractères avant de finaliser.</span>. N'ajoute pas de notes explicatives sur ton processus de rédaction à la fin de la bio.`,

      service_description: `Rédige une description de service pour JobPartiel, qui DOIT faire entre 700 et 800 caractères (espaces compris), qui soit claire, persuasive, et qui donne vraiment envie de faire appel à moi. Imagine que tu expliques ce service à un ami, de manière professionnelle mais super accessible.

      Informations sur moi et le contexte du service (ces informations viennent de mon profil JobPartiel) :
      - Mon nom pour le service : ${profileData.nom} ${profileData.prenom}
      - Mon rôle/métier : ${profileData.metier}
      - Ma zone d'intervention principale : ${profileData.ville}, ${profileData.code_postal}
      - Ma disponibilité : ${profileData.disponibilite}
      - Mes badges ou certifications pertinents pour ce service : ${profileData.badges}
      - Retours clients (Note moyenne : ${profileData.note_moyenne}/5 sur ${profileData.nombre_avis} avis)

      Détails spécifiques au service à décrire (ces informations seront fournies en complément par l'utilisateur ou le système au moment de la génération pour un service spécifique) :
      - Type de service : [par exemple, Jardinage, Bricolage, Garde d'animaux, etc.]
      - Catégorie et sous-catégorie précises du service : [Catégorie du service], [Sous-catégorie du service]
      - Nom du service (si applicable) : [Titre du service]
      - Mon tarif ou mode de tarification : [Tarif du service] € ([Type de tarif]: horaire/forfait)
      - Équipement que j'utilise ou que je fournis : [Équipement pour le service]
      - Mon expérience spécifique dans CE domaine : [Expérience spécifique pour ce type de service]

      Ce qu'il faut **ABSOLUMENT ÉVITER** :
      -   Les phrases toutes faites, le jargon d'entreprise impersonnel.
      -   Un ton robotique ou trop formel.
      -   Les listes à puces brutes dans la description finale.
      -   Copier/coller ma description actuelle.
      -   Lister tous mes services un par un dans la description.
      -   L'ajout d'adresse et de code postal dans la description.

      Voici comment structurer la description (en 700-800 caractères MAXIMUM) :

      1.  **Accroche Personnalisée et Vivante** :
          *   Commence par une phrase qui capte l'attention et qui est directement liée au service. Évite les "Besoin de... ?". Sois plus original !
          *   Exemple : "Marre de passer vos week-ends à tondre la pelouse ? Laissez-moi redonner vie à votre jardin pendant que vous profitez de votre temps libre !"

      2.  **Ce que je propose concrètement (avec une touche humaine)** :
          *   Explique clairement ce que le service inclut. Ne te contente pas de lister, décris l'action et le résultat pour le client.
          *   Exemple : "Je m'occupe de A à Z de la tonte de votre pelouse, du ramassage de l'herbe et même des petites finitions pour que tout soit impeccable."

      3.  **Pourquoi me choisir MOI pour CETTE mission (mes atouts spécifiques)** :
          *   Mets en avant tes compétences spécifiques pour ce service, ton expérience pertinente. Qu'est-ce qui fait que tu es bon là-dedans ?
          *   Intègre naturellement tes badges ou certifications si cela renforce ta crédibilité pour ce service.
          *   Exemple : "Avec mes X années d'expérience en entretien paysager et mon matériel professionnel, je garantis un résultat soigné et rapide. En plus, mon badge 'Jobbeur Efficace' témoigne de ma capacité à bien faire les choses !"

      4.  **Les Bénéfices pour le Client (parle-lui directement)** :
          *   Au lieu d'une liste, explique avec tes mots ce que le client va y gagner : gain de temps, tranquillité d'esprit, un travail bien fait, etc.
          *   Exemple : "Imaginez : plus de corvée, juste le plaisir d'un jardin accueillant. Vous gagnez du temps et de l'énergie, et votre espace extérieur retrouve toute sa splendeur."

      5.  **Infos Pratiques (claires et directes)** :
          *   Précise ma zone d'intervention pour ce service.
          *   Mentionne ma politique tarifaire de manière simple.
          *   Exemple : "J'interviens sur ${profileData.ville} et ses environs. Mes tarifs sont transparents, discutons ensemble de votre besoin pour un devis personnalisé."

      6.  **Appel à l'Action Chaleureux et Incitatif** :
          *   Termine par une phrase qui invite à la conversation, pas juste "contactez-moi".
          *   Exemple : "Prêt(e) à retrouver un jardin magnifique sans lever le petit doigt ? Envoyez-moi un message, je serai ravi d'échanger avec vous sur votre projet !"

      Ton général : Adopte un ton professionnel mais super amical et enthousiaste. L'idée est de montrer que derrière le service, il y a une personne compétente et sympathique. Évite le langage trop formel ou robotique. Utilise des mots simples et des phrases qui donnent confiance.
      N'hésite pas à utiliser quelques mots-clés pertinents, mais sans en faire une liste. L'objectif est que le client se dise : "C'est exactement cette personne qu'il me faut !"
      Format HTML simple : Utilise des balises comme <p>, <strong>, <em> pour structurer et mettre en valeur, mais sans excès. Pas d'astérisques ni de markdown. N'utilise pas de listes à puces avec des astérisques ou des tirets, utilise plutôt le HTML approprié.

      <span style="color: red;">IMPORTANT : La description finale DOIT faire entre 700 et 800 caractères (espaces compris). Toute réponse en dehors de cette fourchette sera rejetée. Compte bien les caractères avant de finaliser.</span>`,

      mission_post: `Rédige une description de mission comme si tu étais un particulier qui cherche de l'aide pour une tâche. Ton texte doit être naturel, sympathique et donner envie aux jobbeurs de te proposer leurs services.

      Voici les informations sur ma mission (à intégrer naturellement dans le texte) :
      - Titre de ma mission : [Titre de la mission]
      - Type de service : [Catégorie de la mission] > [Sous-catégorie de la mission]
      - Ce que je propose : Échange en Jobi ou paiement direct
      - Quand j'aimerais que ce soit fait : [Date de la mission] (si précisée)
      - Durée approximative : [Durée estimée de la mission] (si précisée)
      - Compétences appréciées : [Compétences pour la mission]
      - Matériel : Est-ce que je fournis le matériel ou le jobbeur doit l'apporter
      - Urgence : Est-ce urgent ou pas

      Conseils pour la rédaction :
      1. Sois précis sur ce que j'attends concrètement (tâches à réaliser)
      2. Mentionne ce qui pourrait rendre cette mission intéressante ou agréable
      3. Indique les compétences ou qualités qui seraient un plus
      4. Donne toutes les infos pratiques importantes (timing, matériel, etc.)
      5. Utilise un ton amical et personnel, comme si tu parlais à un voisin serviable

      À ÉVITER ABSOLUMENT :
      - Le jargon professionnel ou commercial
      - Les formules impersonnelles type "nous recherchons un prestataire"
      - Les listes à puces dans la version finale
      - Les phrases toutes faites qui sonnent comme une annonce d'emploi
      - Mentionner mon adresse complète ou code postal (pour des raisons de confidentialité)

      Format et style :
      - Écris à la première personne (je cherche, j'ai besoin...)
      - Utilise un ton conversationnel et chaleureux
      - Structure avec des balises HTML simples (<p>, <strong>, <em>) pour la mise en forme
      - Termine par une invitation amicale à me contacter

      <span style="color: red;">IMPORTANT : Ta description DOIT faire entre 600 et 700 caractères (espaces compris). C'est une contrainte technique de la plateforme.</span>

      Exemple de ton à adopter : "Bonjour ! Je cherche quelqu'un pour m'aider à... J'aimerais que ce soit fait... Si vous avez de l'expérience en... ce serait parfait ! N'hésitez pas à me proposer vos services si vous pensez pouvoir m'aider."`,

      gallery_description: `Rédige une description professionnelle, engageante et concise (200-300 caractères max) pour présenter l'album photo d'un utilisateur mettant en avant ses réalisations de jobbing (services proposés). La description doit être écrite à la première personne, comme si l'utilisateur présentait lui-même son album à un visiteur, avec un ton chaleureux, authentique, enthousiaste et accessible. Adresse-toi directement au lecteur. Intègre le nom de la galerie de façon naturelle et subtile dans le texte, sans le reprendre tel quel ni commencer par "Bienvenue dans ma galerie ...". N'utilise aucun guillemet (\"). Pas de HTML, pas de markdown, pas de liste. Si la description est vide, crée une description à partir du nom de la galerie uniquement, en le reformulant de façon fluide et naturelle. La description finale doit obligatoirement faire 300 caractères maximum, espaces compris. Toute réponse plus longue sera rejetée.`,

      review_response: `Rédige une réponse à un avis client sur JobPartiel qui soit sincère, personnalisée et professionnelle. L'objectif est de montrer que chaque retour est pris au sérieux et que la satisfaction client est ma priorité.

      Contexte de l'avis (ces informations seront fournies avec l'avis spécifique) :
      - Évaluation donnée par le client : [Note de l'avis]/5
      - Ce que le client a écrit (Commentaire) : [Commentaire de l'avis]
      - Nom du client (si disponible, pour personnaliser) : [Nom du client]
      - Date de publication de l'avis : [Date de l'avis]
      - Le service ou la mission concerné(e) : [Service/Mission concerné(e)]

      Mes informations (pour le contexte, basées sur mon profil ${profileData.nom} ${profileData.prenom}) :
      - Mon nom : ${profileData.nom} ${profileData.prenom}
      - Mon rôle sur JobPartiel : ${profileData.type_profil} (jobbeur/client)
      - Ma réputation globale (Note moyenne) : ${profileData.note_moyenne}/5 (sur ${profileData.nombre_avis} avis)

      Ce qu'il faut **ABSOLUMENT ÉVITER** :
      -   Les phrases toutes faites, le jargon d'entreprise impersonnel.
      -   Un ton robotique ou trop formel.
      -   Les listes à puces brutes dans l'avis finale.
      -   Copier/coller mon avis actuel.
      -   L'ajout d'adresse et de code postal dans la description.

      Instructions pour la réponse :

      1.  **Accusé de Réception Humain** :
          *   Commence toujours par remercier le client pour son temps et son feedback, que l'avis soit positif, mitigé ou négatif.
          *   Si possible et pertinent, utilise le nom du client : "Bonjour [Nom du client], merci beaucoup pour votre retour !"

      2.  **Réponse Spécifique au Contenu de l'Avis** :
          *   **Si l'avis est positif** : Exprime ta joie et ta satisfaction. Mentionne un aspect positif spécifique que le client a souligné si possible. Renforce l'idée que tu serais ravi de collaborer à nouveau.
              *   Exemple : "Je suis ravi(e) d'apprendre que [aspect spécifique] vous a plu ! C'était un plaisir de travailler sur [mission/service] pour vous."
          *   **Si l'avis est négatif ou mitigé** : Montre de l'empathie et présente des excuses sincères pour l'expérience décevante. Ne sois pas sur la défensive.
              *   Adresse directement les points soulevés par le client.
              *   Propose une solution concrète si c'est approprié (ex : discuter plus en détail, offrir une compensation, expliquer les mesures prises pour améliorer).
              *   Invite à poursuivre la discussion en privé pour résoudre le problème.
              *   Exemple : "Je suis sincèrement désolé(e) d'apprendre que [problème spécifique] n'a pas été à la hauteur de vos attentes. Ce n'est pas le niveau de service que je vise. Pourrions-nous en discuter davantage pour que je comprenne mieux et que nous trouvions une solution ?"

      3.  **Ton Général** :
          *   Reste toujours courtois, professionnel et constructif.
          *   Adapte légèrement ton ton à celui du client (sans copier), mais garde une ligne directrice respectueuse.
          *   L'objectif est de transformer même une expérience négative en une opportunité de montrer ton professionnalisme et ton engagement envers la satisfaction client.

      4.  **Conclusion** :
          *   Termine sur une note positive ou proactive.

      Format SANS HTML : N'utilise aucunes balises html comme <p>, <strong>, <em>. Pas d'astérisques ni de markdown. N'utilise pas de listes à puces avec des astérisques ou des tirets, mais seulement du texte brut.

      Longueur : Environ 100-180 mots. L'important est la pertinence et la sincérité.
      Évite les réponses génériques et standardisées. Chaque client mérite une attention personnelle.`,

      mission_offer: `Rédige une proposition d'offre percutante et personnalisée, qui DOIT faire entre 700 et 800 caractères (espaces compris), pour répondre à une mission spécifique sur JobPartiel. Je veux que le client sente que j'ai bien compris son besoin et que je suis LA bonne personne pour le job.

      Détails de la mission à laquelle je réponds (ces informations seront fournies avec la mission) :
      - Intitulé de la mission : [Titre de la mission]
      - Ce que le client a décrit : [Description de la mission]
      - Secteur d'activité : [Catégorie de la mission]
      - Le budget qu'ils ont en tête : [Budget de la mission] €
      - Où ça se passe : [Ville de la mission], [Code postal de la mission]
      - Pour quand : [Date de la mission]

      Mes atouts pour CETTE mission (basés sur mon profil ${profileData.nom} ${profileData.prenom}) :
      - Mon nom : ${profileData.nom} ${profileData.prenom}
      - Mes services (${profileData.nombre_services} au total) :
      ${profileData.services_details}
      - Ce que les autres disent de mon travail (Note moyenne) : ${profileData.note_moyenne}/5 (sur ${profileData.nombre_avis} avis)
      - Mon tarif de base (pour info) : ${profileData.tarif_habituel} €/heure
      - Mes disponibilités actuelles : ${profileData.disponibilite}
      - Mes petits plus (Badges/Certifications pertinents) : ${profileData.badges}

      Ce qu'il faut **ABSOLUMENT ÉVITER** :
      -   Les phrases toutes faites, le jargon d'entreprise impersonnel.
      -   Un ton robotique ou trop formel.
      -   Les listes à puces brutes dans l'offre.
      -   Copier/coller mon offre actuelle.
      -   Lister tous mes services un par un dans l'offre.

      Comment construire mon offre (environ 150-250 mots) :

      1.  **Accroche Personnalisée** :
          *   Commence par montrer que tu as lu et compris la demande. Fais référence à un élément spécifique de sa description.
          *   Exprime ton enthousiasme pour cette mission.
          *   Exemple : "Bonjour ! Votre mission concernant [élément spécifique de la description] a particulièrement retenu mon attention et je serais ravi(e) de vous aider."

      2.  **Pourquoi MOI pour CETTE mission ? (Ma Proposition de Valeur)** :
          *   Explique clairement comment mes compétences et mon expérience répondent DIRECTEMENT aux besoins de la mission. Sois spécifique.
          *   Mets en avant 1 ou 2 réalisations similaires si tu en as.
          *   Intègre naturellement tes badges ou certifications s'ils sont un vrai plus pour cette tâche.
          *   Exemple : "Avec mes [X années] d'expérience en [compétence clé] et ayant déjà réalisé [mission similaire], je suis confiant(e) pour [résultat attendu par le client]. Mon badge 'Expert en X' témoigne de ma maîtrise dans ce domaine."

      3.  **Mon Approche (Comment je compte m'y prendre - brièvement)** :
          *   Si pertinent, esquisse rapidement comment tu aborderais la mission. Cela montre ton professionnalisme.
          *   Exemple : "Je propose de commencer par [première étape] afin de bien cerner [objectif]."

      4.  **Proposition Tarifaire et Disponibilité** :
          *   Confirme si le budget te convient ou propose ton tarif, en expliquant sa valeur (qualité, efficacité).
          *   Indique clairement tes disponibilités par rapport à la date souhaitée par le client.
          *   Exemple : "Votre budget de [Budget de la mission]€ me semble adapté / Je peux réaliser cette mission pour un tarif de X€. Je suis disponible à partir du ${profileData.disponibilite} et pourrais commencer dès le [Date de la mission]."

      5.  **Appel à la Discussion** :
          *   Termine par une invitation à échanger davantage.
          *   Exemple : "N'hésitez pas si vous avez des questions ou si vous souhaitez discuter plus en détail de votre projet. Je suis à votre écoute !"

      Format SANS HTML : N'utilise aucunes balises html comme <p>, <strong>, <em>. Pas d'astérisques ni de markdown. N'utilise pas de listes à puces avec des astérisques ou des tirets, mais seulement du texte brut.

      Ton général : Professionnel, confiant, mais aussi accessible et enthousiaste. Le client doit sentir que tu es compétent(e), fiable et que ce sera un plaisir de travailler avec toi.
      Évite les copier-coller génériques. Chaque offre doit sembler unique et pensée pour la mission.

      <span style="color: red;">IMPORTANT : La proposition finale DOIT faire entre 700 et 800 caractères (espaces compris). Toute réponse en dehors de cette fourchette sera rejetée. Compte bien les caractères avant de finaliser.</span>`,

      support_user_assistance: `Tu es un assistant IA spécialisé dans l'aide aux utilisateurs de JobPartiel. Ton rôle est d'analyser le ticket de support et de fournir des conseils utiles et des solutions pratiques à l'utilisateur.

Contexte du ticket :
- Titre : {{ticket_title}}
- Description : {{ticket_description}}
- Catégorie : {{ticket_category}}
- Priorité : {{ticket_priority}}
- Statut : {{ticket_status}}

Profil utilisateur :
- Nom : {{prenom}} {{nom}}
- Type de profil : {{type_de_profil}}
- Ville : {{ville}}
- Services : {{services}}

Historique des commentaires :
{{comments_history}}

Fournis une réponse d'assistance qui :
- Analyse le problème décrit
- Propose des solutions concrètes et étapes à suivre
- Donne des conseils préventifs pour éviter le problème à l'avenir
- Reste bienveillant et encourageant
- Fait environ 200-300 mots
- Utilise un ton amical et professionnel
- Inclut des liens vers la documentation si pertinent

Concentre-toi sur l'aide pratique et les solutions réalisables.`,

      support_staff_assistance: `Tu es un assistant IA pour l'équipe support de JobPartiel. Ton rôle est d'analyser le ticket et de fournir des recommandations professionnelles pour la résolution et la gestion du cas.

Contexte du ticket :
- Titre : {{ticket_title}}
- Description : {{ticket_description}}
- Catégorie : {{ticket_category}}
- Priorité : {{ticket_priority}}
- Statut : {{ticket_status}}
- Assigné à : {{assigned_to}}
- SLA : {{sla_due_at}}

Profil utilisateur :
- Nom : {{prenom}} {{nom}}
- Type de profil : {{type_de_profil}}
- Email : {{email}}
- Ville : {{ville}}
- Services : {{services}}
- Statut compte : {{profil_actif}}

Historique des commentaires :
{{comments_history}}

Fournis une analyse professionnelle qui inclut :
- Évaluation de la complexité du problème
- Recommandations de résolution étape par étape
- Identification des risques potentiels
- Suggestions d'escalade si nécessaire
- Modèles de réponse adaptés au contexte
- Estimation du temps de résolution
- Actions préventives pour éviter la récurrence

Ton analyse doit être factuelle, structurée et orientée solution pour optimiser l'efficacité du support.`,

      support_comment: `Tu es un assistant IA spécialisé dans la rédaction de commentaires pour les tickets de support de JobPartiel.

Rédige un commentaire professionnel et utile pour ce ticket de support. Le commentaire doit :
- Être pertinent par rapport au problème décrit
- Prendre en compte l'historique des échanges
- Proposer une solution concrète ou demander des clarifications nécessaires
- Être constructif et bienveillant
- Faire entre 50 et 300 mots
- Utiliser un ton professionnel mais chaleureux
- Éviter le jargon technique si l'utilisateur n'est pas du staff
- Proposer des solutions pratiques
- Faire preuve d'empathie si nécessaire
- Se terminer par "Cordialement,\nL'équipe JobPartiel"

IMPORTANT : Ne pas inclure de signature personnalisée avec un nom individuel. Utilise uniquement "L'équipe JobPartiel" comme signature.

Le commentaire final doit être directement utilisable et aider à faire avancer la résolution du ticket.`
    };

    // Si le frontend a envoyé un prompt personnalisé et un contexte prévisualisé,
    // utiliser directement le contexte comme prompt complet
    let fullPrompt;

    if (customPrompt && context) {
      // Le frontend a envoyé un prompt personnalisé et un contexte prévisualisé
      // Le contexte est déjà le prompt prévisualisé avec les données de l'utilisateur
      fullPrompt = context;
    } else {
      // Utiliser le prompt personnalisé stocké en base de données ou le prompt par défaut
      let prompt = userPrompt?.prompt || defaultPrompts[type as keyof typeof defaultPrompts];

      // Remplacer les variables dans le prompt
      Object.entries(profileData).forEach(([key, value]) => {
        prompt = prompt.replace(new RegExp(`{{${key}}}`, 'g'), value || `[${key} non spécifié]`);
      });

      // Ajouter le contexte au prompt
      fullPrompt = `${prompt}\n\nInformations/Contexte supplémentaires:\n${context}`;
    }

    logger.info('Génération IA : Lancement de la génération avec le prompt : ' + JSON.stringify({prompt: fullPrompt.slice(0, 5000)}));

    // Appeler l'API OpenRouter
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), TIMEOUT_API);

    try {
      let AI_API_MODEL = await selectAIModel(false);
      let response;
      let usedFallback = false;
      // Factorisation du payload pour éviter la duplication
      const getAIPayload = (model: string) => ({
        model,
        messages: [
          {
            role: "system",
            content: "Si on te demande quel LLM tu es, qui t'a créé, ou des questions similaires sur ton identité ou ton modèle sous-jacent, réponds que tu es un assistant basé sur un algorithme propriétaire développé par l'équipe technique de JobPartiel pour aider au mieux les utilisateurs. Ne révèle jamais le vrai nom du modèle ou de la technologie utilisée."
          },
          {
            role: "user",
            content: fullPrompt
          }
        ],
        temperature: 1.2,
        top_p: 0.9,
        frequency_penalty: 0.3,
        presence_penalty: 0.6,
        max_tokens: 1500,
        // stop: ["```"],
        structured_outputs: true
      });
      const axiosConfig = {
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${AI_API_KEY}`,
          'HTTP-Referer': 'https://jobpartiel.fr',
          'X-Title': 'JobPartiel AI Generation Assistant'
        },
        signal: controller.signal,
        timeout: TIMEOUT_API
      };
      try {
        response = await axios.post(
          AI_API_URL,
          getAIPayload(AI_API_MODEL),
          axiosConfig
        );
        // Fallback si le modèle gratuit retourne une erreur 503 provider dans le body
        if (
          !usedFallback &&
          response?.data?.error &&
          response.data.error.code === 503
        ) {
          logger.warn('Erreur 503 provider détectée, tentative avec le modèle payant.');
          AI_API_MODEL = AI_API_MODEL_PAYANT;
          usedFallback = true;
          response = await axios.post(
            AI_API_URL,
            getAIPayload(AI_API_MODEL),
            axiosConfig
          );
        }
      } catch (err: any) {
        // Si erreur 429 ou 400, on tente une fois avec la clé payante
        if (err.response && (err.response.status === 429 || err.response.status === 400) && !usedFallback) {
          logger.warn(`Erreur ${err.response.status} détectée, tentative avec le modèle payant.`);
          AI_API_MODEL = AI_API_MODEL_PAYANT;
          usedFallback = true;
          response = await axios.post(
            AI_API_URL,
            getAIPayload(AI_API_MODEL),
            axiosConfig
          );
        } else {
          throw err;
        }
      }

      // Extraire le contenu généré de façon sécurisée
      let generatedContent;
      if (
        response &&
        response.data &&
        Array.isArray(response.data.choices) &&
        response.data.choices[0] &&
        response.data.choices[0].message &&
        typeof response.data.choices[0].message.content === 'string'
      ) {
        generatedContent = response.data.choices[0].message.content;
      } else {
        logger.error('Réponse IA inattendue :', {
          data: response?.data,
          status: response?.status,
          headers: response?.headers
        });
        return res.status(500).json({
          success: false,
          message: "Erreur lors de la génération du contenu : réponse IA inattendue.",
          error: 'Format de réponse IA non conforme',
          toastType: 'error'
        });
      }

      // Enregistrer les données d'utilisation de l'API : token utilisés, prix, etc.
      try {
        if (response.data && response.data.usage) {
          // Déterminer le type de service en fonction du type de génération
          let serviceType: 'generation' | 'biography_generation' | 'service_description_generation' | 'mission_post_generation' | 'review_response_generation' | 'mission_offer_generation' | 'comment_generation' | 'midjourney_prompt' | 'card_editor_prompt' | 'custom_prompt_generation' | 'slogan_generation' | 'support_user_assistance' | 'support_staff_assistance' | 'support_comment_generation' = 'generation';

          switch (type) {
            case 'biography':
              serviceType = 'biography_generation';
              break;
            case 'service_description':
              serviceType = 'service_description_generation';
              break;
            case 'mission_post':
              serviceType = 'mission_post_generation';
              break;
            case 'review_response':
              serviceType = 'review_response_generation';
              break;
            case 'mission_offer':
              serviceType = 'mission_offer_generation';
              break;
            case 'comment':
              serviceType = 'comment_generation';
              break;
            case 'midjourney_prompt':
              serviceType = 'midjourney_prompt';
              break;
            case 'card_editor_prompt':
              serviceType = 'card_editor_prompt';
              break;
            case 'default_prompt':
              serviceType = 'custom_prompt_generation';
              break;
            case 'slogan':
              serviceType = 'slogan_generation';
              break;
            case 'support_user_assistance':
              serviceType = 'support_user_assistance';
              break;
            case 'support_staff_assistance':
              serviceType = 'support_staff_assistance';
              break;
            case 'support_comment':
              serviceType = 'support_comment_generation';
              break;
            default:
              serviceType = 'generation';
          }

          await logOpenRouterUsage(
            userId,
            serviceType,
            AI_API_MODEL,
            response.data.usage.prompt_tokens || 0,
            response.data.usage.completion_tokens || 0,
            response.data.usage.total_tokens || 0,
            response.data.id || null
          );
        } else {
          // Si les données d'utilisation ne sont pas disponibles, loguer avec des valeurs estimées
          // Déterminer le type de service en fonction du type de génération
          let serviceType: 'generation' | 'biography_generation' | 'service_description_generation' | 'mission_post_generation' | 'review_response_generation' | 'mission_offer_generation' | 'comment_generation' | 'midjourney_prompt' | 'card_editor_prompt' | 'custom_prompt_generation' | 'slogan_generation' | 'support_user_assistance' | 'support_staff_assistance' | 'support_comment_generation' = 'generation';

          switch (type) {
            case 'biography':
              serviceType = 'biography_generation';
              break;
            case 'service_description':
              serviceType = 'service_description_generation';
              break;
            case 'mission_post':
              serviceType = 'mission_post_generation';
              break;
            case 'review_response':
              serviceType = 'review_response_generation';
              break;
            case 'mission_offer':
              serviceType = 'mission_offer_generation';
              break;
            case 'comment':
              serviceType = 'comment_generation';
              break;
            case 'midjourney_prompt':
              serviceType = 'midjourney_prompt';
              break;
            case 'card_editor_prompt':
              serviceType = 'card_editor_prompt';
              break;
            case 'default_prompt':
              serviceType = 'custom_prompt_generation';
              break;
            case 'slogan':
              serviceType = 'slogan_generation';
              break;
            case 'support_user_assistance':
              serviceType = 'support_user_assistance';
              break;
            case 'support_staff_assistance':
              serviceType = 'support_staff_assistance';
              break;
            case 'support_comment':
              serviceType = 'support_comment_generation';
              break;
            default:
              serviceType = 'generation';
          }

          await logOpenRouterUsage(
            userId,
            serviceType,
            AI_API_MODEL,
            fullPrompt.length / 4, // Estimation grossière: environ 4 caractères par token
            generatedContent.length / 4, // Estimation pour le contenu généré
            (fullPrompt.length / 4) + (generatedContent.length / 4),
            response.data?.id || null
          );
        }
      } catch (logError) {
        logger.error('Erreur lors de l\'enregistrement des données d\'utilisation OpenRouter pour la génération', {
          error: logError,
          userId
        });
      }

      // L'enregistrement dans l'historique a déjà été fait lors de la déduction des crédits

      // Enregistrer l'activité
      await logUserActivity(
        userId,
        'ai_credit_used',
        undefined,
        'ai_credits',
        JSON.stringify({ type, credits_remaining: newCredits }),
        getIpFromRequest(req)
      );

      logger.info('Génération IA : Contenu généré terminé : ' + generatedContent);

      // Retourner le contenu généré
      return res.status(200).json({
        success: true,
        content: generatedContent,
        creditsRemaining: newCredits,
        toastType: 'success'
      });
    } catch (apiError: any) {
      logger.error('Erreur lors de l\'appel à l\'API OpenRouter:', apiError);

      // RESTITUER LES CRÉDITS en cas d'erreur de génération
      if (creditsDebited && currentCredits !== undefined) {
        logger.warn('Restitution des crédits suite à une erreur API de génération');
        await restoreAiCredits(userId!, currentCredits, 1);
      }

      return res.status(500).json({
        success: false,
        message: 'Erreur lors de la génération du contenu',
        error: apiError.message,
        toastType: 'error'
      });
    } finally {
      clearTimeout(timeoutId);
    }
  } catch (error: any) {
    logger.error('Erreur lors de la génération du contenu:', error);

    // Si les crédits ont été débités et qu'une erreur survient AVANT l'API, les restituer
    // (Les erreurs d'API sont déjà gérées dans le catch spécifique)
    if (creditsDebited && userId && currentCredits !== undefined && !error.message?.includes('API OpenRouter')) {
      logger.warn('Restitution des crédits suite à une erreur générale (non-API)');
      await restoreAiCredits(userId, currentCredits, 1);
    }

    return res.status(500).json({
      success: false,
      message: 'Erreur lors de la génération du contenu',
      toastType: 'error'
    });
  }
};

/**
 * Génère une assistance IA pour les tickets de support
 */
export const generateSupportAssistance = async (req: Request, res: Response) => {
  // Déclarer les variables au niveau de la fonction pour qu'elles soient accessibles dans le catch
  let userId: string | undefined;
  let currentCredits: number | undefined;
  let creditsDebited = false;

  try {
    userId = req.user?.id || req.user?.userId;
    const isStaff = req.user?.role === 'jobpadm' || req.user?.role === 'jobmodo';

    if (!userId) {
      return res.status(401).json({
        success: false,
        message: 'Utilisateur non authentifié',
        toastType: 'error'
      });
    }

    // Vérifier si l'utilisateur a donné son consentement pour l'utilisation de l'IA
    const hasConsent = await hasUserConsented(userId);
    if (!hasConsent) {
      return res.status(403).json({
        success: false,
        message: 'Vous devez accepter les conditions d\'utilisation de l\'IA avant de pouvoir utiliser l\'assistance',
        requiresConsent: true,
        toastType: 'error'
      });
    }

    const { ticketId, mode } = req.body;

    if (!ticketId || !mode || !['user', 'staff'].includes(mode)) {
      return res.status(400).json({
        success: false,
        message: 'Données invalides',
        toastType: 'error'
      });
    }

    // Récupérer le ticket avec toutes les informations nécessaires
    const { data: ticket, error: ticketError } = await supabase
      .from('support_tickets')
      .select(`
        *,
        user:users!support_tickets_user_id_fkey(id, email, user_type, profil_actif),
        assignee:users!support_tickets_assigned_to_fkey(id, email),
        support_ticket_comments(
          id, message, is_internal, created_at,
          user:users!support_ticket_comments_user_id_fkey(id, email, role)
        )
      `)
      .eq('id', ticketId)
      .single();

    if (ticketError || !ticket) {
      return res.status(404).json({
        success: false,
        message: 'Ticket non trouvé',
        toastType: 'error'
      });
    }

    // Vérifier les permissions
    const canAccess = isStaff || ticket.user_id === userId;
    if (!canAccess) {
      return res.status(403).json({
        success: false,
        message: 'Non autorisé à accéder à ce ticket',
        toastType: 'error'
      });
    }

    // Vérifier les permissions pour le mode staff
    if (mode === 'staff' && !isStaff) {
      return res.status(403).json({
        success: false,
        message: 'Accès réservé au personnel de support',
        toastType: 'error'
      });
    }

    // Vérifier les crédits IA
    const creditsCacheKey = `${USER_CREDITS_CACHE_PREFIX}${userId}`;

    const cachedCredits = await redis.get(creditsCacheKey);
    if (cachedCredits !== null) {
      currentCredits = parseInt(cachedCredits, 10);
    } else {
      const { data: creditsData, error: creditsError } = await supabase
        .from('user_ai_credits')
        .select('credits')
        .eq('user_id', userId)
        .single();

      if (creditsError && creditsError.code !== 'PGRST116') {
        logger.error('Erreur lors de la vérification des crédits IA:', creditsError);
        return res.status(500).json({
          success: false,
          message: 'Erreur lors de la vérification des crédits IA',
          toastType: 'error'
        });
      }

      currentCredits = creditsData?.credits || 0;
      await redis.setex(creditsCacheKey, CACHE_TTL_SHORT, currentCredits!.toString());
    }

    if (!currentCredits || currentCredits <= 0) {
      return res.status(400).json({
        success: false,
        message: 'Vous n\'avez pas assez de crédits IA. Veuillez en acheter dans le menu "Intelligence Artificielle"',
        toastType: 'error'
      });
    }

    // Déduire les crédits IA AVANT la génération pour éviter les abus
    const newCredits = currentCredits - 1;

    // Mettre à jour les crédits dans la base de données
    const { error: updateError } = await supabase
      .from('user_ai_credits')
      .update({
        credits: newCredits,
        updated_at: new Date().toISOString()
      })
      .eq('user_id', userId);

    if (updateError) {
      logger.error('Erreur lors de la mise à jour des crédits IA:', updateError);
      return res.status(500).json({
        success: false,
        message: 'Erreur lors de la déduction des crédits IA',
        toastType: 'error'
      });
    }

    // Mettre à jour le cache
    await redis.setex(creditsCacheKey, CACHE_TTL_SHORT, newCredits.toString());

    // Enregistrer immédiatement la déduction dans l'historique
    try {
      const { logAiCreditsOperation } = require('./aiCreditsController');
      await logAiCreditsOperation(
        userId,
        'utilisation',
        1,
        currentCredits,
        newCredits,
        undefined,
        undefined,
        getIpFromRequest(req),
        `assistance_support_${mode}`
      );
    } catch (historyError) {
      logger.error("Erreur lors de l'enregistrement de la déduction dans l'historique:", historyError);
      // Continuer malgré l'erreur
    }

    // Marquer que les crédits ont été débités
    creditsDebited = true;

    // Récupérer le profil utilisateur du ticket
    const { data: userProfile, error: profileError } = await supabase
      .from('user_profil')
      .select('*')
      .eq('user_id', ticket.user_id)
      .single();

    // Déchiffrer les données du profil
    const decryptedUserProfile = userProfile ? await decryptProfilDataAsync(userProfile) : null;

    // Déchiffrer les données utilisateur du ticket
    const decryptedTicketUser = ticket.user ? await decryptUserDataAsync(ticket.user) : null;

    // Récupérer les services de l'utilisateur
    const { data: userServices, error: servicesError } = await supabase
      .from('user_services')
      .select('titre, categorie')
      .eq('user_id', ticket.user_id)
      .eq('statut', 'actif');

    // Préparer l'historique des commentaires
    const commentsHistory = ticket.support_ticket_comments
      ?.map((comment: any) => {
        const userRole = comment.user?.role || 'utilisateur';
        const roleLabel = userRole === 'jobpadm' ? 'Admin' :
                         userRole === 'jobmodo' ? 'Modérateur' : 'Utilisateur';
        return `[${roleLabel}] ${comment.message}`;
      })
      .join('\n') || 'Aucun commentaire';

    // Préparer les données pour le prompt (utiliser les données déchiffrées)
    const promptData = {
      ticket_title: ticket.title,
      ticket_description: ticket.description,
      ticket_category: ticket.category,
      ticket_priority: ticket.priority,
      ticket_status: ticket.status,
      assigned_to: ticket.assignee?.email || 'Non assigné',
      sla_due_at: ticket.sla_due_at || 'Non défini',
      prenom: decryptedUserProfile?.prenom || '',
      nom: decryptedUserProfile?.nom || '',
      type_de_profil: decryptedTicketUser?.user_type || '',
      email: decryptedTicketUser?.email || '',
      ville: decryptedUserProfile?.ville || '',
      services: userServices?.map((s: any) => s.titre).join(', ') || 'Aucun service',
      profil_actif: decryptedTicketUser?.profil_actif ? 'Actif' : 'Inactif',
      comments_history: commentsHistory
    };

    // Prompts pour l'assistance support
    const supportPrompts = {
      support_user_assistance: `Tu es un assistant IA spécialisé dans l'aide aux utilisateurs de JobPartiel. Ton rôle est d'analyser le ticket de support et de fournir des conseils utiles et des solutions pratiques à l'utilisateur.

Contexte du ticket :
- Titre : {{ticket_title}}
- Description : {{ticket_description}}
- Catégorie : {{ticket_category}}
- Priorité : {{ticket_priority}}
- Statut : {{ticket_status}}

Profil utilisateur :
- Nom : {{prenom}} {{nom}}
- Type de profil : {{type_de_profil}}
- Ville : {{ville}}
- Services : {{services}}

Historique des commentaires :
{{comments_history}}

Fournis une réponse d'assistance qui :
- Analyse le problème décrit
- Propose des solutions concrètes et étapes à suivre
- Donne des conseils préventifs pour éviter le problème à l'avenir
- Reste bienveillant et encourageant
- Fait environ 200-300 mots
- Utilise un ton amical et professionnel
- Inclut des liens vers la documentation si pertinent
- Se termine par "Cordialement,\nL'équipe JobPartiel"

IMPORTANT : Ne pas inclure de signature personnalisée avec un nom individuel. Utilise uniquement "L'équipe JobPartiel" comme signature.

Concentre-toi sur l'aide pratique et les solutions réalisables.`,

      support_staff_assistance: `Tu es un assistant IA pour l'équipe support de JobPartiel. Ton rôle est d'analyser le ticket et de fournir des recommandations professionnelles pour la résolution et la gestion du cas.

Contexte du ticket :
- Titre : {{ticket_title}}
- Description : {{ticket_description}}
- Catégorie : {{ticket_category}}
- Priorité : {{ticket_priority}}
- Statut : {{ticket_status}}
- Assigné à : {{assigned_to}}
- SLA : {{sla_due_at}}

Profil utilisateur :
- Nom : {{prenom}} {{nom}}
- Type de profil : {{type_de_profil}}
- Email : {{email}}
- Ville : {{ville}}
- Services : {{services}}
- Statut compte : {{profil_actif}}

Historique des commentaires :
{{comments_history}}

Fournis une analyse professionnelle qui inclut :
- Évaluation de la complexité du problème
- Recommandations de résolution étape par étape
- Identification des risques potentiels
- Suggestions d'escalade si nécessaire
- Modèles de réponse adaptés au contexte
- Estimation du temps de résolution
- Actions préventives pour éviter la récurrence

Ton analyse doit être factuelle, structurée et orientée solution pour optimiser l'efficacité du support.`
    };

    // Sélectionner le prompt approprié
    const promptType = mode === 'staff' ? 'support_staff_assistance' : 'support_user_assistance';
    let prompt = supportPrompts[promptType as keyof typeof supportPrompts];

    // Remplacer les variables dans le prompt
    Object.entries(promptData).forEach(([key, value]) => {
      prompt = prompt.replace(new RegExp(`{{${key}}}`, 'g'), value || `[${key} non spécifié]`);
    });

    logger.info('Assistance Support IA : Génération pour le ticket:', ticketId, 'Mode:', mode);

    // Appeler l'API OpenRouter
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), TIMEOUT_API);

    try {
      let AI_API_MODEL = await selectAIModel(false);
      let response;
      let usedFallback = false;

      const getAIPayload = (model: string) => ({
        model,
        messages: [
          {
            role: "system",
            content: "Tu es un assistant IA spécialisé dans le support client pour JobPartiel. Fournis des réponses utiles, professionnelles et adaptées au contexte."
          },
          {
            role: "user",
            content: prompt
          }
        ],
        temperature: 0.7,
        top_p: 0.9,
        max_tokens: 1000,
        structured_outputs: true
      });

      const axiosConfig = {
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${AI_API_KEY}`,
          'HTTP-Referer': 'https://jobpartiel.fr',
          'X-Title': 'JobPartiel Support AI Assistant'
        },
        signal: controller.signal,
        timeout: TIMEOUT_API
      };

      try {
        response = await axios.post(AI_API_URL, getAIPayload(AI_API_MODEL), axiosConfig);
      } catch (err: any) {
        if (err.response && (err.response.status === 429 || err.response.status === 400) && !usedFallback) {
          logger.warn(`Erreur ${err.response.status} détectée, tentative avec le modèle payant.`);
          AI_API_MODEL = AI_API_MODEL_PAYANT;
          usedFallback = true;
          response = await axios.post(AI_API_URL, getAIPayload(AI_API_MODEL), axiosConfig);
        } else {
          throw err;
        }
      }

      const generatedContent = response.data.choices[0].message.content;

      // Logger l'utilisation OpenRouter
      try {
        const serviceType = mode === 'staff' ? 'support_staff_assistance' : 'support_user_assistance';
        await logOpenRouterUsage(
          userId,
          serviceType,
          AI_API_MODEL,
          prompt.length / 4,
          generatedContent.length / 4,
          (prompt.length / 4) + (generatedContent.length / 4),
          response.data?.id || null
        );
      } catch (logError) {
        logger.error('Erreur lors de l\'enregistrement des données d\'utilisation OpenRouter:', logError);
      }

      // L'enregistrement dans l'historique a déjà été fait lors de la déduction des crédits

      // Enregistrer l'activité
      await logUserActivity(
        userId,
        'support_ai_assistance_used',
        ticketId,
        'support_tickets',
        JSON.stringify({ mode, credits_remaining: newCredits }),
        getIpFromRequest(req)
      );

      return res.status(200).json({
        success: true,
        content: generatedContent,
        creditsRemaining: newCredits,
        mode,
        toastType: 'success'
      });

    } catch (apiError: any) {
      logger.error('Erreur lors de l\'appel à l\'API OpenRouter pour l\'assistance support:', apiError);

      // RESTITUER LES CRÉDITS en cas d'erreur de génération
      if (creditsDebited && currentCredits !== undefined) {
        logger.warn('Restitution des crédits suite à une erreur API d\'assistance support');
        await restoreAiCredits(userId!, currentCredits, 1);
      }

      return res.status(500).json({
        success: false,
        message: 'Erreur lors de la génération de l\'assistance',
        error: apiError.message,
        toastType: 'error'
      });
    } finally {
      clearTimeout(timeoutId);
    }

  } catch (error: any) {
    logger.error('Erreur lors de la génération de l\'assistance support:', error);

    // Si les crédits ont été débités et qu'une erreur survient AVANT l'API, les restituer
    // (Les erreurs d'API sont déjà gérées dans le catch spécifique)
    if (creditsDebited && userId && currentCredits !== undefined && !error.message?.includes('API OpenRouter')) {
      logger.warn('Restitution des crédits suite à une erreur générale d\'assistance support (non-API)');
      await restoreAiCredits(userId, currentCredits, 1);
    }

    return res.status(500).json({
      success: false,
      message: 'Erreur lors de la génération de l\'assistance',
      toastType: 'error'
    });
  }
};