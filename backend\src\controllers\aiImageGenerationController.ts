import { Request, Response } from 'express';
import logger from '../utils/logger';
import { redis } from '../config/redis';
import { supabase } from '../config/supabase';
import { getIpFromRequest, logUserActivity } from '../utils/activityLogger';
import { hasUserConsented } from './aiConsent';
import { generateImage, generateContextualPrompt, IMAGE_GENERATION_COST, ImageGenerationPurpose } from '../services/aiImageGenerationService';
import { uploadProfilPhoto, uploadGalleryPhoto, uploadMissionPhoto, uploadBannerPhoto } from '../services/storage';
import { generateMidjourneyPromptViaOpenRouter } from '../services/aiImageGenerationService';

// Préfixe pour le cache Redis des crédits IA
const USER_CREDITS_CACHE_PREFIX = 'ai_credits:';

/**
 * Fonction utilitaire pour restituer les crédits IA en cas d'erreur
 */
const restoreAiCredits = async (userId: string, originalCredits: number): Promise<void> => {
  try {
    const creditsCacheKey = `${USER_CREDITS_CACHE_PREFIX}${userId}`;

    // Restaurer les crédits dans la base de données
    const { error: restoreError } = await supabase
      .from('user_ai_credits')
      .update({ credits: originalCredits })
      .eq('user_id', userId);

    if (restoreError) {
      logger.error('Erreur lors de la restitution des crédits IA:', restoreError);
    } else {
      logger.info(`Crédits IA restitués pour l'utilisateur ${userId}: ${originalCredits}`);
    }

    // Restaurer le cache
    await redis.setex(creditsCacheKey, 3600, originalCredits.toString());

    // Enregistrer la restitution dans l'historique
    try {
      const { logAiCreditsOperation } = require('./aiCreditsController');
      await logAiCreditsOperation(
        userId,
        'autre',
        IMAGE_GENERATION_COST,
        originalCredits - IMAGE_GENERATION_COST,
        originalCredits,
        `Restitution suite à erreur de génération d'image IA`,
        undefined,
        'system',
        undefined
      );
    } catch (historyError) {
      logger.error("Erreur lors de l'enregistrement de la restitution dans l'historique:", historyError);
    }
  } catch (error) {
    logger.error('Erreur critique lors de la restitution des crédits IA:', error);
  }
};

/**
 * Génère une image avec l'IA
 */
export const generateImageWithAI = async (req: Request, res: Response) => {
  // Déclarer les variables au niveau de la fonction pour qu'elles soient accessibles dans le catch
  let userId: string | undefined;
  let currentCredits: number | undefined;
  let creditsDebited = false;

  try {
    // Récupérer l'ID utilisateur depuis req.user
    userId = req.user?.id || req.user?.userId;

    if (!userId) {
      return res.status(401).json({
        success: false,
        message: 'Utilisateur non authentifié',
        toastType: 'error'
      });
    }

    // Vérifier si l'utilisateur a donné son consentement pour l'utilisation de l'IA
    const hasConsent = await hasUserConsented(userId);
    if (!hasConsent) {
      return res.status(403).json({
        success: false,
        message: 'Vous devez accepter les conditions d\'utilisation de l\'IA avant de pouvoir générer du contenu',
        requiresConsent: true,
        toastType: 'error'
      });
    }

    // Valider les données de la requête
    const { prompt, purpose, width, height, useContextualPrompt, storeTemporarily, galleryId, galleryName, galleryDescription, missionTitle, missionDescription } = req.body;

    if (!purpose || !['profile_picture', 'banner_picture', 'mission_image', 'gallery_photo', 'featured_photo', 'card_editor'].includes(purpose)) {
      return res.status(400).json({
        success: false,
        message: 'Type d\'image invalide',
        toastType: 'error'
      });
    }

    logger.info('Génération d\'image IA : Début de génération pour l\'utilisateur:', userId);

    // Vérifier si l'utilisateur a des crédits IA (dans le cache)
    const creditsCacheKey = `${USER_CREDITS_CACHE_PREFIX}${userId}`;

    // Essayer de récupérer les crédits depuis le cache
    const cachedCredits = await redis.get(creditsCacheKey);

    if (cachedCredits !== null) {
      currentCredits = parseInt(cachedCredits, 10);
    } else {
      // Si pas dans le cache, récupérer depuis la base de données
      const { data: creditsData, error: creditsError } = await supabase
        .from('user_ai_credits')
        .select('credits')
        .eq('user_id', userId)
        .single();

      if (creditsError) {
        logger.error('Erreur lors de la récupération des crédits IA:', creditsError);
        return res.status(500).json({
          success: false,
          message: 'Erreur lors de la récupération des crédits IA',
          toastType: 'error'
        });
      }

      currentCredits = creditsData?.credits || 0;

      // Mettre en cache pour les prochaines requêtes
      await redis.setex(creditsCacheKey, 3600, currentCredits!.toString());
    }

    // Vérifier si l'utilisateur a assez de crédits (currentCredits est maintenant défini)
    if (!currentCredits || currentCredits < IMAGE_GENERATION_COST) {
      return res.status(400).json({
        success: false,
        message: `Vous n'avez pas assez de crédits IA. La génération d'image nécessite ${IMAGE_GENERATION_COST} crédits. Veuillez en acheter dans le menu "Intelligence Artificielle"`,
        toastType: 'error'
      });
    }

    // Déterminer le prompt de base (manuel ou contextuel)
    let basePrompt = prompt;
    if (!prompt || useContextualPrompt) {
      if (purpose === 'mission_image') {
        basePrompt = await generateContextualPrompt(
          userId,
          purpose as ImageGenerationPurpose,
          undefined, // galleryId
          undefined, // galleryName
          undefined, // galleryDescription
          missionTitle,
          missionDescription
        );
      } else {
        basePrompt = await generateContextualPrompt(
          userId,
          purpose as ImageGenerationPurpose,
          galleryId,
          galleryName,
          galleryDescription
        );
      }
    }

    logger.info(`Génération d'image IA : Prompt de base (FR) : ${basePrompt}`);

    // Améliorer/convertir le prompt en anglais Midjourney via OpenRouter
    let midjourneyPrompt = '';
    if (basePrompt && basePrompt.trim().length > 0) {
      try {
        midjourneyPrompt = await generateMidjourneyPromptViaOpenRouter(userId, purpose as ImageGenerationPurpose, undefined, basePrompt);
        logger.info('Prompt Midjourney généré (EN) et utilisé pour la génération d\'image.');
      } catch (err) {
        logger.warn('Erreur lors de la génération du prompt Midjourney via OpenRouter:', err);
        midjourneyPrompt = basePrompt; // fallback : on utilise le prompt FR si erreur
      }
    }

    // Déduire les crédits IA AVANT la génération pour éviter les abus
    const newCredits = currentCredits - IMAGE_GENERATION_COST;

    // Mettre à jour les crédits dans la base de données
    const { error: updateError } = await supabase
      .from('user_ai_credits')
      .update({ credits: newCredits })
      .eq('user_id', userId);

    if (updateError) {
      logger.error('Erreur lors de la mise à jour des crédits IA:', updateError);
      return res.status(500).json({
        success: false,
        message: 'Erreur lors de la déduction des crédits IA',
        toastType: 'error'
      });
    }

    // Mettre à jour le cache
    await redis.setex(creditsCacheKey, 3600, newCredits.toString());

    // Enregistrer immédiatement la déduction dans l'historique
    try {
      const { logAiCreditsOperation } = require('./aiCreditsController');
      await logAiCreditsOperation(
        userId,
        'utilisation',
        IMAGE_GENERATION_COST,
        currentCredits,
        newCredits,
        undefined,
        undefined,
        getIpFromRequest(req),
        purpose
      );
    } catch (historyError) {
      logger.error("Erreur lors de l'enregistrement de la déduction dans l'historique:", historyError);
      // Continuer malgré l'erreur
    }

    // Marquer que les crédits ont été débités
    creditsDebited = true;

    let imageUrl: string;
    let imageBase64: string;

    try {
      // Générer l'image avec le prompt Midjourney (anglais)
      const result = await generateImage({
        prompt: midjourneyPrompt,
        width: width || 1024,
        height: height || 1024,
        userId,
        purpose: purpose as ImageGenerationPurpose,
        storeTemporarily: storeTemporarily === true // Convertir en booléen explicite
      });

      imageUrl = result.imageUrl;
      imageBase64 = result.imageBase64;
    } catch (generationError: any) {
      // RESTITUER LES CRÉDITS en cas d'erreur de génération
      logger.error('Erreur lors de la génération d\'image, restitution des crédits:', generationError);
      if (currentCredits !== undefined) {
        await restoreAiCredits(userId!, currentCredits);
      }

      // Retourner directement l'erreur sans la relancer pour éviter la double restitution
      return res.status(500).json({
        success: false,
        message: `Erreur lors de la génération d'image: ${generationError.message}`,
        toastType: 'error'
      });
    }

    // L'enregistrement dans l'historique a déjà été fait lors de la déduction des crédits

    // Enregistrer l'activité
    await logUserActivity(
      userId,
      'ai_credit_used',
      undefined,
      'ai_image_generation',
      JSON.stringify({ purpose, credits_remaining: newCredits }),
      getIpFromRequest(req)
    );

    logger.info('Génération d\'image IA : Image générée avec succès');

    // Retourner l'URL de l'image générée
    return res.status(200).json({
      success: true,
      imageUrl,
      imageBase64,
      creditsRemaining: newCredits,
      prompt: basePrompt,
      midjourneyPrompt,
      toastType: 'success'
    });
  } catch (error: any) {
    logger.error('Erreur lors de la génération d\'image IA:', error);

    // Si les crédits ont été débités et qu'une erreur survient AVANT la génération, les restituer
    // (Les erreurs de génération sont déjà gérées dans le catch spécifique)
    if (creditsDebited && userId && currentCredits !== undefined && !error.message?.includes('génération d\'image')) {
      logger.warn('Restitution des crédits suite à une erreur générale (non-génération)');
      await restoreAiCredits(userId, currentCredits);
    }

    return res.status(500).json({
      success: false,
      message: `Erreur lors de la génération d'image: ${error.message}`,
      toastType: 'error'
    });
  }
};

/**
 * Confirme et déplace une image générée du stockage temporaire vers le stockage définitif
 */
export const confirmGeneratedImage = async (req: Request, res: Response) => {
  try {
    // Récupérer l'ID utilisateur depuis req.user
    const userId = req.user?.id || req.user?.userId;

    if (!userId) {
      return res.status(401).json({
        success: false,
        message: 'Utilisateur non authentifié',
        toastType: 'error'
      });
    }

    // Valider les données de la requête
    const { imageUrl, purpose, galleryId, galleryName, galleryDescription } = req.body;

    if (!imageUrl || !purpose) {
      return res.status(400).json({
        success: false,
        message: 'URL de l\'image et type d\'image requis',
        toastType: 'error'
      });
    }

    if (!['profile_picture', 'banner_picture', 'mission_image', 'gallery_photo', 'featured_photo', 'card_editor', 'carte_visite_et_flyer'].includes(purpose)) {
      return res.status(400).json({
        success: false,
        message: 'Type d\'image invalide',
        toastType: 'error'
      });
    }

    // Vérifier si l'URL provient bien du bucket temporaire
    const tempBucketUrl = '/api/storage-proxy/temp_moderation/';
    if (!imageUrl.includes(tempBucketUrl)) {
      return res.status(400).json({
        success: false,
        message: 'L\'image n\'est pas stockée dans le bucket temporaire',
        toastType: 'error'
      });
    }

    // Extraire le chemin du fichier dans le bucket temporaire
    const filePath = imageUrl.split(tempBucketUrl)[1];
    if (!filePath) {
      return res.status(400).json({
        success: false,
        message: 'Impossible de déterminer le chemin du fichier',
        toastType: 'error'
      });
    }

    // Télécharger l'image depuis le bucket temporaire
    const { data: fileData, error: downloadError } = await supabase.storage
      .from('temp_moderation')
      .download(filePath);

    if (downloadError || !fileData) {
      logger.error('Erreur lors du téléchargement de l\'image temporaire', { downloadError, timestamp: Date.now() });
      return res.status(500).json({
        success: false,
        message: 'Erreur lors de la récupération de l\'image temporaire',
        toastType: 'error'
      });
    }

    // Convertir en buffer
    const buffer = Buffer.from(await fileData.arrayBuffer());
    const mimeType = fileData.type || 'image/jpeg';

    // Stocker l'image dans le bucket approprié selon le purpose
    let finalImageUrl = '';
    let featuredPhotoObj = null;
    let avatarUrl = null;
    let responseType = 'default';
    try {
      switch (purpose) {
        case 'profile_picture':
          finalImageUrl = await uploadProfilPhoto(userId, buffer, mimeType, { addIaTag: true });
          avatarUrl = finalImageUrl;
          responseType = 'avatar';
          break;
        case 'banner_picture':
          finalImageUrl = await uploadBannerPhoto(userId, buffer, mimeType, { addIaTag: true });
          responseType = 'banner';
          break;
        case 'mission_image':
          const tempMissionId = `ai_generated_${Date.now()}`;
          finalImageUrl = await uploadMissionPhoto(userId, buffer, mimeType, tempMissionId, { addIaTag: true });
          break;
        case 'gallery_photo':
          // Utiliser le vrai galleryId si fourni, sinon fallback sur un id temporaire
          const finalGalleryId = galleryId || `ai_generated_${Date.now()}`;
          finalImageUrl = await uploadGalleryPhoto(userId, buffer, mimeType, finalGalleryId, galleryName, { addIaTag: true });

          // Ajouter l'image à la galerie dans la base de données
          const { data: galleryPhoto, error: galleryError } = await supabase
            .from('user_gallery_photos')
            .insert([{
              gallery_id: finalGalleryId,
              user_id: userId,
              photo_url: finalImageUrl,
              caption: null,
              created_at: new Date().toISOString(),
              updated_at: new Date().toISOString()
            }])
            .select()
            .single();

          if (galleryError) {
            logger.error('Erreur lors de l\'insertion dans user_gallery_photos', { galleryError });
            return res.status(500).json({
              message: "Erreur lors de l'enregistrement de la photo de galerie",
              success: false,
              toastType: 'error'
            });
          }

          featuredPhotoObj = {
            id: galleryPhoto?.id,
            photo_url: finalImageUrl,
            caption: galleryPhoto?.caption,
            created_at: galleryPhoto?.created_at,
            updated_at: galleryPhoto?.updated_at,
            order_index: galleryPhoto?.order_index
          };
          responseType = 'gallery';
          break;
        case 'featured_photo':
          finalImageUrl = await uploadGalleryPhoto(userId, buffer, mimeType, 'featured', 'photos_mises_en_avant', { addIaTag: true });
          // Insertion dans la table user_featured_photos
          const storagePath = finalImageUrl.split('galerie_realisation_client/')[1];
          const { data: photo, error: dbError } = await supabase
            .from('user_featured_photos')
            .insert([{
              user_id: userId,
              photo_url: finalImageUrl,
              storage_path: storagePath
            }])
            .select()
            .single();
          if (dbError) {
            logger.error('Erreur lors de l\'insertion dans user_featured_photos', { dbError });
            return res.status(500).json({
              message: "Erreur lors de l'enregistrement de la photo mise en avant",
              success: false
            });
          }
          featuredPhotoObj = {
            id: photo?.id,
            photo_url: finalImageUrl,
            caption: photo?.caption
          };
          responseType = 'featured';
          break;
        case 'card_editor':
          // Importer la fonction d'upload pour les cartes de visite et flyers
          const { uploadCardEditorImage } = require('../services/storage');
          // Récupérer le templateId transmis par le frontend
          const templateId = req.body.templateId || '';
          // Ne PAS ajouter le tag IA pour les cartes/flyers
          finalImageUrl = await uploadCardEditorImage(userId, buffer, mimeType, templateId, { addIaTag: false });
          break;
        case 'carte_visite_et_flyer':
          // Même logique que 'card_editor' mais pour le nouveau purpose
          const { uploadCardEditorImage: uploadCardEditorImageCVF } = require('../services/storage');
          const templateIdCVF = req.body.templateId || '';
          finalImageUrl = await uploadCardEditorImageCVF(userId, buffer, mimeType, templateIdCVF, { addIaTag: false });
          break;
        default:
          logger.error('[DEBUG][CONFIRM_IMAGE] Type d\'image non supporté', { purpose, timestamp: Date.now() });
          return res.status(400).json({
            success: false,
            message: 'Type d\'image non supporté',
            toastType: 'error'
          });
      }
    } catch (err) {
      const error = err instanceof Error ? err : new Error(String(err));
      logger.error('[DEBUG][CONFIRM_IMAGE] Erreur lors de l\'upload dans le bucket définitif', { error, purpose, timestamp: Date.now() });
      return res.status(500).json({
        success: false,
        message: `Erreur lors de l'upload dans le bucket définitif: ${error.message}`,
        toastType: 'error'
      });
    }

    // Supprimer l'image temporaire
    try {
      await supabase.storage
        .from('temp_moderation')
        .remove([filePath]);
    } catch (err) {
      const error = err instanceof Error ? err : new Error(String(err));
      // On continue quand même
    }

    // Enregistrer l'activité
    try {
      await logUserActivity(
        userId,
        'image_confirmed',
        undefined,
        'ai_image_generation',
        JSON.stringify({ purpose }),
        getIpFromRequest(req)
      );
      logger.info('[DEBUG][CONFIRM_IMAGE] Image IA confirmée et déplacée avec succès', {
        userId,
        purpose,
        tempUrl: imageUrl,
        finalUrl: finalImageUrl,
        timestamp: Date.now()
      });
    } catch (err) {
      const error = err instanceof Error ? err : new Error(String(err));
      logger.error('[DEBUG][CONFIRM_IMAGE] Erreur lors du log d\'activité', { error, userId, purpose });
    }

    // Retourner la réponse adaptée
    if (responseType === 'avatar') {
      return res.status(200).json({
        success: true,
        avatarUrl,
        finalImageUrl,
        toastType: 'success'
      });
    } else if (responseType === 'featured') {
      return res.json({
        success: true,
        photo: featuredPhotoObj
      });
    } else if (responseType === 'gallery') {
      return res.json({
        success: true,
        photo: featuredPhotoObj,
        finalImageUrl,
        toastType: 'success'
      });
    } else {
      return res.status(200).json({
        success: true,
        finalImageUrl,
        toastType: 'success'
      });
    }
  } catch (error: any) {
    logger.error('[DEBUG][CONFIRM_IMAGE] Erreur lors de la confirmation de l\'image IA:', { error, timestamp: Date.now() });
    return res.status(500).json({
      success: false,
      message: `Erreur lors de la confirmation de l'image: ${error.message}`,
      toastType: 'error'
    });
  }
};
