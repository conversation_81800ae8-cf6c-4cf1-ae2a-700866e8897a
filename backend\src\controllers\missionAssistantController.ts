import { Request, Response } from 'express';
import axios from 'axios';
import logger from '../utils/logger';
import { redis } from '../config/redis';
import { supabase } from '../config/supabase';
import { getIpFromRequest } from '../utils/activityLogger';
import { logUserActivity } from '../utils/activityLogger';
import { logOpenRouterUsage } from '../services/contentModerationService';
import { decryptProfilDataAsync } from '../utils/encryption';
import { getServiceCategories, getServiceSubcategories } from '../services/serviceCategoriesService';
import { selectAIModel } from './openRouterController';

// URL et clé API OpenRouter
const AI_API_URL = process.env.MODERATION_API_URL || 'https://api.openrouter.ai/api/v1/chat/completions';
const AI_API_KEY = process.env.MODERATION_API_KEY || '';
const AI_API_MODEL_FREE = process.env.MODERATION_API_MODEL_FREE || 'meta-llama/llama-3.3-70b-instruct:free';
const AI_API_MODEL_PAYANT = process.env.MODERATION_API_MODEL_PAYANT || 'google/gemini-2.5-flash-lite';
const DAILY_CALLS_LIMIT = 999;

// Constantes pour le cache Redis
const CACHE_PREFIX = 'ai_generation:';
const USER_CREDITS_CACHE_PREFIX = `ai_credits:`; // Format existant pour la compatibilité
const CACHE_TTL_SHORT = 60 * 5; // 5 minutes

// Timeout pour les appels API
const TIMEOUT_API = 15000;

/**
 * Fonction utilitaire pour restituer les crédits IA en cas d'erreur
 */
const restoreAiCredits = async (userId: string, originalCredits: number, creditsCost: number): Promise<void> => {
  try {
    const creditsCacheKey = `${USER_CREDITS_CACHE_PREFIX}${userId}`;

    // Restaurer les crédits dans la base de données
    const { error: restoreError } = await supabase
      .from('user_ai_credits')
      .update({ credits: originalCredits })
      .eq('user_id', userId);

    if (restoreError) {
      logger.error('Erreur lors de la restitution des crédits IA:', restoreError);
    } else {
      logger.info(`Crédits IA restitués pour l'utilisateur ${userId}: ${originalCredits}`);
    }

    // Restaurer le cache
    await redis.setex(creditsCacheKey, CACHE_TTL_SHORT, originalCredits.toString());

    // Enregistrer la restitution dans l'historique
    try {
      const { error: historyError } = await supabase
        .from('user_ai_credits_historique')
        .insert({
          user_id: userId,
          operation_type: 'autre',
          montant: creditsCost,
          solde_avant: originalCredits - creditsCost,
          solde_apres: originalCredits,
          description: `Restitution suite à erreur de génération d'assistant mission`,
          created_at: new Date().toISOString()
        });

      if (historyError) {
        logger.error("Erreur lors de l'enregistrement de la restitution dans l'historique:", historyError);
      }
    } catch (historyError) {
      logger.error("Erreur lors de l'enregistrement de la restitution dans l'historique:", historyError);
    }
  } catch (error) {
    logger.error('Erreur critique lors de la restitution des crédits IA:', error);
  }
};

// Fonction utilitaire pour nettoyer les balises markdown autour du JSON
function cleanMarkdownJson(text: string): string {
  let cleanedText = text.trim();
  // Supprimer les délimiteurs Markdown d'ouverture (```json ou ```)
  const markdownStartMatch = cleanedText.match(/^```(json)?\s*\n/);
  if (markdownStartMatch) {
    cleanedText = cleanedText.substring(markdownStartMatch[0].length);
  }
  // Supprimer les délimiteurs Markdown de fermeture (```)
  cleanedText = cleanedText.replace(/```\s*$/g, '').trim();
  return cleanedText;
}

/**
 * Génère une étape de mission avec l'IA
 */
const generateMissionStep = async (req: Request, res: Response) => {
  // Déclarer les variables au niveau de la fonction pour qu'elles soient accessibles dans le catch
  let userId: string | undefined;
  let currentCredits: number | undefined;
  let creditsDebited = false;
  let creditCost: number = 1; // Valeur par défaut, sera mise à jour selon le type de génération

  try {
    // Récupérer l'ID utilisateur depuis req.user
    userId = req.user?.id || req.user?.userId;

    if (!userId) {
      return res.status(401).json({
        success: false,
        message: 'Utilisateur non authentifié',
        toastType: 'error'
      });
    }

    // Valider les données de la requête
    const { step, userInput, currentData, isFullGeneration } = req.body;

    if (!step || !userInput) {
      return res.status(400).json({
        success: false,
        message: 'Données invalides',
        toastType: 'error'
      });
    }

    // Déterminer le coût en crédits - toujours 1 crédit par étape
    creditCost = 1;

    logger.info('Assistant Mission IA : Début de génération pour l\'utilisateur:', userId);

    // Vérifier si l'utilisateur a des crédits IA (dans le cache)
    const creditsCacheKey = `${USER_CREDITS_CACHE_PREFIX}${userId}`;

    // Essayer de récupérer les crédits depuis le cache
    const cachedCredits = await redis.get(creditsCacheKey);

    if (cachedCredits !== null) {
      // Utiliser les crédits du cache
      currentCredits = parseInt(cachedCredits, 10);
      logger.info(`Crédits IA récupérés depuis le cache pour l'utilisateur ${userId}: ${currentCredits}`);
    } else {
      // Récupérer les crédits depuis la base de données
      const { data: creditsData, error: creditsError } = await supabase
        .from('user_ai_credits')
        .select('credits')
        .eq('user_id', userId)
        .single();

      if (creditsError && creditsError.code !== 'PGRST116') {
        logger.error('Erreur lors de la vérification des crédits IA:', creditsError);
        return res.status(500).json({
          success: false,
          message: 'Erreur lors de la vérification des crédits IA',
          toastType: 'error'
        });
      }

      currentCredits = creditsData?.credits || 0;

      // Mettre en cache les crédits
      await redis.setex(creditsCacheKey, CACHE_TTL_SHORT, currentCredits!.toString());
      logger.info(`Crédits IA mis en cache pour l'utilisateur ${userId}: ${currentCredits}`);
    }

    // Vérifier si l'utilisateur a suffisamment de crédits
    if (!currentCredits || currentCredits < creditCost) {
      return res.status(400).json({
        success: false,
        message: `Vous n'avez pas assez de crédits IA. Cette opération nécessite ${creditCost} crédit${creditCost > 1 ? 's' : ''}. Veuillez en acheter dans le menu "Intelligence Artificielle"`,
        toastType: 'error'
      });
    }

    // Déduire les crédits IA AVANT la génération pour éviter les abus
    const newCredits = currentCredits - creditCost;

    // Mettre à jour les crédits dans la base de données
    const { error: updateError } = await supabase
      .from('user_ai_credits')
      .update({
        credits: newCredits,
        updated_at: new Date().toISOString()
      })
      .eq('user_id', userId);

    if (updateError) {
      logger.error('Erreur lors de la mise à jour des crédits IA:', updateError);
      return res.status(500).json({
        success: false,
        message: 'Erreur lors de la déduction des crédits IA',
        toastType: 'error'
      });
    }

    // Mettre à jour le cache
    await redis.setex(creditsCacheKey, CACHE_TTL_SHORT, newCredits.toString());

    // Enregistrer immédiatement la déduction dans l'historique
    try {
      const { error: historyError } = await supabase
        .from('user_ai_credits_historique')
        .insert({
          user_id: userId,
          operation_type: 'utilisation',
          montant: creditCost,
          solde_avant: currentCredits,
          solde_apres: newCredits,
          description: `Assistant mission IA - ${isFullGeneration ? 'Génération complète' : 'Étape'}`,
          created_at: new Date().toISOString()
        });

      if (historyError) {
        logger.error("Erreur lors de l'enregistrement de la déduction dans l'historique:", historyError);
        // Continuer malgré l'erreur
      }
    } catch (historyError) {
      logger.error("Erreur lors de l'enregistrement de la déduction dans l'historique:", historyError);
      // Continuer malgré l'erreur
    }

    // Marquer que les crédits ont été débités
    creditsDebited = true;

    // Récupérer les données du profil détaillé
    const { data: userProfile, error: profileError } = await supabase
      .from('user_profil')
      .select('*')
      .eq('user_id', userId)
      .single();

    // Déchiffrer les données du profil
    const decryptedUserProfile = userProfile ? await decryptProfilDataAsync(userProfile) : null;

    if (profileError && profileError.code !== 'PGRST116') {
      logger.error('Erreur lors de la récupération du profil utilisateur:', profileError);
      // Continuer même si le profil n'est pas trouvé
    }

    // Récupérer les catégories et sous-catégories de services dynamiquement
    const categories = await getServiceCategories();
    const subcategories = await getServiceSubcategories();

    // Obtenir la date et l'heure actuelles
    const now = new Date();
    const dateOptions: Intl.DateTimeFormatOptions = {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      timeZone: 'Europe/Paris'
    };
    const timeOptions: Intl.DateTimeFormatOptions = {
      hour: '2-digit',
      minute: '2-digit',
      timeZone: 'Europe/Paris'
    };
    const currentDate = now.toLocaleDateString('fr-FR', dateOptions);
    const currentTime = now.toLocaleTimeString('fr-FR', timeOptions);

    // Construire le prompt pour l'IA
    const prompt = `
      Tu es un assistant pour la création de mission sur JobPartiel, une plateforme de services entre particuliers.

      Date et heure actuelles: ${currentDate} à ${currentTime}

      L'utilisateur a décrit son besoin: "${userInput}"

      Informations sur l'utilisateur:
      - Nom: ${decryptedUserProfile?.prenom || ''} ${decryptedUserProfile?.nom || ''}
      - Ville: ${decryptedUserProfile?.ville || ''}
      - Code postal: ${decryptedUserProfile?.code_postal || ''}
      - Adresse: ${decryptedUserProfile?.adresse || ''}

      Données déjà collectées:
      ${JSON.stringify(currentData || {})}

      Étape actuelle: ${step}

      Pour cette étape, tu dois analyser le besoin de l'utilisateur et générer les informations appropriées au format JSON.

      ${step === 'Description du besoin' ? `
        Tu es un expert en rédaction de descriptions de missions pour JobPartiel. Ta tâche est de créer une description professionnelle, détaillée et attrayante qui aidera l'utilisateur à trouver rapidement un prestataire qualifié.  
        
        INSTRUCTIONS DÉTAILLÉES:
        1. Génère un titre court (max 70 caractères) qui soit accrocheur, précis et qui mette en avant le service principal demandé
        2. Crée une description détaillée (max 1200 caractères) qui:
           - Commence par une phrase d'accroche qui résume clairement le besoin
           - Détaille précisément les tâches à accomplir avec des paragraphes bien structurés
           - Inclut toutes les spécifications techniques pertinentes (dimensions, matériaux, équipements, etc.)
           - Mentionne les compétences ou qualifications requises pour le prestataire
           - Précise le niveau de qualité attendu et les délais si mentionnés
           - Utilise un ton professionnel mais accessible
           - Évite tout langage vague ou générique
        3. Détermine si la mission est urgente en analysant minutieusement le langage utilisé

        Règles pour is_urgent:
        - Si l'utilisateur utilise des mots comme "urgent", "rapidement", "dès que possible", "aujourd'hui", "immédiatement", mets is_urgent à true
        - Si l'utilisateur mentionne une date proche (aujourd'hui ou demain) avec un ton pressant, mets is_urgent à true
        - Si l'utilisateur a explicitement coché la case "Mission urgente" dans l'interface (${currentData?.is_urgent === true ? 'oui' : 'non'}), utilise cette valeur
        - Sinon, mets is_urgent à false

        RÈGLES DE CONFIDENTIALITÉ:
        - Ne jamais inclure d'adresse complète dans la description
        - Ne jamais inclure de numéro de téléphone dans la description
        - Si l'utilisateur mentionne une adresse, utilise uniquement la ville (ex: "à Perpignan")
        - Concentre-toi sur la description du service demandé, pas sur les informations personnelles

        CONSERVATION DES INFORMATIONS IMPORTANTES:
        - Si l'utilisateur mentionne un budget précis, inclus-le OBLIGATOIREMENT dans la description (ex: "Budget de 40€ pour 6 heures")
        - Si l'utilisateur mentionne une durée, inclus-la OBLIGATOIREMENT dans la description
        - Si l'utilisateur mentionne des dates ou horaires spécifiques, inclus-les OBLIGATOIREMENT dans la description
        - Si l'utilisateur mentionne des matériaux, outils ou équipements spécifiques, inclus-les OBLIGATOIREMENT dans la description
        - Si l'utilisateur mentionne des compétences ou qualifications requises, inclus-les OBLIGATOIREMENT dans la description
        - Conserve toutes les informations importantes fournies par l'utilisateur dans ta description afin de générer une description la plus précise possible de son besoin

        INTERDICTION ABSOLUE DE TOUT COMMENTAIRE SUR L'ABSENCE D'INFORMATION:
        - Si une information (budget, durée, etc.) n'est pas connue, ne commente pas l'absence d'information.

        EXEMPLES DE TITRES EFFICACES:
        - "Création site web avec système de réservation pour restaurant"
        - "Tonte de pelouse et taille de haies sur terrain de 500m²"
        - "Garde de chat affectueux pendant 2 semaines en août"

        EXEMPLES DE DESCRIPTIONS EFFICACES:
        - "Je recherche un développeur web expérimenté pour créer un site vitrine avec système de réservation en ligne pour mon restaurant. Le site doit inclure: page d'accueil avec photos, menu interactif, système de réservation de table avec confirmation par email, page contact et formulaire. Design moderne et épuré souhaité, compatible mobile et tablette. Compétences requises: HTML/CSS, JavaScript, PHP ou équivalent. Le site doit être livré avec documentation et formation d'une heure pour la gestion du contenu. Budget de 800€ pour l'ensemble du projet."
        - "Besoin d'un jardinier qualifié pour entretenir mon jardin de 500m² à Perpignan. Les travaux comprennent: tonte complète de la pelouse, taille des 6 haies de cyprès (hauteur 2m), désherbage des massifs de fleurs et nettoyage complet après intervention. Le jardin n'a pas été entretenu depuis 2 mois. Matériel non fourni, merci de venir avec votre équipement. Intervention souhaitée le week-end prochain, durée estimée: 4-5 heures. Budget: 120€ pour l'ensemble des travaux."

        Réponds uniquement au format JSON suivant:
        {
          "titre": "Titre court et précis",
          "description": "Description détaillée du besoin",
          "is_urgent": true/false
        }
      ` : ''}

      ${step === 'Catégorie et service' ? `
        Détermine la catégorie et sous-catégorie de service qui correspond le mieux au besoin décrit.

        Voici les catégories disponibles:
        ${categories?.map(c => `- ID: "${c.id}", Nom: "${c.nom}"`).join('\n        ') || ''}

        Voici les sous-catégories disponibles:
        ${subcategories?.map(sc => `- ID: "${sc.id}", Category ID: "${sc.categoryId}", Nom: "${sc.nom}"`).join('\n        ') || ''}

        INSTRUCTIONS STRICTES:
        1. Tu DOIS choisir une catégorie et une sous-catégorie parmi les listes ci-dessus.
        2. Pour category_id, utilise UNIQUEMENT l'ID exact d'une des catégories (ex: "1").
        3. Pour subcategory_id, utilise UNIQUEMENT l'ID exact d'une sous-catégorie (ex: "1-1").
        4. Ne modifie PAS les IDs, utilise-les tels quels.
        5. Assure-toi que la sous-catégorie appartient bien à la catégorie choisie.
        6. Réponds UNIQUEMENT avec un objet JSON contenant category_id et subcategory_id.

        Format de réponse OBLIGATOIRE:
        {
          "category_id": "X",
          "subcategory_id": "X-Y"
        }

        Exemple: Pour du jardinage/tonte de pelouse:
        {
          "category_id": "1",
          "subcategory_id": "1-1"
        }

        N'ajoute AUCUN texte explicatif, UNIQUEMENT l'objet JSON.
      ` : ''}

      ${step === 'Budget' ? `
        Détermine le budget pour cette mission et la méthode de paiement préférée.

        Règles pour budget_defini:
        - Si l'utilisateur mentionne explicitement un montant précis (ex: "200€" ou "150 jobi"), mets budget_defini à true.
        - Si l'utilisateur donne une fourchette de prix (ex: "entre 100 et 200€") ou utilise des termes comme "environ", "à peu près", mets budget_defini à false.
        - Si aucun budget n'est mentionné, mets budget à 0 et budget_defini à false.

        Règles pour payment_method:
        - Si l'utilisateur mentionne explicitement "jobi" ou "échange" UNIQUEMENT, utilise "jobi".
        - Si l'utilisateur mentionne explicitement "euros" ou "€" UNIQUEMENT, utilise "euros".
        - Si l'utilisateur mentionne les deux options OU indique qu'il accepte les deux modes de paiement, utilise "both".
        - Si l'utilisateur utilise des termes comme "au choix", "peu importe", "l'un ou l'autre", utilise "both".
        - Si la description contient "paiement en jobi" ou similaire, utilise "jobi".
        - Si aucune méthode n'est spécifiée, utilise "euros" par défaut.

        ${currentData && currentData.extracted_data && currentData.extracted_data.budget ?
          `IMPORTANT: L'utilisateur a déjà mentionné un budget de ${currentData.extracted_data.budget} ${currentData.extracted_data.payment_method || 'euros'} dans sa description.
           Utilise cette information comme point de départ, sauf si l'utilisateur a fourni des détails différents dans son input actuel.` : ''}

        Réponds uniquement au format JSON suivant:
        {"budget": ${currentData && currentData.extracted_data && currentData.extracted_data.budget ? currentData.extracted_data.budget : 0}, "budget_defini": ${currentData && currentData.extracted_data && currentData.extracted_data.budget ? 'true' : 'false'}, "payment_method": "${currentData && currentData.extracted_data && currentData.extracted_data.payment_method ? currentData.extracted_data.payment_method : 'euros'}"}

        Note: payment_method peut être "jobi" (système de troc/échange), "euros" (paiement direct) ou "both" (les deux options acceptées)
      ` : ''}

      ${step === 'Localisation' ? `
        Détermine l'adresse où la mission doit être effectuée.

        IMPORTANT:
        - Analyse attentivement le message original de l'utilisateur pour y trouver une adresse
        - Si l'utilisateur a mentionné une adresse complète dans son message, utilise-la
        - Si l'adresse n'est pas spécifiée, utilise l'adresse de l'utilisateur
        - Extrait le numéro, la rue, le code postal et la ville si ces informations sont présentes
        - Assure-toi que l'adresse est complète et précise pour permettre sa validation ultérieure

        Message original de l'utilisateur: "${userInput}"

        Réponds uniquement au format JSON suivant:
        {"adresse": "Adresse complète", "code_postal": "Code postal", "ville": "Ville", "intervention_zone": {"center": [latitude, longitude], "radius": 15}}

        Exemple si l'utilisateur a mentionné "12 rue des Lilas à Perpignan":
        {"adresse": "12 rue des Lilas", "code_postal": "${decryptedUserProfile?.code_postal || ''}", "ville": "Perpignan", "intervention_zone": {"center": [42.6886, 2.8948], "radius": 15}}

        Exemple si aucune adresse n'est mentionnée:
        {"adresse": "${decryptedUserProfile?.adresse || ''}", "code_postal": "${decryptedUserProfile?.code_postal || ''}", "ville": "${decryptedUserProfile?.ville || ''}", "intervention_zone": {"center": [], "radius": 15}}

        Note: Si tu ne peux pas déterminer les coordonnées exactes, laisse le tableau center vide.
      ` : ''}

      ${step === 'Horaires' ? `
        Détermine si l'utilisateur a des préférences horaires pour sa mission.
        Si oui, génère des créneaux horaires appropriés.

        IMPORTANT - INTERPRÉTATION DES DATES:
        - Utilise la date actuelle (${currentDate}) comme référence
        - Si l'utilisateur mentionne "la semaine prochaine", calcule les dates correctement
        - Si l'utilisateur mentionne "vendredi prochain", détermine la date exacte
        - Si l'utilisateur mentionne "ce week-end", détermine les dates exactes (samedi et dimanche prochains uniquement, donc celui qui est le plus proche)
        - Le "week-end" correspond STRICTEMENT au samedi et dimanche, JAMAIS au vendredi ou au lundi
        - Convertis toutes les références temporelles en dates précises

        Exemples d'interprétation:
        - "vendredi prochain" → date exacte du prochain vendredi
        - "la semaine prochaine" → dates de la semaine suivante
        - "dans deux jours" → date exacte dans deux jours
        - "ce week-end" → le samedi et dimanche prochains uniquement

        RÈGLES POUR LE CHAMP date_mission:
        - Si l'utilisateur a une préférence horaire (has_time_preference = true), le champ date_mission DOIT contenir la date du premier créneau horaire
        - Si plusieurs créneaux sont spécifiés, utilise la date du premier créneau pour date_mission
        - Ne laisse JAMAIS le champ date_mission vide si has_time_preference = true

        Réponds uniquement au format JSON suivant:
        {"date_mission": "YYYY-MM-DD", "has_time_preference": true, "time_slots": [{"date": "YYYY-MM-DD", "start": "HH:MM", "end": "HH:MM"}]}

        Si pas de préférence horaire:
        {"date_mission": "", "has_time_preference": false, "time_slots": []}
      ` : ''}

      ${step === 'Résumé' ? `
        Génère un résumé textuel de la mission complète avec toutes les informations collectées.
        Ce résumé sera affiché à l'utilisateur pour validation.

        IMPORTANT:
        - Le résumé doit être un texte formaté en français, PAS un objet JSON
        - Présente les informations de manière claire et structurée
        - Inclus toutes les informations importantes: titre, description, catégorie, budget, localisation, dates et horaires
        - Si la mission est urgente (${currentData?.is_urgent === true ? 'oui' : 'non'}), mentionne-le clairement au début du résumé
        - Utilise un langage simple et direct
        - Formate le texte pour qu'il soit facile à lire (paragraphes, puces, etc.)
        - N'inclus PAS les identifiants techniques (category_id, subcategory_id)
        - Utilise les noms des catégories et sous-catégories, pas leurs IDs

        Exemple de format attendu:
        "Vous avez créé une mission de baby-sitting pour garder votre nièce sur deux soirées: mardi 13 mai de 20h à 2h du matin et jeudi 15 mai de 22h à 2h du matin. La mission se déroulera au 16 rue des fleurs à Perpignan. Vous proposez un budget de 240€ pour ces 6 heures de garde."

        Réponds uniquement au format JSON suivant:
        {"summary": "Texte du résumé formaté comme dans l'exemple ci-dessus"}
      ` : ''}

      Réponds UNIQUEMENT avec un objet JSON valide correspondant à l'étape demandée, sans aucun texte supplémentaire.
      Assure-toi que le JSON est correctement formaté et contient toutes les propriétés requises pour l'étape.
      N'ajoute pas de commentaires ou d'explications en dehors de l'objet JSON.
    `;

    logger.info('Assistant Mission IA : Lancement de la génération avec le prompt : ' + prompt.slice(0, 20000));

    // Appeler l'API OpenRouter
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), TIMEOUT_API);

    try {
      let AI_API_MODEL = await selectAIModel(false);
      let response;
      let usedFallback = false;
      // Factorisation du payload pour éviter la duplication du prompt
      const getAIPayload = (model: string) => ({
        model,
        messages: [
          {
            role: "system",
            content: `Tu es un assistant spécialisé dans la création de missions sur JobPartiel.

            Tu dois répondre UNIQUEMENT au format JSON, sans aucun texte explicatif.

            Pour l'étape "Catégorie et service", tu dois absolument utiliser les IDs exacts fournis dans les listes, sans les modifier.
            - Exemple correct: {"category_id": "1", "subcategory_id": "1-1"}
            - Exemple incorrect: {"category_id": "ID de la catégorie", "subcategory_id": "ID de la sous-catégorie"}

            Pour l'étape "Résumé", tu dois générer un texte lisible en français qui résume toutes les informations de la mission:
            - Le résumé doit être un texte formaté, PAS un objet JSON échappé
            - N'inclus PAS les identifiants techniques (category_id, subcategory_id)
            - Utilise les noms des catégories et sous-catégories, pas leurs IDs
            - Présente les informations de manière claire et structurée
            - Exemple: {"summary": "Vous avez créé une mission de jardinage pour tondre votre pelouse le samedi 10 mai à 14h. La mission se déroulera à votre domicile à Paris. Vous proposez un budget de 50€."}

            Pour l'étape "Budget", respecte strictement ces règles:
            - Si l'utilisateur mentionne explicitement "jobi" ou "échange" UNIQUEMENT, utilise "jobi" comme méthode de paiement.
            - Si l'utilisateur mentionne explicitement "euros" ou "€" UNIQUEMENT, utilise "euros" comme méthode de paiement.
            - Si l'utilisateur mentionne les deux options OU indique qu'il accepte les deux modes de paiement, utilise "both".
            - Si l'utilisateur utilise des termes comme "au choix", "peu importe", "l'un ou l'autre", utilise "both".
            - Si la description contient "paiement en jobi" ou similaire, utilise "jobi".
            - Si aucune méthode n'est spécifiée, utilise "euros" par défaut.

            Pour l'étape "Description du besoin", respecte strictement ces règles:
            - Ne jamais inclure d'adresse complète dans la description
            - Ne jamais inclure de numéro de téléphone dans la description
            - Si l'utilisateur mentionne une adresse, utilise uniquement la ville (ex: "à Perpignan")

            Pour l'étape "Localisation", respecte strictement ces règles:
            - Analyse attentivement le message original de l'utilisateur pour y trouver une adresse
            - Si l'utilisateur a mentionné une adresse complète, utilise-la même si elle n'apparaît pas dans la description
            - Extrait le numéro, la rue, le code postal et la ville si ces informations sont présentes

            Pour l'étape "Horaires", respecte strictement ces règles:
            - Utilise la date actuelle comme référence pour interpréter les expressions temporelles
            - Convertis les expressions comme "la semaine prochaine", "vendredi prochain" en dates précises
            - Fournis toujours des dates au format YYYY-MM-DD et des heures au format HH:MM
            - Si has_time_preference = true, le champ date_mission DOIT contenir la date du premier créneau horaire
            - Ne laisse JAMAIS le champ date_mission vide si has_time_preference = true
            - ATTENTION: Le "week-end" correspond STRICTEMENT au samedi et dimanche, JAMAIS au vendredi ou au lundi
            - Quand l'utilisateur mentionne "ce week-end", génère UNIQUEMENT des créneaux pour le samedi et/ou dimanche prochain

            N'utilise jamais de texte descriptif à la place des IDs numériques.`
          },
          {
            role: "user",
            content: prompt
          }
        ],
        temperature: 0.1,
        top_p: 0.35,
        frequency_penalty: 0.0,
        presence_penalty: 0.0,
        max_tokens: 1500,
        response_format: { type: "json_object" }
      });
      const axiosConfig = {
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${AI_API_KEY}`,
          'HTTP-Referer': 'https://jobpartiel.fr',
          'X-Title': 'JobPartiel Mission Assistant'
        },
        signal: controller.signal,
        timeout: TIMEOUT_API
      };
      try {
        response = await axios.post(
          AI_API_URL,
          getAIPayload(AI_API_MODEL),
          axiosConfig
        );
        // Fallback si le modèle gratuit retourne une erreur 503 provider dans le body
        if (
          !usedFallback &&
          response?.data?.error &&
          response.data.error.code === 503
        ) {
          logger.warn('Erreur 503 provider détectée, tentative avec le modèle payant.');
          AI_API_MODEL = AI_API_MODEL_PAYANT;
          usedFallback = true;
          response = await axios.post(
            AI_API_URL,
            getAIPayload(AI_API_MODEL),
            axiosConfig
          );
        }
      } catch (err: any) {
        // Si erreur 429 ou 400, on tente une fois avec la clé payante
        if (err.response && (err.response.status === 429 || err.response.status === 400) && !usedFallback) {
          logger.warn(`Erreur ${err.response.status} détectée, tentative avec le modèle payant.`);
          AI_API_MODEL = AI_API_MODEL_PAYANT;
          usedFallback = true;
          response = await axios.post(
            AI_API_URL,
            getAIPayload(AI_API_MODEL),
            axiosConfig
          );
        } else {
          throw err;
        }
      }

      // Extraire le contenu généré
      const generatedContent = response.data.choices[0].message.content;

      // Nettoyer le contenu généré des balises markdown éventuelles
      const cleanedContent = cleanMarkdownJson(generatedContent);

      // Vérifier que le contenu est bien un JSON valide
      try {
        const parsedContent = JSON.parse(cleanedContent);
        logger.info(`Contenu JSON généré et validé: ${JSON.stringify(parsedContent)}`);

        // Si c'est l'étape "Horaires", vérifier que les dates de week-end sont correctes
        if (step === 'Horaires' && parsedContent.has_time_preference === true) {
          // Vérifier si l'utilisateur a mentionné "ce week-end" ou "weekend" dans son input
          const mentionsWeekend = userInput.toLowerCase().includes('week-end') || 
                                 userInput.toLowerCase().includes('weekend') ||
                                 (currentData?.description && (
                                   currentData.description.toLowerCase().includes('week-end') || 
                                   currentData.description.toLowerCase().includes('weekend')
                                 ));
          
          if (mentionsWeekend) {
            // Obtenir la date actuelle
            const currentDateObj = new Date();
            
            // Calculer les dates du prochain week-end (samedi et dimanche)
            const currentDay = currentDateObj.getDay(); // 0 = dimanche, 1 = lundi, ..., 6 = samedi
            const daysUntilSaturday = currentDay === 6 ? 7 : (6 - currentDay);
            const daysUntilSunday = currentDay === 0 ? 7 : (7 - currentDay);
            
            const nextSaturday = new Date(currentDateObj);
            nextSaturday.setDate(currentDateObj.getDate() + daysUntilSaturday);
            const nextSunday = new Date(currentDateObj);
            nextSunday.setDate(currentDateObj.getDate() + daysUntilSunday);
            
            // Formater les dates pour comparaison
            const formatDate = (date: Date) => {
              const year = date.getFullYear();
              const month = String(date.getMonth() + 1).padStart(2, '0');
              const day = String(date.getDate()).padStart(2, '0');
              return `${year}-${month}-${day}`;
            };
            
            const saturdayStr = formatDate(nextSaturday);
            const sundayStr = formatDate(nextSunday);
            
            logger.info(`Date du samedi prochain: ${saturdayStr}, date du dimanche prochain: ${sundayStr}`);
            
            // Vérifier et corriger les dates des créneaux si nécessaire
            let needsCorrection = false;
            
            // Vérifier si les créneaux sont en dehors du week-end
            parsedContent.time_slots = parsedContent.time_slots.map((slot: any) => {
              // Si la date n'est ni samedi ni dimanche prochain, la corriger
              if (slot.date !== saturdayStr && slot.date !== sundayStr) {
                logger.warn(`Date incorrecte détectée pour week-end: ${slot.date}, correction vers ${saturdayStr}`);
                slot.date = saturdayStr; // Par défaut, on utilise le samedi
                needsCorrection = true;
              }
              return slot;
            });
            
            // Si aucun créneau n'est pour le week-end, ajouter un créneau pour samedi
            if (parsedContent.time_slots.length === 0 && mentionsWeekend) {
              parsedContent.time_slots.push({
                date: saturdayStr,
                start: "09:00",
                end: "12:00"
              });
              needsCorrection = true;
              logger.warn(`Aucun créneau pour le week-end détecté, ajout d'un créneau par défaut le samedi`);
            }
            
            // Mettre à jour date_mission si nécessaire
            if (needsCorrection && parsedContent.time_slots.length > 0) {
              parsedContent.date_mission = parsedContent.time_slots[0].date;
              logger.info(`Date de mission corrigée: ${parsedContent.date_mission}`);
            }
          }
        }

        // Si c'est l'étape "Catégorie et service", vérifier que les IDs sont valides
        if (step === 'Catégorie et service') {
          const categoryId = parsedContent.category_id;
          const subcategoryId = parsedContent.subcategory_id;

          logger.info(`Catégorie sélectionnée: ${categoryId}, Sous-catégorie sélectionnée: ${subcategoryId}`);

          // Vérifier si les IDs existent dans les listes
          const categoryExists = categories.some(c => c.id === categoryId);
          const subcategoryExists = subcategories.some(sc => sc.id === subcategoryId);

          if (!categoryExists) {
            logger.warn(`La catégorie ${categoryId} n'existe pas dans la liste des catégories disponibles`);
          }

          if (!subcategoryExists) {
            logger.warn(`La sous-catégorie ${subcategoryId} n'existe pas dans la liste des sous-catégories disponibles`);
          }
        }

        // Si c'est l'étape "Budget", vérifier la cohérence de la méthode de paiement
        if (step === 'Budget') {
          // Vérifier les mentions dans la description et l'input utilisateur
          const description = currentData?.description?.toLowerCase() || '';
          const userInputLower = userInput.toLowerCase();

          const mentionsEuros = description.includes('euros') ||
                               description.includes('€') ||
                               userInputLower.includes('euros') ||
                               userInputLower.includes('€');

          const mentionsJobi = description.includes('jobi') ||
                              description.includes('échange') ||
                              userInputLower.includes('jobi') ||
                              userInputLower.includes('échange');

          const mentionsPaiementJobi = description.includes('paiement en jobi') ||
                                      description.includes('payé en jobi') ||
                                      userInputLower.includes('paiement en jobi') ||
                                      userInputLower.includes('payé en jobi');

          const mentionsHybride = description.includes('au choix') ||
                                 description.includes('peu importe') ||
                                 description.includes('l\'un ou l\'autre') ||
                                 description.includes('les deux') ||
                                 userInputLower.includes('au choix') ||
                                 userInputLower.includes('peu importe') ||
                                 userInputLower.includes('l\'un ou l\'autre') ||
                                 userInputLower.includes('les deux');

          // Vérifier la cohérence et corriger si nécessaire
          if (mentionsPaiementJobi && parsedContent.payment_method !== 'jobi') {
            logger.warn('L\'IA n\'a pas choisi jobi comme méthode de paiement alors que le paiement en jobi est explicitement mentionné. Correction automatique.');
            parsedContent.payment_method = 'jobi';
          } else if (mentionsHybride && parsedContent.payment_method !== 'both') {
            logger.warn('L\'IA n\'a pas choisi le mode hybride alors que l\'utilisateur accepte les deux options. Correction automatique.');
            parsedContent.payment_method = 'both';
          } else if (mentionsJobi && mentionsEuros && !mentionsPaiementJobi && parsedContent.payment_method !== 'both') {
            logger.warn('L\'IA n\'a pas choisi le mode hybride alors que l\'utilisateur mentionne les deux options. Correction automatique.');
            parsedContent.payment_method = 'both';
          } else if (mentionsJobi && !mentionsEuros && parsedContent.payment_method === 'euros') {
            logger.warn('L\'IA a choisi euros comme méthode de paiement alors que seul jobi est mentionné. Correction automatique.');
            parsedContent.payment_method = 'jobi';
          } else if (mentionsEuros && !mentionsJobi && parsedContent.payment_method === 'jobi') {
            logger.warn('L\'IA a choisi jobi comme méthode de paiement alors que seul euros est mentionné. Correction automatique.');
            parsedContent.payment_method = 'euros';
          }
        }
      } catch (parseError) {
        logger.error(`Erreur lors du parsing du contenu JSON: ${parseError}. Contenu: ${cleanedContent}`);
      }

      // Enregistrer les données d'utilisation de l'API : token utilisés, prix, etc.
      try {
        if (response.data && response.data.usage) {
          await logOpenRouterUsage(
            userId,
            'mission_assistant',
            AI_API_MODEL,
            response.data.usage.prompt_tokens || 0,
            response.data.usage.completion_tokens || 0,
            response.data.usage.total_tokens || 0,
            response.data.id || null
          );
        }
      } catch (logError) {
        logger.error('Erreur lors de l\'enregistrement des données d\'utilisation OpenRouter', {
          error: logError,
          userId
        });
      }

      // L'enregistrement dans l'historique a déjà été fait lors de la déduction des crédits

      // Enregistrer l'activité
      await logUserActivity(
        userId,
        'ai_credit_used',
        undefined,
        'ai_credits',
        JSON.stringify({
          type: 'mission_assistant',
          isFullGeneration: isFullGeneration || false,
          creditCost: creditCost,
          credits_remaining: newCredits
        }),
        getIpFromRequest(req)
      );

      // Retourner le contenu généré (potentiellement modifié)
      let finalContent = cleanedContent;
      try {
        const parsedContent = JSON.parse(cleanedContent);

        // === FILTRE ANTI-PHRASES GENERIQUES DANS LA DESCRIPTION ===
        if (parsedContent.description && typeof parsedContent.description === 'string') {
          // Liste de regex pour détecter les phrases à supprimer
          const genericPatterns = [
            /je n['']ai pas (précisé|donné|spécifié)[^.]*\./gi,
            /aucun[^.]*n['']est précisé\./gi,
            /non précisé[\s.!?,]*/gi,
            /non renseigné[\s.!?,]*/gi,
            /aucune information (donnée|spécifiée)[^.]*\./gi,
            /n['']a pas été spécifié[\s.!?,]*/gi,
            /aucun détail[^.]*\./gi
          ];
          let desc = parsedContent.description;
          genericPatterns.forEach((pattern) => {
            desc = desc.replace(pattern, '');
          });
          // Nettoyage espaces et ponctuation
          desc = desc.replace(/\s{2,}/g, ' ').replace(/\.\s*\./g, '.').trim();
          parsedContent.description = desc;
          finalContent = JSON.stringify(parsedContent);
        }

        // Si des modifications ont été apportées, utiliser la version modifiée
        if (step === 'Budget' && parsedContent.payment_method) {
          const description = currentData?.description?.toLowerCase() || '';
          const userInputLower = userInput.toLowerCase();

          const mentionsEuros = description.includes('euros') ||
                               description.includes('€') ||
                               userInputLower.includes('euros') ||
                               userInputLower.includes('€');

          const mentionsJobi = description.includes('jobi') ||
                              description.includes('échange') ||
                              userInputLower.includes('jobi') ||
                              userInputLower.includes('échange');

          const mentionsPaiementJobi = description.includes('paiement en jobi') ||
                                      description.includes('payé en jobi') ||
                                      userInputLower.includes('paiement en jobi') ||
                                      userInputLower.includes('payé en jobi');

          const mentionsHybride = description.includes('au choix') ||
                                 description.includes('peu importe') ||
                                 description.includes('l\'un ou l\'autre') ||
                                 description.includes('les deux') ||
                                 userInputLower.includes('au choix') ||
                                 userInputLower.includes('peu importe') ||
                                 userInputLower.includes('l\'un ou l\'autre') ||
                                 userInputLower.includes('les deux');

          // Appliquer les corrections si nécessaire
          let needsCorrection = false;

          if (mentionsPaiementJobi && parsedContent.payment_method === 'euros') {
            parsedContent.payment_method = 'jobi';
            needsCorrection = true;
          } else if (mentionsJobi && !mentionsEuros && parsedContent.payment_method === 'euros') {
            parsedContent.payment_method = 'jobi';
            needsCorrection = true;
          } else if (mentionsEuros && !mentionsJobi && parsedContent.payment_method === 'jobi') {
            parsedContent.payment_method = 'euros';
            needsCorrection = true;
          }

          if (needsCorrection) {
            finalContent = JSON.stringify(parsedContent);
            logger.info(`Contenu JSON corrigé: ${finalContent}`);
          }
        }
      } catch (error) {
        // En cas d'erreur, utiliser le contenu original
        logger.error(`Erreur lors de la modification du contenu JSON: ${error}`);
      }

      return res.status(200).json({
        success: true,
        content: finalContent,
        creditsRemaining: newCredits,
        creditCost: creditCost,
        toastType: 'success'
      });
    } catch (apiError: any) {
      logger.error('Erreur lors de l\'appel à l\'API OpenRouter pour l\'assistant mission:', apiError);

      // RESTITUER LES CRÉDITS en cas d'erreur de génération
      if (creditsDebited && currentCredits !== undefined) {
        logger.warn('Restitution des crédits suite à une erreur API d\'assistant mission');
        await restoreAiCredits(userId!, currentCredits, creditCost);
      }

      return res.status(500).json({
        success: false,
        message: 'Erreur lors de la génération du contenu',
        error: apiError.message,
        toastType: 'error'
      });
    } finally {
      clearTimeout(timeoutId);
    }
  } catch (error: any) {
    logger.error('Erreur lors de la génération du contenu:', error);

    // Si les crédits ont été débités et qu'une erreur survient AVANT l'API, les restituer
    if (creditsDebited && userId && currentCredits !== undefined && !error.message?.includes('API OpenRouter')) {
      logger.warn('Restitution des crédits suite à une erreur générale d\'assistant mission (non-API)');
      await restoreAiCredits(userId, currentCredits, creditCost);
    }

    return res.status(500).json({
      success: false,
      message: 'Erreur lors de la génération du contenu',
      toastType: 'error'
    });
  }
};

// Fonction pour restituer des crédits en cas d'erreur de génération complète
const restoreCreditsAfterError = async (req: Request, res: Response) => {
  let userId: string | undefined;

  try {
    // Récupérer l'ID utilisateur depuis req.user
    userId = req.user?.id || req.user?.userId;

    if (!userId) {
      return res.status(401).json({
        success: false,
        message: 'Utilisateur non authentifié',
        toastType: 'error'
      });
    }

    // Récupérer les données de la requête
    const { creditsToRestore, reason } = req.body;

    if (!creditsToRestore || creditsToRestore <= 0) {
      return res.status(400).json({
        success: false,
        message: 'Nombre de crédits invalide',
        toastType: 'error'
      });
    }

    // Appeler la fonction de restitution
    await restoreAiCredits(userId, creditsToRestore, reason || 'Erreur lors de génération complète');

    logger.info(`${creditsToRestore} crédits IA restitués pour l'utilisateur ${userId} - Raison: ${reason}`);

    res.json({
      success: true,
      message: `${creditsToRestore} crédit${creditsToRestore > 1 ? 's' : ''} restitué${creditsToRestore > 1 ? 's' : ''}`,
      toastType: 'success'
    });

  } catch (error) {
    logger.error('Erreur lors de la restitution des crédits:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur lors de la restitution des crédits',
      toastType: 'error'
    });
  }
};

export { generateMissionStep, restoreCreditsAfterError };
