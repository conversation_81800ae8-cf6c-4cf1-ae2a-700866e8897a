import { Request, Response } from 'express';
import { supabase } from '../config/supabase';
import logger from '../utils/logger';
import { queueEmail } from '../services/emailQueueService';
import { redis } from '../config/redis';
import { z } from 'zod';
import { logUserActivity, getIpFromRequest } from '../utils/activityLogger';
import { decryptUserDataAsync, decryptProfilDataAsync, encryptProfilDataAsync, encryptUserDataAsync } from '../utils/encryption';
import { decryptDataAsync } from '../utils/encryption';

// Les types sont déjà définis dans authMiddleware.ts

/**
 * Gestion des abonnements - Modifier/Étendre/Créer
 */
export const manageSubscription = async (req: Request, res: Response) => {
  try {
    const { userId } = req.params;
    const { 
      action, 
      subscriptionType, 
      duration, 
      autoRenewal = false, 
      description, 
      sendNotification = true, 
      sendEmail = true 
    } = req.body;
    const adminUserId = req.user?.userId;

    if (!userId || !action) {
      return res.status(400).json({
        success: false,
        message: 'Paramètres manquants (userId, action)',
        toastType: 'error'
      });
    }

    // Récupérer les informations de l'utilisateur cible
    const { data: targetUser, error: userError } = await supabase
      .from('users')
      .select('email')
      .eq('id', userId)
      .single();

    if (userError || !targetUser) {
      return res.status(404).json({
        success: false,
        message: 'Utilisateur non trouvé',
        toastType: 'error'
      });
    }

    let result;
    let message = '';

    switch (action) {
      case 'create':
        if (!subscriptionType || !duration) {
          return res.status(400).json({
            success: false,
            message: 'Type d\'abonnement et durée requis pour la création',
            toastType: 'error'
          });
        }

        const startDate = new Date();
        const endDate = new Date();
        endDate.setDate(endDate.getDate() + parseInt(duration));

        result = await supabase
          .from('user_abo')
          .insert({
            user_id: userId,
            type_abonnement: subscriptionType,
            statut: 'actif',
            date_debut: startDate.toISOString(),
            date_fin: endDate.toISOString(),
            renouvellement_auto: autoRenewal,
            admin_created: true,
            admin_user_id: adminUserId,
            description: description || `Abonnement ${subscriptionType} créé par un administrateur`
          });

        message = `Abonnement ${subscriptionType} créé avec succès pour ${duration} jours`;
        break;

      case 'extend':
        if (!duration) {
          return res.status(400).json({
            success: false,
            message: 'Durée requise pour l\'extension',
            toastType: 'error'
          });
        }

        // Récupérer l'abonnement actuel
        const { data: currentSub } = await supabase
          .from('user_abo')
          .select('*')
          .eq('user_id', userId)
          .eq('statut', 'actif')
          .order('created_at', { ascending: false })
          .limit(1)
          .single();

        if (!currentSub) {
          return res.status(404).json({
            success: false,
            message: 'Aucun abonnement actif trouvé',
            toastType: 'error'
          });
        }

        const currentEndDate = new Date(currentSub.date_fin);
        const newEndDate = new Date(currentEndDate);
        newEndDate.setDate(newEndDate.getDate() + parseInt(duration));

        result = await supabase
          .from('user_abo')
          .update({
            date_fin: newEndDate.toISOString(),
            updated_at: new Date().toISOString()
          })
          .eq('id', currentSub.id);

        message = `Abonnement étendu de ${duration} jours avec succès`;
        break;

      case 'suspend':
        result = await supabase
          .from('user_abo')
          .update({
            statut: 'suspendu',
            suspension_reason: description || 'Suspendu par un administrateur',
            suspended_by: adminUserId,
            updated_at: new Date().toISOString()
          })
          .eq('user_id', userId)
          .eq('statut', 'actif');

        message = 'Abonnement suspendu avec succès';
        break;

      case 'reactivate':
        result = await supabase
          .from('user_abo')
          .update({
            statut: 'actif',
            suspension_reason: null,
            suspended_by: null,
            updated_at: new Date().toISOString()
          })
          .eq('user_id', userId)
          .eq('statut', 'suspendu');

        message = 'Abonnement réactivé avec succès';
        break;

      case 'cancel':
        // Récupérer les informations de l'abonnement avant annulation
        const { data: aboToCancel } = await supabase
          .from('user_abo')
          .select('type_abonnement')
          .eq('user_id', userId)
          .eq('statut', 'actif')
          .single();

        // Si c'est un abonnement premium, réinitialiser les options aux valeurs par défaut du plan gratuit
        let updateData: any = {
          statut: 'annule',
          renouvellement_auto: false,
          cancellation_reason: description || 'Annulé par un administrateur',
          cancelled_by: adminUserId,
          updated_at: new Date().toISOString()
        };

        if (aboToCancel?.type_abonnement === 'premium') {
          const { subscriptions } = require('../config/ConfigSubscriptions');
          const defaultGratuitOptions = {
            services: subscriptions.gratuit.services.included,
            galleries: subscriptions.gratuit.galleries.included,
            interventionAreas: subscriptions.gratuit.interventionAreas.included,
            conversations_messages_prives: subscriptions.gratuit.conversations_messages_prives.included,
            quotes: subscriptions.gratuit.quotes.included,
            invoices: subscriptions.gratuit.invoices.included,
            planning_slots: subscriptions.gratuit.planning_slots.included,
            missionResponses: subscriptions.gratuit.missionResponses.included,
            favoriteLimit: subscriptions.gratuit.favoriteLimit.included,
            businessCards: subscriptions.gratuit.businessCards.included,
            flyers: subscriptions.gratuit.flyers.included,
            franceEntiere: false
          };

          updateData.type_abonnement = 'gratuit';
          updateData.montant = 0;
          updateData.options = defaultGratuitOptions;

          // Appliquer les ajustements des fonctionnalités premium
          try {
            const { adjustPremiumFeatures } = require('../routes/configSubscriptions');
            const adjustmentResult = await adjustPremiumFeatures(userId);

            if (adjustmentResult.success && adjustmentResult.summary !== 'Aucun ajustement nécessaire') {
              logger.info(`Fonctionnalités premium ajustées lors de l'annulation par admin pour l'utilisateur ${userId}: ${adjustmentResult.summary}`);
            }
          } catch (error) {
            logger.error(`Erreur lors de l'ajustement des fonctionnalités premium lors de l'annulation par admin pour l'utilisateur ${userId}:`, error);
          }
        }

        result = await supabase
          .from('user_abo')
          .update(updateData)
          .eq('user_id', userId)
          .eq('statut', 'actif');

        message = 'Abonnement annulé avec succès';
        break;

      default:
        return res.status(400).json({
          success: false,
          message: 'Action invalide (create, extend, suspend, reactivate, cancel)',
          toastType: 'error'
        });
    }

    if (result?.error) {
      logger.error('Erreur lors de la gestion de l\'abonnement:', result.error);
      return res.status(500).json({
        success: false,
        message: 'Erreur lors de la gestion de l\'abonnement',
        toastType: 'error'
      });
    }

    // Envoyer une notification
    if (sendNotification) {
      const { error: notifError } = await supabase
        .from('user_notifications')
        .insert({
          user_id: userId,
          type: 'subscription',
          title: `Abonnement ${action === 'create' ? 'créé' : action === 'extend' ? 'étendu' : action === 'suspend' ? 'suspendu' : action === 'reactivate' ? 'réactivé' : 'annulé'}`,
          content: `Votre abonnement a été ${action === 'create' ? 'créé' : action === 'extend' ? 'étendu' : action === 'suspend' ? 'suspendu' : action === 'reactivate' ? 'réactivé' : 'annulé'} par un administrateur.${description ? ` Motif: ${description}` : ''}`,
          link: '/dashboard/abonnement',
          is_read: false,
          is_archived: false
        });

      if (notifError) {
        logger.error('Erreur lors de l\'envoi de la notification:', notifError);
      }
    }

    // Envoyer un email
    if (sendEmail && targetUser.email) {
      try {
        await queueEmail(
          targetUser.email,
          `Modification de votre abonnement JobPartiel`,
          `
          <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
            <h2 style="color: #FF6B2C;">Modification d'abonnement</h2>
            <p>Bonjour,</p>
            <p>Votre abonnement JobPartiel a été <strong>${action === 'create' ? 'créé' : action === 'extend' ? 'étendu' : action === 'suspend' ? 'suspendu' : action === 'reactivate' ? 'réactivé' : 'annulé'}</strong> par un administrateur.</p>
            ${description ? `<p><strong>Motif :</strong> ${description}</p>` : ''}
            ${action === 'create' && subscriptionType ? `<p><strong>Type d'abonnement :</strong> ${subscriptionType}</p>` : ''}
            ${duration ? `<p><strong>Durée :</strong> ${duration} jours</p>` : ''}
            <p>Vous pouvez consulter les détails de votre abonnement dans votre espace personnel.</p>
            <p>Cordialement,<br>L'équipe JobPartiel</p>
          </div>
          `
        );
      } catch (emailError) {
        logger.error('Erreur lors de l\'envoi de l\'email abonnement:', emailError);
      }
    }

    // Invalider le cache
    await redis.del(`user_details:${userId}`);
    await redis.del(`user_stats:${userId}:*`);

    res.json({
      success: true,
      message,
      data: {
        action,
        subscriptionType,
        duration,
        autoRenewal,
        description,
        adminUserId,
        timestamp: new Date().toISOString()
      },
      toastType: 'success'
    });

  } catch (error) {
    logger.error('Erreur dans manageSubscription:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur serveur lors de la gestion de l\'abonnement',
      toastType: 'error'
    });
  }
};

/**
 * Actions administratives sur l'utilisateur
 */
export const performUserAction = async (req: Request, res: Response) => {
  try {
    const { userId } = req.params;
    const {
      action,
      reason,
      duration,
      sendNotification = true,
      sendEmail = true
    } = req.body;
    const adminUserId = req.user?.userId;

    if (!userId || !action) {
      return res.status(400).json({
        success: false,
        message: 'Paramètres manquants (userId, action)',
        toastType: 'error'
      });
    }

    // Récupérer les informations de l'utilisateur cible
    const { data: targetUser, error: userError } = await supabase
      .from('users')
      .select('email, profil_actif, role')
      .eq('id', userId)
      .single();

    if (userError || !targetUser) {
      return res.status(404).json({
        success: false,
        message: 'Utilisateur non trouvé',
        toastType: 'error'
      });
    }

    let result;
    let message = '';
    let emailSubject = '';
    let emailContent = '';

    switch (action) {
      case 'activate':
        result = await supabase
          .from('users')
          .update({
            profil_actif: true,
            suspended_until: null,
            suspension_reason: null,
            updated_at: new Date().toISOString()
          })
          .eq('id', userId);

        message = 'Utilisateur activé avec succès';
        emailSubject = 'Votre compte JobPartiel a été activé';
        emailContent = `
          <p>Votre compte JobPartiel a été activé par un administrateur.</p>
          ${reason ? `<p><strong>Motif :</strong> ${reason}</p>` : ''}
          <p>Vous pouvez maintenant accéder à toutes les fonctionnalités de la plateforme.</p>
        `;
        break;

      case 'deactivate':
        result = await supabase
          .from('users')
          .update({
            profil_actif: false,
            suspension_reason: reason || 'Désactivé par un administrateur',
            updated_at: new Date().toISOString()
          })
          .eq('id', userId);

        message = 'Utilisateur désactivé avec succès';
        emailSubject = 'Votre compte JobPartiel a été désactivé';
        emailContent = `
          <p>Votre compte JobPartiel a été désactivé par un administrateur.</p>
          ${reason ? `<p><strong>Motif :</strong> ${reason}</p>` : ''}
          <p>Contactez le support si vous pensez qu'il s'agit d'une erreur.</p>
        `;
        break;

      case 'suspend':
        if (!duration) {
          return res.status(400).json({
            success: false,
            message: 'Durée de suspension requise',
            toastType: 'error'
          });
        }

        const suspensionEnd = new Date();
        suspensionEnd.setDate(suspensionEnd.getDate() + parseInt(duration));

        result = await supabase
          .from('users')
          .update({
            profil_actif: false,
            suspended_until: suspensionEnd.toISOString(),
            suspension_reason: reason || 'Suspendu par un administrateur',
            updated_at: new Date().toISOString()
          })
          .eq('id', userId);

        message = `Utilisateur suspendu pour ${duration} jours`;
        emailSubject = 'Votre compte JobPartiel a été suspendu';
        emailContent = `
          <p>Votre compte JobPartiel a été suspendu jusqu'au ${suspensionEnd.toLocaleDateString('fr-FR')}.</p>
          ${reason ? `<p><strong>Motif :</strong> ${reason}</p>` : ''}
          <p>Contactez le support si vous souhaitez contester cette décision.</p>
        `;
        break;

      case 'verify_profile':
        result = await supabase
          .from('users')
          .update({
            profil_verifier: true,
            updated_at: new Date().toISOString()
          })
          .eq('id', userId);

        message = 'Profil vérifié avec succès';
        emailSubject = 'Votre profil JobPartiel a été vérifié';
        emailContent = `
          <p>Félicitations ! Votre profil JobPartiel a été vérifié par notre équipe.</p>
          ${reason ? `<p><strong>Note :</strong> ${reason}</p>` : ''}
          <p>Vous bénéficiez maintenant d'une meilleure visibilité sur la plateforme.</p>
        `;
        break;

      case 'unverify_profile':
        result = await supabase
          .from('users')
          .update({
            profil_verifier: false,
            updated_at: new Date().toISOString()
          })
          .eq('id', userId);

        message = 'Vérification du profil retirée';
        emailSubject = 'Vérification de votre profil JobPartiel retirée';
        emailContent = `
          <p>La vérification de votre profil JobPartiel a été retirée.</p>
          ${reason ? `<p><strong>Motif :</strong> ${reason}</p>` : ''}
          <p>Vous pouvez soumettre une nouvelle demande de vérification.</p>
        `;
        break;

      case 'verify_identity':
        result = await supabase
          .from('users')
          .update({
            identite_verifier: true,
            updated_at: new Date().toISOString()
          })
          .eq('id', userId);

        message = 'Identité vérifiée avec succès';
        emailSubject = 'Votre identité JobPartiel a été vérifiée';
        emailContent = `
          <p>Votre identité a été vérifiée avec succès sur JobPartiel.</p>
          ${reason ? `<p><strong>Note :</strong> ${reason}</p>` : ''}
          <p>Votre compte bénéficie maintenant d'un niveau de confiance élevé.</p>
        `;
        break;

      case 'verify_company':
        result = await supabase
          .from('users')
          .update({
            entreprise_verifier: true,
            updated_at: new Date().toISOString()
          })
          .eq('id', userId);

        message = 'Entreprise vérifiée avec succès';
        emailSubject = 'Votre entreprise JobPartiel a été vérifiée';
        emailContent = `
          <p>Votre entreprise a été vérifiée avec succès sur JobPartiel.</p>
          ${reason ? `<p><strong>Note :</strong> ${reason}</p>` : ''}
          <p>Vous pouvez maintenant proposer vos services en tant qu'entreprise vérifiée.</p>
        `;
        break;

      case 'reset_password':
        // Générer un token de réinitialisation
        const resetToken = Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15);
        const resetExpiry = new Date();
        resetExpiry.setHours(resetExpiry.getHours() + 24); // 24h

        const { error: resetError } = await supabase
          .from('password_resets')
          .insert({
            user_id: userId,
            token: resetToken,
            expires_at: resetExpiry.toISOString(),
            admin_initiated: true,
            admin_user_id: adminUserId
          });

        if (resetError) {
          logger.error('Erreur lors de la création du token de réinitialisation:', resetError);
          return res.status(500).json({
            success: false,
            message: 'Erreur lors de la génération du lien de réinitialisation',
            toastType: 'error'
          });
        }

        message = 'Lien de réinitialisation généré avec succès';
        emailSubject = 'Réinitialisation de votre mot de passe JobPartiel';
        emailContent = `
          <p>Un administrateur a initié une réinitialisation de votre mot de passe.</p>
          ${reason ? `<p><strong>Motif :</strong> ${reason}</p>` : ''}
          <p><a href="${process.env.FRONTEND_URL}/reset-password?token=${resetToken}" style="background-color: #FF6B2C; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">Réinitialiser mon mot de passe</a></p>
          <p>Ce lien expire dans 24 heures.</p>
        `;
        break;

      default:
        return res.status(400).json({
          success: false,
          message: 'Action invalide',
          toastType: 'error'
        });
    }

    if (result?.error) {
      logger.error('Erreur lors de l\'action utilisateur:', result.error);
      return res.status(500).json({
        success: false,
        message: 'Erreur lors de l\'exécution de l\'action',
        toastType: 'error'
      });
    }

    // Envoyer une notification
    if (sendNotification) {
      const { error: notifError } = await supabase
        .from('user_notifications')
        .insert({
          user_id: userId,
          type: 'admin_action',
          title: `Action administrative : ${action}`,
          content: `Une action administrative a été effectuée sur votre compte.${reason ? ` Motif: ${reason}` : ''}`,
          link: '/dashboard/profil',
          is_read: false,
          is_archived: false
        });

      if (notifError) {
        logger.error('Erreur lors de l\'envoi de la notification:', notifError);
      }
    }

    // Envoyer un email
    if (sendEmail && targetUser.email && emailSubject && emailContent) {
      try {
        await queueEmail(
          targetUser.email,
          emailSubject,
          `
          <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
            <h2 style="color: #FF6B2C;">Action administrative</h2>
            <p>Bonjour,</p>
            ${emailContent}
            <p>Cordialement,<br>L'équipe JobPartiel</p>
          </div>
          `
        );
      } catch (emailError) {
        logger.error('Erreur lors de l\'envoi de l\'email action:', emailError);
      }
    }

    // Invalider le cache
    await redis.del(`user_details:${userId}`);
    await redis.del(`user_stats:${userId}:*`);

    res.json({
      success: true,
      message,
      data: {
        action,
        reason,
        duration,
        adminUserId,
        timestamp: new Date().toISOString()
      },
      toastType: 'success'
    });

  } catch (error) {
    logger.error('Erreur dans performUserAction:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur serveur lors de l\'exécution de l\'action',
      toastType: 'error'
    });
  }
};

/**
 * Gestion des photos et galeries utilisateur
 */
export const manageUserPhotos = async (req: Request, res: Response) => {
  try {
    const { userId } = req.params;
    const { action, photoId, galleryId, reason, sendNotification = true, sendEmail = true } = req.body;
    const adminUserId = req.user?.userId;

    if (!userId || !action) {
      return res.status(400).json({
        success: false,
        message: 'Paramètres manquants (userId, action)',
        toastType: 'error'
      });
    }

    // Vérifier que le motif est fourni pour toutes les actions
    if (!reason || reason.trim() === '') {
      return res.status(400).json({
        success: false,
        message: 'Le motif de l\'action est obligatoire',
        toastType: 'error'
      });
    }

    // Récupérer les informations de l'utilisateur cible
    const { data: targetUser, error: userError } = await supabase
      .from('users')
      .select('email')
      .eq('id', userId)
      .single();

    if (userError || !targetUser) {
      return res.status(404).json({
        success: false,
        message: 'Utilisateur non trouvé',
        toastType: 'error'
      });
    }

    let result;
    let message = '';
    let notificationTitle = '';
    let notificationContent = '';
    let emailSubject = '';
    let emailContent = '';
    let photoInfo = null;
    let galleryInfo = null;

    // Récupérer les informations de la photo ou galerie avant suppression
    if (photoId) {
      const { data: photo } = await supabase
        .from('user_gallery_photos')
        .select('caption, photo_url')
        .eq('id', photoId)
        .eq('user_id', userId)
        .single();
      
      if (!photo) {
        const { data: featuredPhoto } = await supabase
          .from('user_featured_photos')
          .select('caption, photo_url')
          .eq('id', photoId)
          .eq('user_id', userId)
          .single();
        photoInfo = featuredPhoto;
      } else {
        photoInfo = photo;
      }
    }

    if (galleryId) {
      const { data: gallery } = await supabase
        .from('user_gallery')
        .select('name, description, user_gallery_photos(count)')
        .eq('id', galleryId)
        .eq('user_id', userId)
        .single();
      galleryInfo = gallery;
    }

    switch (action) {
      case 'delete_photo':
        if (!photoId) {
          return res.status(400).json({
            success: false,
            message: 'ID de photo requis',
            toastType: 'error'
          });
        }

        // Supprimer la photo de la galerie
        result = await supabase
          .from('user_gallery_photos')
          .delete()
          .eq('id', photoId)
          .eq('user_id', userId);

        message = 'Photo de galerie supprimée avec succès';
        notificationTitle = 'Photo supprimée par la modération';
        notificationContent = `Une de vos photos de galerie a été supprimée par un modérateur. Motif: ${reason}`;
        emailSubject = 'Photo supprimée - JobPartiel';
        emailContent = `
          <p>Une de vos photos de galerie a été supprimée par notre équipe de modération.</p>
          ${photoInfo?.caption ? `<p><strong>Photo :</strong> ${photoInfo.caption}</p>` : ''}
          <p><strong>Motif :</strong> ${reason}</p>
          <p>Si vous pensez qu'il s'agit d'une erreur, contactez notre support.</p>
        `;
        break;

      case 'delete_featured_photo':
        if (!photoId) {
          return res.status(400).json({
            success: false,
            message: 'ID de photo requis',
            toastType: 'error'
          });
        }

        // Supprimer la photo mise en avant
        result = await supabase
          .from('user_featured_photos')
          .delete()
          .eq('id', photoId)
          .eq('user_id', userId);

        message = 'Photo mise en avant supprimée avec succès';
        notificationTitle = 'Photo mise en avant supprimée';
        notificationContent = `Votre photo mise en avant a été supprimée par un modérateur. Motif: ${reason}`;
        emailSubject = 'Photo mise en avant supprimée - JobPartiel';
        emailContent = `
          <p>Votre photo mise en avant a été supprimée par notre équipe de modération.</p>
          ${photoInfo?.caption ? `<p><strong>Photo :</strong> ${photoInfo.caption}</p>` : ''}
          <p><strong>Motif :</strong> ${reason}</p>
          <p>Vous pouvez ajouter une nouvelle photo mise en avant dans votre profil.</p>
        `;
        break;

      case 'delete_gallery':
        if (!galleryId) {
          return res.status(400).json({
            success: false,
            message: 'ID de galerie requis',
            toastType: 'error'
          });
        }

        // Supprimer toute la galerie et ses photos (cascade)
        result = await supabase
          .from('user_gallery')
          .delete()
          .eq('id', galleryId)
          .eq('user_id', userId);

        message = 'Galerie supprimée avec succès';
        notificationTitle = 'Galerie supprimée par la modération';
        notificationContent = `Une de vos galeries a été supprimée par un modérateur. Motif: ${reason}`;
        emailSubject = 'Galerie supprimée - JobPartiel';
        emailContent = `
          <p>Une de vos galeries a été supprimée par notre équipe de modération.</p>
          ${galleryInfo?.name ? `<p><strong>Galerie :</strong> ${galleryInfo.name}</p>` : ''}
          ${galleryInfo?.user_gallery_photos ? `<p><strong>Photos supprimées :</strong> ${galleryInfo.user_gallery_photos[0]?.count || 0}</p>` : ''}
          <p><strong>Motif :</strong> ${reason}</p>
          <p>Si vous pensez qu'il s'agit d'une erreur, contactez notre support.</p>
        `;
        break;

      case 'moderate_photo':
        if (!photoId) {
          return res.status(400).json({
            success: false,
            message: 'ID de photo requis',
            toastType: 'error'
          });
        }

        // Marquer la photo comme modérée dans la table user_gallery_photos
        result = await supabase
          .from('user_gallery_photos')
          .update({
            is_moderated: true,
            moderated_by: adminUserId,
            moderated_at: new Date().toISOString(),
            moderation_reason: reason
          })
          .eq('id', photoId)
          .eq('user_id', userId);

        // Si pas trouvée dans user_gallery_photos, essayer user_featured_photos
        if (result?.error || !result?.data) {
          result = await supabase
            .from('user_featured_photos')
            .update({
              is_moderated: true,
              moderated_by: adminUserId,
              moderated_at: new Date().toISOString(),
              moderation_reason: reason
            })
            .eq('id', photoId)
            .eq('user_id', userId);
        }

        message = 'Photo envoyée en modération';
        notificationTitle = 'Photo en cours de modération';
        notificationContent = `Une de vos photos est en cours de modération. Motif: ${reason}`;
        emailSubject = 'Photo en modération - JobPartiel';
        emailContent = `
          <p>Une de vos photos est actuellement en cours de modération par notre équipe.</p>
          ${photoInfo?.caption ? `<p><strong>Photo :</strong> ${photoInfo.caption}</p>` : ''}
          <p><strong>Motif :</strong> ${reason}</p>
          <p>Nous vous tiendrons informé de la décision finale.</p>
        `;
        break;

      default:
        return res.status(400).json({
          success: false,
          message: 'Action invalide (delete_photo, delete_featured_photo, delete_gallery, moderate_photo)',
          toastType: 'error'
        });
    }

    if (result?.error) {
      logger.error('Erreur lors de la gestion des photos:', result.error);
      return res.status(500).json({
        success: false,
        message: 'Erreur lors de la gestion des photos',
        toastType: 'error'
      });
    }

    // Envoyer une notification à l'utilisateur
    if (sendNotification) {
      const { error: notifError } = await supabase
        .from('user_notifications')
        .insert({
          user_id: userId,
          type: 'system',
          title: notificationTitle,
          content: notificationContent,
          link: '/dashboard/galerie',
          is_read: false,
          is_archived: false
        });

      if (notifError) {
        logger.error('Erreur lors de l\'envoi de la notification:', notifError);
      }
    }

    // Envoyer un email à l'utilisateur
    if (sendEmail && targetUser.email && emailSubject && emailContent) {
      try {
        const decryptedEmail = await decryptUserDataAsync({ email: targetUser.email });
        await queueEmail(
          decryptedEmail.email,
          emailSubject,
          `
          <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
            <h2 style="color: #FF6B2C;">Action de modération</h2>
            <p>Bonjour,</p>
            ${emailContent}
            <p>Pour toute question, n'hésitez pas à contacter notre support.</p>
            <p>Cordialement,<br>L'équipe JobPartiel</p>
          </div>
          `
        );
      } catch (emailError) {
        logger.error('Erreur lors de l\'envoi de l\'email de modération photo:', emailError);
      }
    }

    // Logger l'activité admin
    try {
      await logUserActivity(
        adminUserId || 'system',
        'admin_photo_moderation',
        photoId || galleryId || null,
        photoId ? 'user_photo' : (galleryId ? 'user_gallery' : 'admin_action'),
        {
          message: `Action de modération photo: ${action} pour l'utilisateur ${userId}`,
          targetUserId: userId,
          action,
          photoId,
          galleryId,
          reason,
          photoInfo: photoInfo ? { caption: photoInfo.caption } : null,
          galleryInfo: galleryInfo ? { name: galleryInfo.name } : null,
          adminRole: req.user?.role
        }
      );
    } catch (logError) {
      logger.error('Erreur lors du logging de l\'activité admin photo:', logError);
    }

    // Invalider le cache
    await redis.del(`user_details:${userId}`);
    await redis.del(`user_gallery:${userId}`);
    await redis.del(`user_featured_photos:${userId}`);

    res.json({
      success: true,
      message,
      data: {
        action,
        photoId,
        galleryId,
        reason,
        adminUserId,
        photoInfo,
        galleryInfo,
        timestamp: new Date().toISOString()
      },
      toastType: 'success'
    });

  } catch (error) {
    logger.error('Erreur dans manageUserPhotos:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur serveur lors de la gestion des photos',
      toastType: 'error'
    });
  }
};

/**
 * Gestion des badges utilisateur
 */
export const manageUserBadges = async (req: Request, res: Response) => {
  try {
    const { userId } = req.params;
    const { action, badgeId, reason, sendNotification = true } = req.body;
    const adminUserId = req.user?.userId;

    if (!userId || !action) {
      return res.status(400).json({
        success: false,
        message: 'Paramètres manquants (userId, action)',
        toastType: 'error'
      });
    }

    let result;
    let message = '';

    switch (action) {
      case 'award_badge':
        if (!badgeId) {
          return res.status(400).json({
            success: false,
            message: 'ID de badge requis',
            toastType: 'error'
          });
        }

        // Vérifier si l'utilisateur a déjà ce badge
        const { data: existingBadge } = await supabase
          .from('user_badges')
          .select('id')
          .eq('user_id', userId)
          .eq('badge_id', badgeId)
          .single();

        if (existingBadge) {
          return res.status(400).json({
            success: false,
            message: 'L\'utilisateur possède déjà ce badge',
            toastType: 'error'
          });
        }

        // Attribuer le badge
        result = await supabase
          .from('user_badges')
          .insert({
            user_id: userId,
            badge_id: badgeId,
            recompense_recu: true,
            is_lifetime: true,
            date_obtention: new Date().toISOString()
          });

        message = 'Badge attribué avec succès';
        break;

      case 'remove_badge':
        if (!badgeId) {
          return res.status(400).json({
            success: false,
            message: 'ID de badge requis',
            toastType: 'error'
          });
        }

        // Retirer le badge
        result = await supabase
          .from('user_badges')
          .delete()
          .eq('user_id', userId)
          .eq('badge_id', badgeId);

        message = 'Badge retiré avec succès';
        break;

      default:
        return res.status(400).json({
          success: false,
          message: 'Action invalide (award_badge ou remove_badge)',
          toastType: 'error'
        });
    }

    if (result?.error) {
      logger.error('Erreur lors de la gestion des badges:', result.error);
      return res.status(500).json({
        success: false,
        message: 'Erreur lors de la gestion des badges',
        toastType: 'error'
      });
    }

    // Envoyer une notification
    if (sendNotification) {
      const { error: notifError } = await supabase
        .from('user_notifications')
        .insert({
          user_id: userId,
          type: 'system',
          title: action === 'award_badge' ? 'Nouveau badge reçu' : 'Badge retiré',
          content: `${action === 'award_badge' ? 'Vous avez reçu un nouveau badge' : 'Un badge a été retiré de votre profil'} par un administrateur.${reason ? ` Motif: ${reason}` : ''}`,
          link: '/dashboard/badges',
          is_read: false,
          is_archived: false
        });

      if (notifError) {
        logger.error('Erreur lors de l\'envoi de la notification:', notifError);
      }
    }

    // Invalider le cache
    await redis.del(`user_details:${userId}`);

    res.json({
      success: true,
      message,
      data: {
        action,
        badgeId,
        reason,
        adminUserId,
        timestamp: new Date().toISOString()
      },
      toastType: 'success'
    });

  } catch (error) {
    logger.error('Erreur dans manageUserBadges:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur serveur lors de la gestion des badges',
      toastType: 'error'
    });
  }
};

/**
 * Gestion des messages et conversations utilisateur
 */
export const manageUserMessages = async (req: Request, res: Response) => {
  try {
    const { userId } = req.params;
    const { action, conversationId, messageId, reason } = req.body;
    const adminUserId = req.user?.userId;

    if (!userId || !action) {
      return res.status(400).json({
        success: false,
        message: 'Paramètres manquants (userId, action)',
        toastType: 'error'
      });
    }

    let result;
    let message = '';

    switch (action) {
      case 'delete_conversation':
        if (!conversationId) {
          return res.status(400).json({
            success: false,
            message: 'ID de conversation requis',
            toastType: 'error'
          });
        }

        // Supprimer tous les messages de la conversation
        await supabase
          .from('user_messages')
          .delete()
          .eq('conversation_id', conversationId);

        // Supprimer la conversation
        result = await supabase
          .from('user_messages_conversations')
          .delete()
          .eq('id', conversationId)
          .or(`user1_id.eq.${userId},user2_id.eq.${userId}`);

        message = 'Conversation supprimée avec succès';
        break;

      case 'delete_message':
        if (!messageId) {
          return res.status(400).json({
            success: false,
            message: 'ID de message requis',
            toastType: 'error'
          });
        }

        // Supprimer le message spécifique
        result = await supabase
          .from('user_messages')
          .delete()
          .eq('id', messageId)
          .eq('sender_id', userId);

        message = 'Message supprimé avec succès';
        break;

      case 'moderate_conversation':
        if (!conversationId) {
          return res.status(400).json({
            success: false,
            message: 'ID de conversation requis',
            toastType: 'error'
          });
        }

        // Marquer la conversation comme modérée
        result = await supabase
          .from('user_messages_conversations')
          .update({
            is_moderated: true,
            moderated_by: adminUserId,
            moderated_at: new Date().toISOString(),
            moderation_reason: reason
          })
          .eq('id', conversationId);

        message = 'Conversation modérée avec succès';
        break;

      case 'block_user_messages':
        // Bloquer l'utilisateur de l'envoi de messages
        result = await supabase
          .from('users')
          .update({
            can_send_messages: false,
            message_ban_reason: reason,
            message_banned_by: adminUserId,
            message_banned_at: new Date().toISOString()
          })
          .eq('id', userId);

        message = 'Utilisateur bloqué pour l\'envoi de messages';
        break;

      case 'unblock_user_messages':
        // Débloquer l'utilisateur pour l'envoi de messages
        result = await supabase
          .from('users')
          .update({
            can_send_messages: true,
            message_ban_reason: null,
            message_banned_by: null,
            message_banned_at: null
          })
          .eq('id', userId);

        message = 'Utilisateur débloqué pour l\'envoi de messages';
        break;

      default:
        return res.status(400).json({
          success: false,
          message: 'Action invalide',
          toastType: 'error'
        });
    }

    if (result?.error) {
      logger.error('Erreur lors de la gestion des messages:', result.error);
      return res.status(500).json({
        success: false,
        message: 'Erreur lors de la gestion des messages',
        toastType: 'error'
      });
    }

    // Envoyer une notification
    const { error: notifError } = await supabase
      .from('user_notifications')
      .insert({
        user_id: userId,
        type: 'system',
        title: 'Action de modération',
        content: `${message}${reason ? ` Motif: ${reason}` : ''}`,
        link: '/dashboard/messages',
        is_read: false,
        is_archived: false
      });

    if (notifError) {
      logger.error('Erreur lors de l\'envoi de la notification:', notifError);
    }

    // Invalider le cache
    await redis.del(`user_details:${userId}`);

    res.json({
      success: true,
      message,
      data: {
        action,
        conversationId,
        messageId,
        reason,
        adminUserId,
        timestamp: new Date().toISOString()
      },
      toastType: 'success'
    });

  } catch (error) {
    logger.error('Erreur dans manageUserMessages:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur serveur lors de la gestion des messages',
      toastType: 'error'
    });
  }
};

/**
 * Gestion avancée des statistiques et rapports utilisateur
 */
export const getUserMissions = async (req: Request, res: Response) => {
  try {
    const { userId } = req.params;

    if (!userId) {
      return res.status(400).json({
        success: false,
        message: 'ID utilisateur requis',
        toastType: 'error'
      });
    }

    // Récupérer les missions de l'utilisateur avec toutes les données liées
    const { data: missions, error: missionsError } = await supabase
      .from('user_missions')
      .select(`
        *,
        photos:user_mission_photos (
          id,
          photo_url,
          order_index
        ),
        candidatures:user_mission_candidature (
          id,
          jobbeur_id,
          statut,
          message,
          montant_propose,
          montant_contre_offre,
          message_contre_offre,
          date_contre_offre,
          montant_contre_offre_jobbeur,
          message_contre_offre_jobbeur,
          date_contre_offre_jobbeur,
          date_refus,
          date_acceptation,
          montant_paiement,
          created_at,
          updated_at,
          jobbeur:users!user_mission_candidature_jobbeur_id_fkey (
            id,
            email,
            user_profil (
              nom,
              prenom,
              photo_url,
              ville,
              type_de_profil
            )
          )
        ),
        likes:user_mission_likes (
          id,
          user_id
        ),
        comments:user_mission_comments (
          id,
          user_id,
          comment,
          created_at
        )
      `)
      .eq('user_id', userId)
      .order('created_at', { ascending: false });

    if (missionsError) {
      logger.error('Erreur lors de la récupération des missions:', missionsError);
      throw missionsError;
    }

    // Transformer les données pour le frontend
    const transformedMissions = missions?.map(mission => ({
      id: mission.id,
      title: mission.titre,
      description: mission.description,
      category: mission.category_id,
      subcategory: mission.subcategory_id,
      status: mission.statut,
      budget: mission.budget,
      budget_defini: mission.budget_defini,
      created_at: mission.created_at,
      updated_at: mission.updated_at,
      date_mission: mission.date_mission,
      location: mission.ville || mission.adresse,
      adresse: mission.adresse,
      ville: mission.ville,
      code_postal: mission.code_postal,
      is_urgent: mission.is_urgent,
      is_closed: mission.is_closed,
      payment_method: mission.payment_method,
      photos: mission.photos || [],
      applications_count: mission.candidatures?.length || 0,
      candidatures: mission.candidatures?.map((candidature: any) => ({
        id: candidature.id,
        jobbeur_id: candidature.jobbeur_id,
        statut: candidature.statut,
        message: candidature.message,
        montant_propose: candidature.montant_propose,
        montant_contre_offre: candidature.montant_contre_offre,
        message_contre_offre: candidature.message_contre_offre,
        date_contre_offre: candidature.date_contre_offre,
        montant_contre_offre_jobbeur: candidature.montant_contre_offre_jobbeur,
        message_contre_offre_jobbeur: candidature.message_contre_offre_jobbeur,
        date_contre_offre_jobbeur: candidature.date_contre_offre_jobbeur,
        date_refus: candidature.date_refus,
        date_acceptation: candidature.date_acceptation,
        montant_paiement: candidature.montant_paiement,
        created_at: candidature.created_at,
        updated_at: candidature.updated_at,
        jobbeur: candidature.jobbeur ? {
          id: candidature.jobbeur.id,
          email: candidature.jobbeur.email,
          nom: candidature.jobbeur.user_profil?.nom,
          prenom: candidature.jobbeur.user_profil?.prenom,
          photo_url: candidature.jobbeur.user_profil?.photo_url,
          ville: candidature.jobbeur.user_profil?.ville,
          type_de_profil: candidature.jobbeur.user_profil?.type_de_profil
        } : null
      })) || [],
      likes_count: mission.likes?.length || 0,
      comments_count: mission.comments?.length || 0,
      comments: mission.comments || []
    })) || [];

    // Récupérer toutes les candidatures pour ces missions
    const { data: allCandidatures, error: candidaturesError } = await supabase
      .from('user_mission_candidature')
      .select(`
        *,
        jobbeur:users!user_mission_candidature_jobbeur_id_fkey (
          id,
          email,
          profil:user_profil (
            nom,
            prenom,
            photo_url,
            ville,
            type_de_profil
          )
        )
      `)
      .in('mission_id', transformedMissions.map(m => m.id));

    if (candidaturesError) {
      logger.error('Erreur lors de la récupération des candidatures:', candidaturesError);
      return res.status(500).json({
        success: false,
        message: 'Erreur lors de la récupération des candidatures',
        toastType: 'error'
      });
    }

    // Décrypter les données des jobbeurs dans les candidatures
    const decryptedCandidatures = await Promise.all(
      (allCandidatures || []).map(async (candidature) => {
        if (candidature.jobbeur) {
          // Décrypter les données utilisateur (email)
          const decryptedUser = await decryptUserDataAsync(candidature.jobbeur);
          
          // Décrypter les données du profil si elles existent
          let decryptedProfil = null;
          if (candidature.jobbeur.profil && candidature.jobbeur.profil.length > 0) {
            decryptedProfil = await decryptProfilDataAsync(candidature.jobbeur.profil[0]);
          }

          return {
            ...candidature,
            jobbeur: {
              ...decryptedUser,
              profil: decryptedProfil
            }
          };
        }
        return candidature;
      })
    );

    // Grouper les candidatures décryptées par mission_id
    const candidaturesByMission: { [key: string]: any[] } = {};
    decryptedCandidatures.forEach(candidature => {
      if (!candidaturesByMission[candidature.mission_id]) {
        candidaturesByMission[candidature.mission_id] = [];
      }
      candidaturesByMission[candidature.mission_id].push({
        id: candidature.id,
        jobbeur_id: candidature.jobbeur_id,
        statut: candidature.statut,
        message: candidature.message,
        montant_propose: candidature.montant_propose,
        montant_contre_offre: candidature.montant_contre_offre,
        created_at: candidature.created_at,
        jobbeur: candidature.jobbeur ? {
          id: candidature.jobbeur.id,
          email: candidature.jobbeur.email,
          nom: candidature.jobbeur.profil?.nom,
          prenom: candidature.jobbeur.profil?.prenom,
          photo_url: candidature.jobbeur.profil?.photo_url,
          ville: candidature.jobbeur.profil?.ville,
          type_de_profil: candidature.jobbeur.profil?.type_de_profil
        } : null
      });
    });

    // Mettre à jour les missions avec les candidatures décryptées
    const finalMissions = transformedMissions.map(mission => ({
      ...mission,
      candidatures: candidaturesByMission[mission.id] || [],
      applications_count: candidaturesByMission[mission.id]?.length || 0
    }));

    // Debug : afficher les candidatures décryptées
    logger.info(`Candidatures décryptées pour l'utilisateur ${userId}:`, {
      totalCandidatures: decryptedCandidatures.length,
      candidaturesByMission: Object.keys(candidaturesByMission).map(missionId => ({
        missionId,
        count: candidaturesByMission[missionId].length,
        candidatures: candidaturesByMission[missionId].map(c => ({
          id: c.id,
          jobbeur_email: c.jobbeur?.email,
          jobbeur_nom: c.jobbeur?.nom,
          jobbeur_prenom: c.jobbeur?.prenom,
          statut: c.statut
        }))
      }))
    });

    res.json({
      success: true,
      data: finalMissions
    });

  } catch (error) {
    logger.error('Erreur lors de la récupération des missions utilisateur:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur lors de la récupération des missions utilisateur',
      toastType: 'error'
    });
  }
};

export const generateUserReport = async (req: Request, res: Response) => {
  try {
    const { userId } = req.params;
    const { reportType = 'complete', timeRange = '90' } = req.query;

    if (!userId) {
      return res.status(400).json({
        success: false,
        message: 'ID utilisateur requis',
        toastType: 'error'
      });
    }

    // Calculer les dates
    const endDate = new Date();
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - parseInt(timeRange as string));

    // Récupération des informations utilisateur de base
    const { data: userInfo, error: userError } = await supabase
      .from('users')
      .select(`
        *,
        user_profil(*),
        user_ai_credits(*)
      `)
      .eq('id', userId)
      .single();

    if (userError) {
      logger.error('Erreur lors de la récupération des informations utilisateur:', userError);
      return res.status(500).json({
        success: false,
        message: 'Erreur lors de la récupération des informations utilisateur',
        toastType: 'error'
      });
    }

    // Récupération parallèle de toutes les statistiques avec de vraies requêtes SQL
    const [
      actionsHistory,
      loginHistory,
      userMissions,
      missionApplications,
      jobiTransactions,
      userServices,
      userGalleries,
      featuredPhotos,
      userReviews,
      userBadges,
      reportsMade,
      reportsReceived,
      warningsReceived,
      suspensionsCount,
      contentModerated,
      aiCreditsHistory,
      aiImageGenerations,
      openrouterUsage,
      // Nouvelles données ajoutées
      missionPhotos,
      galleryPhotos,
      userNotifications,
      userTransactions,
      userAbonnements,
      userMessages,
      userConversations,
      userMissionLikes,
      userMissionComments,
      userSeoConsents,
      userAiPrompts,
      userAiConsents,
      // Nouvelles données ajoutées
      reviewsGiven,
      invoicesCreated,
      missionEarnings
    ] = await Promise.all([
      // Historique des actions
      supabase
        .from('user_actions_history')
        .select('action_type, action_date')
        .eq('user_id', userId)
        .gte('action_date', startDate.toISOString())
        .lte('action_date', endDate.toISOString()),

      // Historique des connexions
      supabase
        .from('user_login_history')
        .select('login_date')
        .eq('user_id', userId)
        .gte('login_date', startDate.toISOString())
        .lte('login_date', endDate.toISOString()),

      // Missions créées
      supabase
        .from('user_missions')
        .select('id, created_at, statut, budget')
        .eq('user_id', userId)
        .gte('created_at', startDate.toISOString())
        .lte('created_at', endDate.toISOString()),

      // Candidatures envoyées
      supabase
        .from('user_mission_candidature')
        .select('id, created_at, statut, montant_propose')
        .eq('jobbeur_id', userId)
        .gte('created_at', startDate.toISOString())
        .lte('created_at', endDate.toISOString()),

      // Transactions Jobi
      supabase
        .from('user_jobi_historique')
        .select('montant, type_operation, date_creation')
        .eq('user_id', userId)
        .gte('date_creation', startDate.toISOString())
        .lte('date_creation', endDate.toISOString()),

      // Services créés
      supabase
        .from('user_services')
        .select('id, created_at')
        .eq('user_id', userId)
        .gte('created_at', startDate.toISOString())
        .lte('created_at', endDate.toISOString()),

      // Galeries créées
      supabase
        .from('user_gallery')
        .select('id, created_at')
        .eq('user_id', userId)
        .gte('created_at', startDate.toISOString())
        .lte('created_at', endDate.toISOString()),

      // Photos mises en avant
      supabase
        .from('user_featured_photos')
        .select('id, created_at')
        .eq('user_id', userId)
        .gte('created_at', startDate.toISOString())
        .lte('created_at', endDate.toISOString()),

      // Avis reçus
      supabase
        .from('user_reviews')
        .select('id, note, created_at')
        .eq('target_user_id', userId)
        .gte('created_at', startDate.toISOString())
        .lte('created_at', endDate.toISOString()),

      // Badges obtenus
      supabase
        .from('user_badges')
        .select('id, date_obtention')
        .eq('user_id', userId)
        .gte('date_obtention', startDate.toISOString())
        .lte('date_obtention', endDate.toISOString()),

      // Signalements faits
      supabase
        .from('reported_content')
        .select('id, created_at')
        .eq('reported_by', userId)
        .gte('created_at', startDate.toISOString())
        .lte('created_at', endDate.toISOString()),

      // Signalements reçus (approximation)
      supabase
        .from('reported_content')
        .select('id, created_at, content_type')
        .eq('reported_user_id', userId)
        .gte('created_at', startDate.toISOString())
        .lte('created_at', endDate.toISOString()),

      // Avertissements reçus
      supabase
        .from('user_notifications')
        .select('id, created_at')
        .eq('user_id', userId)
        .eq('type', 'warning')
        .gte('created_at', startDate.toISOString())
        .lte('created_at', endDate.toISOString()),

      // Suspensions
      supabase
        .from('user_notifications')
        .select('id, created_at')
        .eq('user_id', userId)
        .eq('type', 'suspension')
        .gte('created_at', startDate.toISOString())
        .lte('created_at', endDate.toISOString()),

      // Contenu modéré
      supabase
        .from('user_notifications')
        .select('id, created_at')
        .eq('user_id', userId)
        .eq('type', 'moderation')
        .gte('created_at', startDate.toISOString())
        .lte('created_at', endDate.toISOString()),

      // Historique des crédits IA
      supabase
        .from('user_ai_credits_historique')
        .select('montant, operation_type, created_at')
        .eq('user_id', userId)
        .gte('created_at', startDate.toISOString())
        .lte('created_at', endDate.toISOString()),

      // Générations d'images IA
      supabase
        .from('ai_image_generation_stats')
        .select('id, created_at')
        .eq('user_id', userId)
        .gte('created_at', startDate.toISOString())
        .lte('created_at', endDate.toISOString()),

      // Utilisation OpenRouter
      supabase
        .from('openrouter_api_usage')
        .select('total_tokens, created_at')
        .eq('user_id', userId)
        .gte('created_at', startDate.toISOString())
        .lte('created_at', endDate.toISOString()),

      // Photos de missions
      supabase
        .from('user_mission_photos')
        .select('id, created_at')
        .eq('user_id', userId)
        .gte('created_at', startDate.toISOString())
        .lte('created_at', endDate.toISOString()),

      // Photos de galerie
      supabase
        .from('user_gallery_photos')
        .select('id, created_at')
        .eq('user_id', userId)
        .gte('created_at', startDate.toISOString())
        .lte('created_at', endDate.toISOString()),

      // Notifications utilisateur
      supabase
        .from('user_notifications')
        .select('id, type, created_at')
        .eq('user_id', userId)
        .gte('created_at', startDate.toISOString())
        .lte('created_at', endDate.toISOString()),

      // Transactions utilisateur
      supabase
        .from('user_transac')
        .select('id, type, montant, statut, created_at')
        .eq('user_id', userId)
        .gte('created_at', startDate.toISOString())
        .lte('created_at', endDate.toISOString()),

      // Abonnements utilisateur
      supabase
        .from('user_abo')
        .select('id, type_abonnement, statut, montant, created_at')
        .eq('user_id', userId)
        .gte('created_at', startDate.toISOString())
        .lte('created_at', endDate.toISOString()),

      // Messages envoyés
      supabase
        .from('user_messages')
        .select('id, created_at')
        .eq('sender_id', userId)
        .gte('created_at', startDate.toISOString())
        .lte('created_at', endDate.toISOString()),

      // Conversations
      supabase
        .from('user_messages_conversations')
        .select('id, created_at')
        .or(`user1_id.eq.${userId},user2_id.eq.${userId}`)
        .gte('created_at', startDate.toISOString())
        .lte('created_at', endDate.toISOString()),

      // Likes de missions
      supabase
        .from('user_mission_likes')
        .select('id, created_at')
        .eq('user_id', userId)
        .gte('created_at', startDate.toISOString())
        .lte('created_at', endDate.toISOString()),

      // Commentaires de missions
      supabase
        .from('user_mission_comments')
        .select('id, created_at')
        .eq('user_id', userId)
        .gte('created_at', startDate.toISOString())
        .lte('created_at', endDate.toISOString()),

      // Consentements SEO
      supabase
        .from('user_seo_consent')
        .select('id, consent_given, created_at')
        .eq('user_id', userId)
        .gte('created_at', startDate.toISOString())
        .lte('created_at', endDate.toISOString()),

      // Prompts IA personnalisés
      supabase
        .from('user_ai_prompts')
        .select('id, type, created_at')
        .eq('user_id', userId)
        .gte('created_at', startDate.toISOString())
        .lte('created_at', endDate.toISOString()),

      // Consentements IA
      supabase
        .from('ai_user_consents')
        .select('id, is_active, created_at')
        .eq('user_id', userId)
        .gte('created_at', startDate.toISOString())
        .lte('created_at', endDate.toISOString()),

      // Avis donnés par l'utilisateur
      supabase
        .from('user_reviews')
        .select('id, note, created_at')
        .eq('author_id', userId)
        .gte('created_at', startDate.toISOString())
        .lte('created_at', endDate.toISOString()),

      // Factures créées
      supabase
        .from('user_transac')
        .select('id, montant, created_at')
        .eq('user_id', userId)
        .eq('categorie', 'facture')
        .gte('created_at', startDate.toISOString())
        .lte('created_at', endDate.toISOString()),

      // Gains de missions (candidatures acceptées avec paiement)
      supabase
        .from('user_mission_candidature')
        .select('id, montant_paiement, payment_date, statut')
        .eq('jobbeur_id', userId)
        .eq('statut', 'acceptée')
        .not('montant_paiement', 'is', null)
        .gte('payment_date', startDate.toISOString())
        .lte('payment_date', endDate.toISOString())
    ]);

    // Traitement des statistiques d'activité
    const activityStats = {
      total_actions: actionsHistory.data?.length || 0,
      login_count: loginHistory.data?.length || 0,
      missions_created: userMissions.data?.length || 0,
      applications_sent: missionApplications.data?.length || 0,
      reviews_given: reviewsGiven.data?.length || 0,
      photos_uploaded: (missionPhotos.data?.length || 0) + (galleryPhotos.data?.length || 0)
    };

    // Traitement des statistiques financières
    const jobiData = jobiTransactions.data || [];
    const invoicesData = invoicesCreated.data || [];
    const earningsData = missionEarnings.data || [];
    
    const financialStats = {
      // Données Jobi
      jobi_earned: jobiData.filter(t => t.type_operation === 'gain').reduce((sum, t) => sum + (t.montant || 0), 0),
      jobi_spent: jobiData.filter(t => t.type_operation === 'depense').reduce((sum, t) => sum + Math.abs(t.montant || 0), 0),
      total_earned: jobiData.filter(t => t.type_operation === 'gain').reduce((sum, t) => sum + (t.montant || 0), 0),
      total_spent: jobiData.filter(t => t.type_operation === 'depense').reduce((sum, t) => sum + Math.abs(t.montant || 0), 0),
      transactions_count: jobiData.length,
      average_transaction: jobiData.length > 0 ? 
        Math.round((jobiData.reduce((sum, t) => sum + Math.abs(t.montant || 0), 0) / jobiData.length) * 100) / 100 : 0,
      
      // Factures
      invoices_created: invoicesData.length,
      total_invoice_amount: invoicesData.reduce((sum, inv) => sum + (inv.montant || 0), 0),
      
      // Gains de missions
      mission_earnings: earningsData.reduce((sum, earning) => sum + (earning.montant_paiement || 0), 0)
    };

    // Traitement des statistiques de contenu
    const reviewsData = userReviews.data || [];
    const contentStats = {
      services_created: userServices.data?.length || 0,
      galleries_created: userGalleries.data?.length || 0,
      featured_photos_added: featuredPhotos.data?.length || 0,
      reviews_received: reviewsData.length,
      average_rating: reviewsData.length > 0 ? 
        Math.round((reviewsData.reduce((sum, r) => sum + (r.note || 0), 0) / reviewsData.length) * 100) / 100 : 0,
      badges_earned: userBadges.data?.length || 0
    };

    // Traitement des statistiques de modération
    const moderationStats = {
      reports_made: reportsMade.data?.length || 0,
      reports_received: reportsReceived.data?.length || 0,
      warnings_received: warningsReceived.data?.length || 0,
      suspensions_count: suspensionsCount.data?.length || 0,
      content_moderated: contentModerated.data?.length || 0
    };

    // Traitement des statistiques IA
    const aiUsageStats = {
      credits_used: aiCreditsHistory.data?.filter(c => c.operation_type === 'utilisation').reduce((sum, c) => sum + Math.abs(c.montant || 0), 0) || 0,
      credits_received: aiCreditsHistory.data?.filter(c => ['achat_jobi', 'achat_stripe', 'offert_abonnement', 'offert_admin'].includes(c.operation_type)).reduce((sum, c) => sum + (c.montant || 0), 0) || 0,
      generations_count: aiImageGenerations.data?.length || 0,
      tokens_used: openrouterUsage.data?.reduce((sum, u) => sum + (u.total_tokens || 0), 0) || 0,
      custom_prompts_count: userAiPrompts.data?.length || 0,
      ai_consent_active: userAiConsents.data?.some(c => c.is_active) || false
    };

    // Traitement des statistiques de photos et médias
    const mediaStats = {
      mission_photos_uploaded: missionPhotos.data?.length || 0,
      gallery_photos_uploaded: galleryPhotos.data?.length || 0,
      total_photos_uploaded: (missionPhotos.data?.length || 0) + (galleryPhotos.data?.length || 0)
    };

    // Traitement des statistiques de communication
    const communicationStats = {
      notifications_received: userNotifications.data?.length || 0,
      notifications_by_type: userNotifications.data?.reduce((acc: any, notif: any) => {
        acc[notif.type] = (acc[notif.type] || 0) + 1;
        return acc;
      }, {}) || {},
      messages_sent: userMessages.data?.length || 0,
      conversations_started: userConversations.data?.length || 0
    };

    // Traitement des statistiques d'engagement
    const engagementStats = {
      mission_likes_given: userMissionLikes.data?.length || 0,
      mission_comments_made: userMissionComments.data?.length || 0,
      seo_consent_given: userSeoConsents.data?.some(c => c.consent_given) || false
    };

    // Traitement des statistiques de transactions avancées
    const transactionsData = userTransactions.data || [];
    const advancedFinancialStats = {
      total_transactions: transactionsData.length,
      transactions_by_type: transactionsData.reduce((acc: any, trans: any) => {
        acc[trans.type] = (acc[trans.type] || 0) + 1;
        return acc;
      }, {}),
      transactions_by_status: transactionsData.reduce((acc: any, trans: any) => {
        acc[trans.statut] = (acc[trans.statut] || 0) + 1;
        return acc;
      }, {}),
      total_transaction_amount: transactionsData.reduce((sum, trans) => sum + Math.abs(trans.montant || 0), 0),
      successful_transactions: transactionsData.filter(t => t.statut === 'complete').length,
      pending_transactions: transactionsData.filter(t => t.statut === 'en_attente').length,
      failed_transactions: transactionsData.filter(t => t.statut === 'refuse').length
    };

    // Traitement des statistiques d'abonnement
    const subscriptionStats = {
      subscriptions_count: userAbonnements.data?.length || 0,
      active_subscriptions: userAbonnements.data?.filter(abo => abo.statut === 'actif').length || 0,
      subscription_types: userAbonnements.data?.reduce((acc: any, abo: any) => {
        acc[abo.type_abonnement] = (acc[abo.type_abonnement] || 0) + 1;
        return acc;
      }, {}) || {},
      total_subscription_amount: userAbonnements.data?.reduce((sum, abo) => sum + (abo.montant || 0), 0) || 0
    };

    // Structurer la réponse selon le format attendu par le frontend
    const report = {
      user: userInfo,
      period: {
        start: startDate.toISOString(),
        end: endDate.toISOString(),
        days: parseInt(timeRange as string)
      },
      // Statistiques principales
      activity: activityStats,
      financial: financialStats,
      content: contentStats,
      moderation: moderationStats,
      aiUsage: aiUsageStats,
      
      // Nouvelles statistiques détaillées
      media: mediaStats,
      communication: communicationStats,
      engagement: engagementStats,
      advancedFinancial: advancedFinancialStats,
      subscription: subscriptionStats,
      
      // Métadonnées du rapport
      generatedAt: new Date().toISOString(),
      reportType,
      
      // Résumé global
      summary: {
        total_data_points: [
          activityStats.total_actions,
          financialStats.transactions_count,
          contentStats.services_created,
          mediaStats.total_photos_uploaded,
          communicationStats.messages_sent,
          engagementStats.mission_likes_given,
          moderationStats.reports_made,
          aiUsageStats.generations_count
        ].reduce((sum, val) => sum + val, 0),
        
        user_engagement_score: Math.round(
          (activityStats.total_actions * 0.2 +
           contentStats.services_created * 0.3 +
           engagementStats.mission_likes_given * 0.1 +
           communicationStats.messages_sent * 0.2 +
           mediaStats.total_photos_uploaded * 0.2) * 100
        ) / 100,
        
        financial_activity_score: Math.round(
          (financialStats.transactions_count * 0.4 +
           advancedFinancialStats.successful_transactions * 0.6) * 100
        ) / 100,
        
        content_creation_score: Math.round(
          (contentStats.services_created * 0.4 +
           mediaStats.total_photos_uploaded * 0.3 +
           contentStats.galleries_created * 0.3) * 100
        ) / 100
      }
    };

    res.json({
      success: true,
      data: report
    });

  } catch (error) {
    logger.error('Erreur dans generateUserReport:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur serveur lors de la génération du rapport',
      toastType: 'error'
    });
  }
};

/**
 * Mettre à jour le profil d'un utilisateur (admin/modo)
 */
export const updateUserProfile = async (req: Request, res: Response) => {
  try {
    const { userId } = req.params;
    const adminUserId = req.user?.userId;
    const adminRole = req.user?.role;

    if (!userId) {
      return res.status(400).json({
        success: false,
        message: 'ID utilisateur manquant',
        toastType: 'error'
      });
    }

    // Vérifier que l'utilisateur existe
    const { data: existingUser, error: userError } = await supabase
      .from('users')
      .select('id, email, role')
      .eq('id', userId)
      .single();

    if (userError || !existingUser) {
      return res.status(404).json({
        success: false,
        message: 'Utilisateur non trouvé',
        toastType: 'error'
      });
    }

    // Schéma de validation pour les données de profil
    const profileUpdateSchema = z.object({
      // Données utilisateur (table users)
      email: z.string().email().optional(),
      role: z.enum(['jobutil', 'jobpadm', 'jobmodo']).optional(),
      user_type: z.enum(['jobbeur', 'non-jobbeur']).optional(),
      profil_actif: z.boolean().optional(),
      email_verifier: z.boolean().optional(),
      profil_verifier: z.boolean().optional(),
      identite_verifier: z.boolean().optional(),
      entreprise_verifier: z.boolean().optional(),
      assurance_verifier: z.boolean().optional(),

      // Données profil (table user_profil) - tous les champs disponibles
      nom: z.string().min(1).max(100).optional(),
      prenom: z.string().min(1).max(100).optional(),
      telephone: z.string().max(20).optional(),
      telephone_prive: z.boolean().optional(),
      numero: z.string().max(10).optional(),
      adresse: z.string().max(255).optional(),
      ville: z.string().max(100).optional(),
      code_postal: z.string().max(10).optional(),
      pays: z.string().max(100).optional(),
      bio: z.string().max(1000).optional(),
      slogan: z.string().max(200).optional(),
      mode_vacance: z.boolean().optional(),
      profil_visible: z.boolean().optional(),
      seo_indexable: z.boolean().optional(),

      // Champs entreprise
      type_de_profil: z.enum(['particulier', 'entreprise']).optional(),
      nom_entreprise: z.string().max(255).optional(),
      prenom_entreprise: z.string().max(255).optional(),
      statut_entreprise: z.string().max(100).optional(),
      siren_entreprise: z.string().max(14).optional(),
      code_ape_entreprise: z.string().max(10).optional(),
      categorie_entreprise: z.string().max(100).optional(),
      effectif_entreprise: z.string().max(50).optional(),

      // Zone d'intervention
      intervention_zone: z.object({
        center: z.array(z.number()).length(2),
        radius: z.number().min(1).max(100)
      }).optional(),

      // Métadonnées
      reason: z.string().optional(),
      sendNotification: z.boolean().default(true),
      sendEmail: z.boolean().default(true),

      // Champs modifiés (pour savoir quoi mettre à jour)
      modifiedFields: z.array(z.string()).optional()
    });

    const validationResult = profileUpdateSchema.safeParse(req.body);
    if (!validationResult.success) {
      return res.status(400).json({
        success: false,
        message: 'Données invalides',
        errors: validationResult.error.errors,
        toastType: 'error'
      });
    }

    const {
      email, role, user_type, profil_actif, email_verifier, profil_verifier,
      identite_verifier, entreprise_verifier, assurance_verifier,
      nom, prenom, telephone, telephone_prive, numero, adresse, ville,
      code_postal, pays, bio, slogan, mode_vacance, profil_visible,
      seo_indexable, type_de_profil, nom_entreprise, prenom_entreprise,
      statut_entreprise, siren_entreprise, code_ape_entreprise,
      categorie_entreprise, effectif_entreprise, intervention_zone,
      reason, sendNotification, sendEmail, modifiedFields
    } = validationResult.data;

    // Vérifier les permissions pour la modification du rôle
    if (role !== undefined && adminRole !== 'jobpadm') {
      return res.status(403).json({
        success: false,
        message: 'Seuls les administrateurs peuvent modifier les rôles',
        toastType: 'error'
      });
    }

    const updates: any = {};
    const profileUpdates: any = {};
    let hasUserUpdates = false;
    let hasProfileUpdates = false;

    // Utiliser modifiedFields si fourni, sinon traiter tous les champs définis
    const fieldsToUpdate = modifiedFields || Object.keys(validationResult.data);

    // Préparer les mises à jour pour la table users (seulement si modifiés)
    if (fieldsToUpdate.includes('email') && email !== undefined) {
      updates.email = email;
      hasUserUpdates = true;
    }
    if (fieldsToUpdate.includes('role') && role !== undefined) {
      updates.role = role;
      hasUserUpdates = true;
    }
    if (fieldsToUpdate.includes('user_type') && user_type !== undefined) {
      updates.user_type = user_type;
      hasUserUpdates = true;
    }
    if (fieldsToUpdate.includes('profil_actif') && profil_actif !== undefined) {
      updates.profil_actif = profil_actif;
      hasUserUpdates = true;
    }
    if (fieldsToUpdate.includes('email_verifier') && email_verifier !== undefined) {
      updates.email_verifier = email_verifier;
      hasUserUpdates = true;
    }
    if (fieldsToUpdate.includes('profil_verifier') && profil_verifier !== undefined) {
      updates.profil_verifier = profil_verifier;
      hasUserUpdates = true;
    }
    if (fieldsToUpdate.includes('identite_verifier') && identite_verifier !== undefined) {
      updates.identite_verifier = identite_verifier;
      hasUserUpdates = true;
    }
    if (fieldsToUpdate.includes('entreprise_verifier') && entreprise_verifier !== undefined) {
      updates.entreprise_verifier = entreprise_verifier;
      hasUserUpdates = true;
    }
    if (fieldsToUpdate.includes('assurance_verifier') && assurance_verifier !== undefined) {
      updates.assurance_verifier = assurance_verifier;
      hasUserUpdates = true;
    }

    // Préparer les mises à jour pour la table user_profil (seulement si modifiés)
    if (fieldsToUpdate.includes('nom') && nom !== undefined) {
      profileUpdates.nom = nom;
      hasProfileUpdates = true;
    }
    if (fieldsToUpdate.includes('prenom') && prenom !== undefined) {
      profileUpdates.prenom = prenom;
      hasProfileUpdates = true;
    }
    if (fieldsToUpdate.includes('telephone') && telephone !== undefined) {
      profileUpdates.telephone = telephone;
      hasProfileUpdates = true;
    }
    if (fieldsToUpdate.includes('telephone_prive') && telephone_prive !== undefined) {
      profileUpdates.telephone_prive = telephone_prive;
      hasProfileUpdates = true;
    }
    if (fieldsToUpdate.includes('numero') && numero !== undefined) {
      profileUpdates.numero = numero;
      hasProfileUpdates = true;
    }
    if (fieldsToUpdate.includes('adresse') && adresse !== undefined) {
      profileUpdates.adresse = adresse;
      hasProfileUpdates = true;
    }
    if (fieldsToUpdate.includes('ville') && ville !== undefined) {
      profileUpdates.ville = ville;
      hasProfileUpdates = true;
    }
    if (fieldsToUpdate.includes('code_postal') && code_postal !== undefined) {
      profileUpdates.code_postal = code_postal;
      hasProfileUpdates = true;
    }
    if (fieldsToUpdate.includes('pays') && pays !== undefined) {
      profileUpdates.pays = pays;
      hasProfileUpdates = true;
    }
    if (fieldsToUpdate.includes('bio') && bio !== undefined) {
      profileUpdates.bio = bio;
      hasProfileUpdates = true;
    }
    if (fieldsToUpdate.includes('slogan') && slogan !== undefined) {
      profileUpdates.slogan = slogan;
      hasProfileUpdates = true;
    }
    if (fieldsToUpdate.includes('mode_vacance') && mode_vacance !== undefined) {
      profileUpdates.mode_vacance = mode_vacance;
      hasProfileUpdates = true;
    }
    if (fieldsToUpdate.includes('profil_visible') && profil_visible !== undefined) {
      profileUpdates.profil_visible = profil_visible;
      hasProfileUpdates = true;
    }
    if (fieldsToUpdate.includes('seo_indexable') && seo_indexable !== undefined) {
      profileUpdates.seo_indexable = seo_indexable;
      hasProfileUpdates = true;
    }
    if (fieldsToUpdate.includes('type_de_profil') && type_de_profil !== undefined) {
      profileUpdates.type_de_profil = type_de_profil;
      hasProfileUpdates = true;
    }
    if (fieldsToUpdate.includes('nom_entreprise') && nom_entreprise !== undefined) {
      profileUpdates.nom_entreprise = nom_entreprise;
      hasProfileUpdates = true;
    }
    if (fieldsToUpdate.includes('prenom_entreprise') && prenom_entreprise !== undefined) {
      profileUpdates.prenom_entreprise = prenom_entreprise;
      hasProfileUpdates = true;
    }
    if (fieldsToUpdate.includes('statut_entreprise') && statut_entreprise !== undefined) {
      profileUpdates.statut_entreprise = statut_entreprise;
      hasProfileUpdates = true;
    }
    if (fieldsToUpdate.includes('siren_entreprise') && siren_entreprise !== undefined) {
      profileUpdates.siren_entreprise = siren_entreprise;
      hasProfileUpdates = true;
    }
    if (fieldsToUpdate.includes('code_ape_entreprise') && code_ape_entreprise !== undefined) {
      profileUpdates.code_ape_entreprise = code_ape_entreprise;
      hasProfileUpdates = true;
    }
    if (fieldsToUpdate.includes('categorie_entreprise') && categorie_entreprise !== undefined) {
      profileUpdates.categorie_entreprise = categorie_entreprise;
      hasProfileUpdates = true;
    }
    if (fieldsToUpdate.includes('effectif_entreprise') && effectif_entreprise !== undefined) {
      profileUpdates.effectif_entreprise = effectif_entreprise;
      hasProfileUpdates = true;
    }
    if (fieldsToUpdate.includes('intervention_zone') && intervention_zone !== undefined) {
      profileUpdates.intervention_zone = intervention_zone;
      hasProfileUpdates = true;
    }

    if (!hasUserUpdates && !hasProfileUpdates) {
      return res.status(400).json({
        success: false,
        message: 'Aucune donnée à mettre à jour',
        toastType: 'error'
      });
    }

    // Crypter les données sensibles du profil si nécessaire
    let encryptedProfileUpdates = profileUpdates;
    if (hasProfileUpdates) {
      encryptedProfileUpdates = await encryptProfilDataAsync(profileUpdates);
    }

    // Crypter les données sensibles utilisateur si nécessaire
    let encryptedUserUpdates = updates;
    if (hasUserUpdates && updates.email) {
      encryptedUserUpdates = await encryptUserDataAsync(updates);
    }

    // Effectuer les mises à jour
    if (hasUserUpdates) {
      encryptedUserUpdates.updated_at = new Date().toISOString();

      const { error: userUpdateError } = await supabase
        .from('users')
        .update(encryptedUserUpdates)
        .eq('id', userId);

      if (userUpdateError) {
        logger.error('Erreur lors de la mise à jour des données utilisateur:', userUpdateError);
        return res.status(500).json({
          success: false,
          message: 'Erreur lors de la mise à jour des données utilisateur',
          toastType: 'error'
        });
      }
    }

    if (hasProfileUpdates) {
      encryptedProfileUpdates.updated_at = new Date().toISOString();

      const { error: profileUpdateError } = await supabase
        .from('user_profil')
        .update(encryptedProfileUpdates)
        .eq('user_id', userId);

      if (profileUpdateError) {
        logger.error('Erreur lors de la mise à jour du profil utilisateur:', profileUpdateError);
        return res.status(500).json({
          success: false,
          message: 'Erreur lors de la mise à jour du profil utilisateur',
          toastType: 'error'
        });
      }
    }

    // Logger l'activité admin
    try {
      await logUserActivity(
        adminUserId || 'system',
        'admin_profile_update',
        `Modification du profil utilisateur ${userId}`,
        getIpFromRequest(req),
        {
          targetUserId: userId,
          updatedFields: Object.keys({ ...updates, ...profileUpdates }),
          reason: reason || 'Modification administrative',
          adminRole
        }
      );
    } catch (logError) {
      logger.error('Erreur lors du logging de l\'activité admin:', logError);
    }

    // Envoyer une notification à l'utilisateur
    if (sendNotification) {
      try {
        await supabase
          .from('user_notifications')
          .insert({
            user_id: userId,
            type: 'profile_update',
            title: 'Profil mis à jour par un administrateur',
            content: `Votre profil a été mis à jour par un administrateur.${reason ? ` Motif: ${reason}` : ''}`,
            link: '/dashboard/profil',
            is_read: false,
            is_archived: false
          });
      } catch (notifError) {
        logger.error('Erreur lors de l\'envoi de la notification:', notifError);
      }
    }

    // Envoyer un email à l'utilisateur
    if (sendEmail && existingUser.email) {
      try {
        const decryptedEmail = await decryptUserDataAsync({ email: existingUser.email });
        await queueEmail(
          decryptedEmail.email,
          'Mise à jour de votre profil JobPartiel',
          `
          <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
            <h2 style="color: #FF6B2C;">Mise à jour de profil</h2>
            <p>Bonjour,</p>
            <p>Votre profil JobPartiel a été mis à jour par un administrateur.</p>
            ${reason ? `<p><strong>Motif :</strong> ${reason}</p>` : ''}
            <p>Vous pouvez consulter les modifications dans votre espace personnel.</p>
            <p>Si vous avez des questions, n'hésitez pas à contacter notre support.</p>
            <p>Cordialement,<br>L'équipe JobPartiel</p>
          </div>
          `
        );
      } catch (emailError) {
        logger.error('Erreur lors de l\'envoi de l\'email de mise à jour profil:', emailError);
      }
    }

    // Invalider le cache
    await redis.del(`user_details:${userId}`);
    await redis.del(`user_stats:${userId}:*`);
    await redis.del(`user_profil:${userId}`);

    res.json({
      success: true,
      message: 'Profil utilisateur mis à jour avec succès',
      data: {
        userId,
        updatedFields: Object.keys({ ...updates, ...profileUpdates }),
        adminUserId,
        reason: reason || 'Modification administrative',
        timestamp: new Date().toISOString()
      },
      toastType: 'success'
    });

  } catch (error) {
    logger.error('Erreur dans updateUserProfile:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur serveur lors de la mise à jour du profil',
      toastType: 'error'
    });
  }
};

/**
 * Récupération de l'historique des activités d'un utilisateur spécifique (Admin)
 */
export const getUserActionsHistory = async (req: Request, res: Response) => {
  try {
    const { userId } = req.params;
    const page = parseInt(req.query.page as string) || 1;
    const limit = parseInt(req.query.limit as string) || 20;
    const type = (req.query.type as string) || 'all';
    const adminUserId = req.user?.userId;

    if (!userId) {
      return res.status(400).json({
        success: false,
        message: 'ID utilisateur requis',
        toastType: 'error'
      });
    }

    // Vérifier que l'utilisateur cible existe
    const { data: targetUser, error: userError } = await supabase
      .from('users')
      .select('id, email')
      .eq('id', userId)
      .single();

    if (userError || !targetUser) {
      return res.status(404).json({
        success: false,
        message: 'Utilisateur non trouvé',
        toastType: 'error'
      });
    }

    const offset = (page - 1) * limit;

    // Construire la requête de base
    let query = supabase
      .from('user_actions_history')
      .select('id, action_type, action_date, resource_id, resource_type, details, ip_address, created_at', { count: 'exact' })
      .eq('user_id', userId)
      .order('action_date', { ascending: false });

    // Filtrer par type d'action si spécifié
    if (type !== 'all') {
      query = query.eq('action_type', type);
    }

    // Récupérer les entrées paginées
    const { data, error, count } = await query.range(offset, offset + limit - 1);

    if (error) {
      logger.error('Erreur lors de la récupération de l\'historique des actions:', { error, userId, adminUserId });
      return res.status(500).json({
        success: false,
        message: 'Erreur lors de la récupération de l\'historique des actions',
        toastType: 'error'
      });
    }

    // Masquer partiellement les adresses IP pour la sécurité (même pour les admins)
    function maskIpAddress(ip: string) {
      if (!ip) return 'Inconnue';
      
      if (ip.includes('.')) {
        // IPv4
        const parts = ip.split('.');
        return `${parts[0]}.${parts[1]}.xxx.xxx`;
      } else if (ip.includes(':')) {
        // IPv6
        const parts = ip.split(':');
        const visibleParts = parts.slice(0, 2);
        return `${visibleParts.join(':')}:xxxx:xxxx:xxxx:xxxx`;
      }
      return 'xxx.xxx.xxx.xxx';
    }

    // Traiter les données pour l'affichage admin
    const processedData = data.map(item => ({
      ...item,
      ip_address: maskIpAddress(item.ip_address),
      // Ajouter des informations supplémentaires pour l'affichage
      formatted_date: new Date(item.action_date).toLocaleString('fr-FR', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
      }),
      // Parser les détails JSON si disponibles
      parsed_details: item.details ? (typeof item.details === 'string' ? JSON.parse(item.details) : item.details) : null
    }));

    // Récupérer les types d'actions distincts pour les filtres
    const { data: actionTypes } = await supabase
      .from('user_actions_history')
      .select('action_type')
      .eq('user_id', userId)
      .order('action_type');

    const distinctActionTypes = [...new Set(actionTypes?.map(item => item.action_type) || [])];

    // Déchiffrer l'email de l'utilisateur cible avant de l'envoyer au frontend
    const decryptedUserEmail = await decryptDataAsync(targetUser.email);

    // Logger l'accès admin à l'historique
    if (adminUserId) {
      await logUserActivity(
        adminUserId,
        'admin_view_user_history',
        userId,
        'user',
        JSON.stringify({
          target_user_id: userId,
          target_user_email: targetUser.email,
          page,
          limit,
          type,
          total_records: count
        }),
        getIpFromRequest(req)
      );
    }

    const result = {
      success: true,
      data: processedData,
      pagination: {
        total: count || 0,
        page,
        limit,
        totalPages: Math.ceil((count || 0) / limit)
      },
      filters: {
        availableTypes: distinctActionTypes,
        currentType: type
      },
      user: {
        id: targetUser.id,
        email: decryptedUserEmail,
      }
    };

    res.status(200).json(result);

  } catch (error) {
    logger.error('Exception lors de la récupération de l\'historique des actions:', { error });
    return res.status(500).json({
      success: false,
      message: 'Erreur serveur lors de la récupération de l\'historique des actions',
      toastType: 'error'
    });
  }
};

