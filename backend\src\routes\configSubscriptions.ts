import { Router, Request, Response } from 'express';
import { authMiddleware } from '../middleware/authMiddleware';
import { subscriptions } from '../config/ConfigSubscriptions';
import logger from '../utils/logger';
import { redis } from '../config/redis';
import rateLimit from 'express-rate-limit';
import { supabase } from '../config/supabase';
import { stripe, PORTAL_RETURN_URL } from '../config/stripe';
import { logUserActivity } from '../utils/activityLogger';
import { sendSubscriptionStatusChangeEmail } from '../services/emailService';
import { decryptProfilDataAsync, decryptUserDataAsync } from '../utils/encryption';
import { queueEmail } from '../services/emailQueueService';

const router = Router();

// Configuration du rate limiter
const configLimiter = rateLimit({
  windowMs: 1 * 30 * 1000, // 30 secondes
  max: 100, // 100 requêtes par IP
  message: { error: 'Trop de requêtes, veuillez réessayer dans 1 minute' },
  standardHeaders: true,
  legacyHeaders: false
});

// Application du rate limiter avant l'authentification
router.use(configLimiter);

// Middleware d'authentification pour toutes les routes
router.use(authMiddleware.authenticateToken);

// Route pour récupérer la configuration des abonnements donc config-subscriptions dans C:\Users\<USER>\CascadeProjects\Github OK\backend\src\config\ConfigSubscriptions.ts
router.get('/', async (req, res) => {
  try {
    const userId = req.user?.userId;
    if (!userId) {
      res.status(401).json({
        success: false,
        message: 'Utilisateur non authentifié',
        toastType: 'error'
      });
      return;
    }

    // Vérifier si la configuration est dans le cache
    const cacheKey = `config_subscriptions:${userId}`;
    const cachedConfig = await redis.get(cacheKey);

    if (cachedConfig) {
      logger.info('Récupération de la configuration des abonnements depuis le cache, cacheKey:', cachedConfig);
      res.json(JSON.parse(cachedConfig));
      return;
    }

    // Si pas en cache, retourner la configuration et la mettre en cache
    const configData = {
      success: true,
      data: subscriptions
    };

    // Mettre en cache pour 4 heure
    await redis.set(cacheKey, JSON.stringify(configData), 'EX', 14400);

    res.json(configData);
    return;

  } catch (error) {
    logger.error('Erreur lors de la récupération de la configuration des abonnements:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur lors de la récupération de la configuration des abonnements',
      toastType: 'error'
    });
    return;
  }
});

// Fonction pour récupérer les informations d'abonnement d'un utilisateur
const getUserSubscription = async (req: Request, res: Response) => {
  try {
    const userId = req.user?.userId;
    if (!userId) {
      res.status(401).json({
        success: false,
        message: 'Utilisateur non authentifié',
        toastType: 'error'
      });
      return;
    }

    // Vérifier si les informations d'abonnement sont dans le cache
    const cacheKey = `subscription_status:${userId}`;
    const cachedStatus = await redis.get(cacheKey);

    if (cachedStatus) {
      logger.info('Récupération du statut d\'abonnement depuis le cache', { userId });
      res.json(JSON.parse(cachedStatus));
      return;
    }

    // Utiliser la fonction getUserSubscriptionLimits pour obtenir toutes les informations d'abonnement
    const subscriptionInfo = await getUserSubscriptionLimits(userId);

    // Construire la réponse avec les informations d'abonnement
    const subscriptionData = {
      success: true,
      plan: subscriptionInfo.plan,
      startDate: subscriptionInfo.startDate || new Date().toISOString(),
      endDate: subscriptionInfo.endDate || null,
      autoRenew: subscriptionInfo.autoRenew,
      price: subscriptionInfo.price,
      options: subscriptionInfo.options,
      features: subscriptionInfo.features,
      // Inclure les limites spécifiques au plan pour chaque option
      limits: {
        services: subscriptionInfo.serviceLimit,
        galleries: subscriptionInfo.galleriesLimit,
        interventionAreas: subscriptionInfo.interventionAreasLimit,
        conversations_messages_prives: subscriptionInfo.conversationsLimit,
        quotes: subscriptionInfo.quoteLimit,
        invoices: subscriptionInfo.invoiceLimit,
        // Inclure d'autres limites au besoin
        history_logs: subscriptionInfo.historyLogsLimit,
        transactions: subscriptionInfo.isPremium
          ? subscriptions.premium.transactions.included
          : subscriptions.gratuit.transactions.included,
        missionResponses: subscriptionInfo.isPremium
          ? subscriptions.premium.missionResponses.included
          : subscriptions.gratuit.missionResponses.included,
        favoriteLimit: subscriptionInfo.favoriteLimit,
        businessCards: subscriptionInfo.businessCardsLimit,
        flyers: subscriptionInfo.flyersLimit
      }
    };

    // Mettre en cache pour 30 minutes
    await redis.set(cacheKey, JSON.stringify(subscriptionData), 'EX', 1800);

    res.json(subscriptionData);
    return;

  } catch (error) {
    logger.error('Erreur lors de la vérification du statut d\'abonnement:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur lors de la vérification du statut d\'abonnement',
      toastType: 'error'
    });
    return;
  }
};

// Route pour récupérer l'état de l'abonnement d'un utilisateur (utilisée par /api/subscription)
router.get('/status', getUserSubscription);

// Créer ou mettre à jour un client Stripe pour l'utilisateur
async function createOrUpdateStripeCustomer(userId: string, email: string, name: string) {
  try {
    // Rechercher si l'utilisateur a déjà un abonnement actif avec stripe_customer_id
    const { data: userAbo, error: aboError } = await supabase
      .from('user_abo')
      .select('stripe_customer_id')
      .eq('user_id', userId)
      .eq('statut', 'actif')
      .maybeSingle();

    if (aboError) throw aboError;

    let customerId = userAbo?.stripe_customer_id;

    if (customerId) {
      // Si l'utilisateur existe déjà dans Stripe, on met à jour ses informations
      await stripe.customers.update(customerId, {
        email,
        name
      });
      logger.info(`Mise à jour du client Stripe: ${customerId}`);
    }
    else {
      // Sinon, on crée un nouveau client Stripe
      const customer = await stripe.customers.create({
        email,
        name,
        metadata: {
          userId
        }
      });
      customerId = customer.id;

      // Vérifier si un abonnement existe déjà
      const { data: existingAbo, error: existingAboError } = await supabase
        .from('user_abo')
        .select('id')
        .eq('user_id', userId)
        .eq('statut', 'actif')
        .maybeSingle();

      if (existingAboError && existingAboError.code !== 'PGRST116') throw existingAboError;

      if (existingAbo) {
        // Mettre à jour l'abonnement existant
        await supabase
          .from('user_abo')
          .update({ stripe_customer_id: customerId })
          .eq('id', existingAbo.id);
      } else {
        // Créer un nouvel abonnement gratuit par défaut
        const now = new Date();
        const endDate = new Date();
        endDate.setMonth(endDate.getMonth() + 1);

        await supabase
          .from('user_abo')
          .insert({
            user_id: userId,
            type_abonnement: 'gratuit',
            date_debut: now.toISOString(),
            date_fin: endDate.toISOString(),
            statut: 'actif',
            stripe_customer_id: customerId,
            renouvellement_auto: true
          });
      }

      logger.info(`Nouveau client Stripe créé: ${customerId}`);
    }

    return customerId;
  } catch (error) {
    logger.error('Erreur lors de la création/mise à jour du client Stripe:', error);
    throw error;
  }
}

// Route pour créer une session Stripe Customer Portal
router.post('/create-portal-session', async (req: Request, res: Response): Promise<void> => {
  try {
    const userId = req.user?.userId;
    if (!userId) {
      logger.warn('Tentative d\'accès au portail client sans authentification');
      res.status(401).json({
        success: false,
        message: 'Utilisateur non authentifié',
        toastType: 'error'
      });
      return;
    }

    // Récupérer l'email de l'utilisateur
    const { data: user, error: userError } = await supabase
      .from('users')
      .select('email')
      .eq('id', userId)
      .single();

    if (userError || !user) {
      logger.error(`Utilisateur non trouvé lors de l'accès au portail client: ${userId}`, { error: userError });
      res.status(404).json({
        success: false,
        message: 'Utilisateur non trouvé',
        toastType: 'error'
      });
      return;
    }

    // Déchiffrer les données utilisateur
    const decryptedUser = await decryptUserDataAsync(user);

    // Récupérer les informations de profil
    const { data: userProfil, error: profilError } = await supabase
      .from('user_profil')
      .select('nom, prenom')
      .eq('user_id', userId)
      .single();

    if (profilError) {
      logger.error(`Erreur lors de la récupération du profil pour le portail client: ${userId}`, { error: profilError });
      res.status(500).json({
        success: false,
        message: 'Erreur lors de la récupération du profil',
        toastType: 'error'
      });
      return;
    }

    // Déchiffrer les données sensibles du profil
    const decryptedUserProfil = userProfil ? await decryptProfilDataAsync(userProfil) : null;

    // Si le profil n'existe pas ou n'a pas de nom/prénom, utiliser des valeurs par défaut
    const nom = decryptedUserProfil?.nom || 'Utilisateur';
    const prenom = decryptedUserProfil?.prenom || '';
    const name = `${prenom} ${nom}`.trim();

    try {
      // Créer ou mettre à jour le client Stripe
      const stripeCustomerId = await createOrUpdateStripeCustomer(userId, decryptedUser.email, name);
      logger.info(`Création portail client Stripe pour: ${userId} (${name}, ${decryptedUser.email}), customerID: ${stripeCustomerId}`);

      // Créer une session pour le Customer Portal
      const session = await stripe.billingPortal.sessions.create({
        customer: stripeCustomerId,
        return_url: PORTAL_RETURN_URL
      });

      res.json({
        success: true,
        url: session.url
      });
      return;
    } catch (stripeError: any) {
      // Log détaillé de l'erreur Stripe
      logger.error(`Erreur Stripe lors de la création du portail client: ${userId}`, {
        error: stripeError.message,
        code: stripeError.code,
        type: stripeError.type,
        params: stripeError.param,
        userId,
        email: decryptedUser.email
      });

      res.status(500).json({
        success: false,
        message: `Erreur lors de la création de la session Stripe: ${stripeError.message || 'Erreur inconnue'}`,
        toastType: 'error'
      });
      return;
    }
  } catch (error: any) {
    logger.error('Erreur générale lors de la création de la session Stripe:', {
      error: error.message || error,
      stack: error.stack
    });

    res.status(500).json({
      success: false,
      message: 'Erreur lors de la création de la session Stripe',
      toastType: 'error'
    });
    return;
  }
});

// Route pour créer un abonnement
router.post('/create-subscription', async (req: Request, res: Response): Promise<void> => {
  try {
    const userId = req.user?.userId;
    if (!userId) {
      res.status(401).json({
        success: false,
        message: 'Utilisateur non authentifié',
        toastType: 'error'
      });
      return;
    }

    const { plan, options } = req.body;

    if (!plan || !['gratuit', 'premium'].includes(plan)) {
      res.status(400).json({
        success: false,
        message: 'Plan d\'abonnement non valide',
        toastType: 'error'
      });
      return;
    }

    // Vérifiez si c'est un passage au plan gratuit avec autoRenew: false
    if (plan === 'gratuit' && options && options.autoRenew === false) {
      // Rechercher l'abonnement actuel de l'utilisateur
      const { data: existingAbo, error: aboError } = await supabase
        .from('user_abo')
        .select('*')
        .eq('user_id', userId)
        .eq('statut', 'actif')
        .maybeSingle();

      if (aboError) {
        logger.error('Erreur lors de la vérification de l\'abonnement existant:', aboError);
        res.status(500).json({
          success: false,
          message: 'Erreur lors de la mise à jour de l\'abonnement',
          toastType: 'error'
        });
        return;
      }

      if (existingAbo) {
        // Si l'abonnement est premium, on désactive juste le renouvellement automatique, le cron serveur s'occupera de l'application du downgrade
        if (existingAbo.type_abonnement === 'premium') {
          await supabase
            .from('user_abo')
            .update({
              renouvellement_auto: false,
              raison_annulation_abonnement: options.raison_annulation_abonnement || null
            })
            .eq('id', existingAbo.id);
          // AJOUT : annuler l'abonnement Stripe à la fin de la période
          if (existingAbo.stripe_subscription_id) {
            await stripe.subscriptions.update(existingAbo.stripe_subscription_id, {
              cancel_at_period_end: true
            });
          }

          // Récupérer les informations de l'utilisateur pour l'email
          const { data: user } = await supabase
            .from('users')
            .select('email')
            .eq('id', userId)
            .single();

          // Déchiffrer les données utilisateur
          const decryptedUser = await decryptUserDataAsync(user);

          // Récupérer le profil utilisateur avec toutes les informations nécessaires
          const { data: userProfil } = await supabase
            .from('user_profil')
            .select('nom, prenom, intervention_zone, phone_visible')
            .eq('user_id', userId)
            .single();

          // Déchiffrer les données sensibles du profil
          const decryptedUserProfil = userProfil ? await decryptProfilDataAsync(userProfil) : null;

          const nom = decryptedUserProfil?.nom || 'Utilisateur';
          const prenom = decryptedUserProfil?.prenom || '';
          const fullName = `${prenom} ${nom}`.trim();
          const frontendUrl = process.env.FRONTEND_URL || 'http://localhost:5173';

          // Vérifier les fonctionnalités qui dépasseront les limites du plan gratuit
          const { default: subscriptions } = await import('../config/ConfigSubscriptions');

          // Récupérer les limites du plan gratuit
          const serviceLimit = subscriptions.gratuit.services.included;
          const galleryLimit = subscriptions.gratuit.galleries.included;
          const favoriteLimit = subscriptions.gratuit.favoriteLimit.included;
          const conversationLimit = subscriptions.gratuit.conversations_messages_prives.included;
          const zoneLimit = subscriptions.gratuit.interventionAreas.included;

          // Récupérer les services actifs de l'utilisateur
          const { data: userServices } = await supabase
            .from('user_services')
            .select('id, titre')
            .eq('user_id', userId)
            .eq('statut', 'actif');

          // Récupérer les galeries actives de l'utilisateur
          const { data: userGalleries } = await supabase
            .from('user_gallery')
            .select('id, name, status')
            .eq('user_id', userId)
            .eq('status', 'actif');

          // Récupérer les favoris de l'utilisateur
          const { data: userFavorites } = await supabase
            .from('user_favorites')
            .select('id')
            .eq('user_id', userId);

          // Récupérer le nombre de conversations de l'utilisateur
          const { count: conversationCount } = await supabase
            .from('user_conversations')
            .select('id', { count: 'exact' })
            .eq('user_id', userId);

          // Calculer les excès pour chaque fonctionnalité
          const excessServicesCount = userServices && userServices.length > serviceLimit
            ? userServices.length - serviceLimit
            : 0;

          const excessGalleriesCount = userGalleries && userGalleries.length > galleryLimit
            ? userGalleries.length - galleryLimit
            : 0;

          const excessFavoritesCount = userFavorites && userFavorites.length > favoriteLimit
            ? userFavorites.length - favoriteLimit
            : 0;

          const excessConversationsCount = conversationCount && conversationCount > conversationLimit
            ? conversationCount - conversationLimit
            : 0;

          const hasExcessZone = decryptedUserProfil?.intervention_zone?.radius > zoneLimit ||
                               (decryptedUserProfil?.intervention_zone?.france_entiere && !subscriptions.gratuit.interventionAreas.franceEntiere.enabled);

          const hasPhoneVisible = decryptedUserProfil?.phone_visible && !subscriptions.gratuit.phoneVisibility.enabled;

          // Construire la liste des avertissements pour la notification
          const warnings = [];

          if (excessServicesCount > 0) {
            warnings.push(`${excessServicesCount} service(s) seront désactivés`);
          }

          if (excessGalleriesCount > 0) {
            warnings.push(`${excessGalleriesCount} galerie(s) seront désactivées`);
          }

          if (excessFavoritesCount > 0) {
            warnings.push(`${excessFavoritesCount} favori(s) seront supprimés`);
          }

          if (excessConversationsCount > 0) {
            warnings.push(`${excessConversationsCount} conversation(s) seront limitées`);
          }

          if (hasExcessZone) {
            warnings.push(`votre zone d'intervention sera réduite à ${zoneLimit} km`);
          }

          if (hasPhoneVisible) {
            warnings.push(`la visibilité de votre numéro de téléphone sera désactivée`);
          }

          // Construire le message d'avertissement pour la notification
          const notificationWarning = warnings.length > 0
            ? `Attention : à la fin de votre abonnement Premium, ${warnings.join(', ')}.`
            : '';

          // Préparer les paramètres pour l'email
          const emailParams: any = {
            userName: fullName,
            oldPlan: 'premium',
            newPlan: 'gratuit',
            changeType: 'cancel_auto_renew',
            endDate: existingAbo.date_fin,
            autoRenew: false,
            oldOptions: existingAbo.options || {},
            newOptions: { autoRenew: false },
            frontendUrl,
            // Ajouter les informations sur les services actuels
            serviceCount: userServices?.length || 0,
            galleryCount: userGalleries?.length || 0,
            favoriteCount: userFavorites?.length || 0,
            conversationCount: conversationCount || 0,
            interventionZone: decryptedUserProfil?.intervention_zone?.radius || zoneLimit,
            // Ajouter les galeries qui seront désactivées
            disabledGalleries: excessGalleriesCount > 0 ? userGalleries?.slice(galleryLimit).map(g => ({ id: g.id, titre: g.name })) : []
          };

          // Envoyer une notification par email
          if (user?.email) {
            await sendSubscriptionStatusChangeEmail(decryptedUser.email, {
              userName: fullName,
              newPlan: 'gratuit',
              oldPlan: 'premium',
              changeType: 'modification',
              html: generateEnhancedSubscriptionEmail(emailParams)
            });
          }

          // Créer une notification dans le dashboard
          let notificationContent = 'Votre abonnement Premium restera actif jusqu\'à la fin de votre période d\'abonnement, puis passera automatiquement au plan Gratuit.';
          if (notificationWarning) {
            notificationContent += ` ${notificationWarning}`;
          }

          await supabase
            .from('user_notifications')
            .insert({
              user_id: userId,
              title: 'Changement d\'abonnement planifié',
              content: notificationContent,
              type: 'system',
              is_read: false,
              is_archived: false,
              link: '/dashboard/premium'
            });

          // Invalider le cache
          await redis.del(`subscription_status:${userId}`);

          // Message de réponse avec avertissement si nécessaire
          let responseMessage = 'Votre abonnement Premium restera actif jusqu\'à la fin de votre période d\'abonnement, puis passera automatiquement au plan Gratuit.';
          if (excessServicesCount > 0) {
            responseMessage += ` Attention : ${excessServicesCount} service(s) seront désactivés lors du passage au plan gratuit.`;
          }

          res.json({
            success: true,
            message: responseMessage,
            toastType: 'success'
          });
          return;
        } else {
          // Si l'abonnement est déjà gratuit, mettre simplement à jour les options
          res.json({
            success: true,
            message: 'Votre abonnement Gratuit est déjà actif, les options ont été mises à jour avec succès.',
            toastType: 'success'
          });
          return;
        }
      }
    }

    // Récupérer l'email de l'utilisateur
    const { data: user, error: userError } = await supabase
      .from('users')
      .select('email')
      .eq('id', userId)
      .single();

    if (userError || !user) {
      res.status(404).json({
        success: false,
        message: 'Utilisateur non trouvé',
        toastType: 'error'
      });
      return;
    }

    // Déchiffrer les données utilisateur
    const decryptedUser = await decryptUserDataAsync(user);

    // Récupérer les informations de profil
    const { data: userProfil, error: profilError } = await supabase
      .from('user_profil')
      .select('nom, prenom')
      .eq('user_id', userId)
      .single();

    if (profilError) {
      logger.error('Erreur lors de la récupération du profil:', profilError);
      res.status(500).json({
        success: false,
        message: 'Erreur lors de la récupération du profil',
        toastType: 'error'
      });
      return;
    }

    // Déchiffrer les données sensibles du profil
    const decryptedUserProfil = userProfil ? await decryptProfilDataAsync(userProfil) : null;

    // Si le profil n'existe pas ou n'a pas de nom/prénom, utiliser des valeurs par défaut
    const nom = decryptedUserProfil?.nom || 'Utilisateur';
    const prenom = decryptedUserProfil?.prenom || '';
    const name = `${prenom} ${nom}`.trim();

    // Récupérer l'abonnement Stripe existant (si présent)
    const { data: existingAbo } = await supabase
      .from('user_abo')
      .select('*')
      .eq('user_id', userId)
      .eq('statut', 'actif')
      .maybeSingle();

    // Créer ou mettre à jour le client Stripe
    const stripeCustomerId = await createOrUpdateStripeCustomer(userId, decryptedUser.email, name);

    // Calculer le prix de l'abonnement en fonction des options
    let basePrice = plan === 'premium' ? subscriptions.premium.prixDeBase : subscriptions.gratuit.prixDeBase;
    let additionalOptions = [];

    // Ajouter le prix de base aux options
    if (plan === 'premium') {
      additionalOptions.push(`Abonnement de base Premium (${basePrice}€)`);
    }

    if (options && plan === 'premium') {
      // Traiter les options supplémentaires
      if (options.interventionAreas && options.interventionAreas > subscriptions.premium.interventionAreas.included) {
        const additionalZones = options.interventionAreas - subscriptions.premium.interventionAreas.included;
        const additionalCost = Math.ceil(additionalZones / 10) * subscriptions.premium.interventionAreas.additionalCost;
        basePrice += additionalCost;
        additionalOptions.push(`Zones supplémentaires: ${additionalZones} km (+${additionalCost}€)`);
      }

      // Option France entière
      if (options.franceEntiere) {
        // Récupérer le prix de l'option France entière depuis la configuration
        const franceEntierePrice = subscriptions.premium.interventionAreas.franceEntiere.additionalCost || 500;
        basePrice += franceEntierePrice;
        additionalOptions.push(`France entière (+${franceEntierePrice}€)`);
      }

      // Services supplémentaires
      if (options.services && options.services > subscriptions.premium.services.included) {
        const additionalServices = options.services - subscriptions.premium.services.included;
        const additionalCost = additionalServices * subscriptions.premium.services.additionalCost;
        basePrice += additionalCost;
        additionalOptions.push(`Services supplémentaires: ${additionalServices} (+${additionalCost}€)`);
      }

      // Galeries supplémentaires
      if (options.galleries && options.galleries > subscriptions.premium.galleries.included) {
        const additionalGalleries = options.galleries - subscriptions.premium.galleries.included;
        const additionalCost = additionalGalleries * subscriptions.premium.galleries.additionalCost;
        basePrice += additionalCost;
        additionalOptions.push(`Galeries supplémentaires: ${additionalGalleries} (+${additionalCost}€)`);
      }

      // Conversations privées supplémentaires
      if (options.conversations_messages_prives && options.conversations_messages_prives > subscriptions.premium.conversations_messages_prives.included) {
        const additionalConversations = options.conversations_messages_prives - subscriptions.premium.conversations_messages_prives.included;
        const additionalCost = additionalConversations * subscriptions.premium.conversations_messages_prives.additionalCost;
        basePrice += additionalCost;
        additionalOptions.push(`Conversations supplémentaires: ${additionalConversations} (+${additionalCost}€)`);
      }

      // Services sélectionnés
      if (options.selectedServices && Array.isArray(options.selectedServices)) {
        const includedServices = subscriptions.premium.services.included || 5;
        const totalSelectedServices = options.selectedServices.reduce((count: number, service: any) => count + (service.subcategoryIds?.length || 0), 0);
        if (totalSelectedServices > includedServices) {
          const additionalServiceCount = totalSelectedServices - includedServices;
          const additionalCost = additionalServiceCount * subscriptions.premium.services.additionalCost;
          basePrice += additionalCost;
          additionalOptions.push(`Services supplémentaires: ${additionalServiceCount} (+${additionalCost}€)`);
        }
      }
    }

    // Vérifier si un code promo a été appliqué
    let finalPrice = basePrice;
    let promoCodeInfo = null;

    if (options.discountedPrice !== undefined && options.discountDetails) {
      // Utiliser le prix réduit fourni
      finalPrice = options.discountedPrice;
      promoCodeInfo = options.discountDetails;
      additionalOptions.push(`Code promo ${promoCodeInfo.code} appliqué (${promoCodeInfo.type === 'percentage' ? promoCodeInfo.value + '%' : promoCodeInfo.value + '€'} de réduction)`);
    }

    // --- AJOUT : MODIFICATION D'UN ABONNEMENT EXISTANT ---
    if (existingAbo && existingAbo.stripe_subscription_id) {
      // Créer un prix Stripe dynamique pour ce montant
      const price = await stripe.prices.create({
        unit_amount: Math.round(finalPrice * 100),
        currency: 'eur',
        recurring: { interval: 'month' },
        product: process.env.STRIPE_PRODUCT_ID, // doit être défini dans .env
        metadata: {
          userId,
          plan,
          options: JSON.stringify({ ...options, promoCodeApplied: promoCodeInfo ? promoCodeInfo.code : undefined }),
          modification: 'true'
        }
      });
      // Mettre à jour l'abonnement Stripe
      await stripe.subscriptions.update(existingAbo.stripe_subscription_id, {
        items: [{ id: (await stripe.subscriptions.retrieve(existingAbo.stripe_subscription_id)).items.data[0].id, price: price.id }],
        metadata: {
          userId,
          plan,
          options: JSON.stringify({ ...options, promoCodeApplied: promoCodeInfo ? promoCodeInfo.code : undefined }),
          modification: 'true'
        },
        proration_behavior: 'create_prorations'
      });
      // Mettre à jour la base de données
      await supabase
        .from('user_abo')
        .update({
          montant: finalPrice,
          options: { ...options, promoCodeApplied: promoCodeInfo ? promoCodeInfo.code : undefined }
        })
        .eq('id', existingAbo.id);
      // Invalider les caches
      await redis.del(`subscription_status:${userId}`);
      await redis.del(`subscription_limits:${userId}`);

      // --- ENVOI EMAIL ET NOTIFICATION ---
      const frontendUrl = process.env.FRONTEND_URL || 'http://localhost:5173';

      if (user?.email) {
        await sendSubscriptionStatusChangeEmail(decryptedUser.email, {
          userName: name,
          newPlan: plan,
          oldPlan: existingAbo?.type_abonnement || plan,
          changeType: 'modification',
          price: finalPrice,
          promoCodeApplied: promoCodeInfo?.code,
          discountAmount: promoCodeInfo?.value,
          discountType: promoCodeInfo?.type,
          oldOptions: existingAbo?.options || {},
          newOptions: { ...options, promoCodeApplied: promoCodeInfo ? promoCodeInfo.code : undefined },
          html: generateEnhancedSubscriptionEmail({
            userName: name,
            oldPlan: existingAbo?.type_abonnement || plan,
            newPlan: plan,
            changeType: 'modification',
            autoRenew: true,
            price: finalPrice,
            oldOptions: existingAbo?.options || {},
            newOptions: { ...options, promoCodeApplied: promoCodeInfo ? promoCodeInfo.code : undefined },
            promoCodeApplied: promoCodeInfo?.code,
            discountAmount: promoCodeInfo?.value,
            discountType: promoCodeInfo?.type,
            frontendUrl
        })
        });
      }
      await supabase
        .from('user_notifications')
        .insert({
          user_id: userId,
          title: 'Abonnement modifié',
          content: 'Votre abonnement a bien été modifié. Les nouvelles options et tarifs sont désormais actifs.',
          type: 'system',
          is_read: false,
          is_archived: false,
          link: '/dashboard/premium'
        });
      // --- FIN ENVOI EMAIL ET NOTIFICATION ---

      res.json({
        success: true,
        message: 'Votre abonnement a été modifié avec succès. Les changements seront effectifs immédiatement.',
        toastType: 'success'
      });
      return;
    }
    // --- FIN AJOUT ---

    // Sinon, logique actuelle de création d'abonnement
    // Créer un checkout pour l'abonnement
    const session = await stripe.checkout.sessions.create({
      customer: stripeCustomerId,
      payment_method_types: ['card'],
      mode: 'subscription',
      line_items: [
        {
          price_data: {
            currency: 'eur',
            product_data: {
              name: plan === 'premium' ? 'Abonnement Premium' : 'Abonnement Gratuit',
              description: additionalOptions.length > 0
                ? `Abonnement avec options personnalisées: ${additionalOptions.join(', ')}`
                : 'Abonnement standard'
            },
            unit_amount: Math.round(finalPrice * 100), // Montant en centimes, arrondi pour éviter les problèmes de decimal
            recurring: {
              interval: 'month'
            }
          },
          quantity: 1
        }
      ],
      success_url: `${PORTAL_RETURN_URL}?success=true`,
      cancel_url: `${PORTAL_RETURN_URL}?canceled=true`,
      subscription_data: {
        metadata: {
          userId,
          plan,
          options: JSON.stringify({
            ...options,
            promoCodeApplied: promoCodeInfo ? promoCodeInfo.code : undefined
          })
        }
      }
    });

    res.json({
      success: true,
      url: session.url
    });
    return;
  } catch (error) {
    logger.error('Erreur lors de la création de l\'abonnement:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur lors de la création de l\'abonnement',
      toastType: 'error'
    });
    return;
  }
});

// Les fonctions de gestion des webhooks Stripe sont exportées et utilisées dans server.ts
// La route webhook est désormais définie dans server.ts uniquement

// Gérer les changements d'abonnement/souscription/renouvellement renvoyé par Stripe
export async function handleSubscriptionChange(subscription: any) {
  try {
    // Log détaillé des métadonnées de l'abonnement pour le débogage
    logger.info(`Webhook Stripe - handleSubscriptionChange - Métadonnées:`, {
      status: subscription.status,
      userId: subscription.metadata?.userId,
      plan: subscription.metadata?.plan,
      is_unbanned_reactivation: subscription.metadata?.is_unbanned_reactivation,
      reactivated_reason: subscription.metadata?.reactivated_reason,
      all_metadata: subscription.metadata
    });

    // AJOUT : Ne rien faire si l'abonnement Stripe n'est pas actif ou en période d'essai
    if (!['active', 'trialing'].includes(subscription.status)) {
      logger.info(`Abonnement Stripe ignoré (statut: ${subscription.status}) pour userId=${subscription.metadata?.userId}`);
      return;
    }
    const userId = subscription.metadata.userId;
    const plan = subscription.metadata.plan || 'gratuit';
    const options = subscription.metadata.options ? JSON.parse(subscription.metadata.options) : {};

    if (!userId) {
      logger.error('UserId manquant dans les métadonnées de l\'abonnement');
      return;
    }

    // Si c'est une simple modification d'options, ne pas envoyer d'email/notification de renouvellement
    const isModification = subscription.metadata && subscription.metadata.modification === 'true';
    if (isModification) {
      logger.info('Modification d\'abonnement détectée, pas d\'email de renouvellement envoyé.');
      // On continue pour mettre à jour la base, mais on sautera l'envoi d'email/notification plus bas
    }

    // Vérifier si c'est un upgrade vers premium ou une modification d'abonnement premium
    // qui augmente le nombre de galeries autorisées
    const { data: currentAbo } = await supabase
      .from('user_abo')
      .select('type_abonnement, options')
      .eq('user_id', userId)
      .eq('statut', 'actif')
      .maybeSingle();

    // Importer la configuration des abonnements
    const { default: subscriptionsConfig } = await import('../config/ConfigSubscriptions');

    // Déterminer si c'est un upgrade de gratuit vers premium
    const isUpgradeToPremium = currentAbo?.type_abonnement === 'gratuit' && plan === 'premium';

    // Déterminer si c'est une modification d'abonnement premium qui augmente le nombre de galeries
    let isGalleryLimitIncreased = false;
    if (plan === 'premium' && currentAbo?.type_abonnement === 'premium') {
      // Récupérer l'ancienne limite de galeries
      const oldGalleryLimit = currentAbo?.options?.galleries || subscriptionsConfig.premium.galleries.included;
      // Récupérer la nouvelle limite de galeries
      const newGalleryLimit = options.galleries || subscriptionsConfig.premium.galleries.included;
      // Vérifier si la limite a augmenté
      isGalleryLimitIncreased = newGalleryLimit > oldGalleryLimit;

      logger.info(`Vérification de l'augmentation de la limite de galeries:`, {
        oldGalleryLimit,
        newGalleryLimit,
        isGalleryLimitIncreased
      });
    }

    // Mettre à jour l'abonnement dans la base de données
    const now = new Date();
    const endDate = new Date();
    endDate.setMonth(endDate.getMonth() + 1);

    // Vérifier si l'utilisateur a déjà un abonnement actif
    const { data: existingAbo, error: aboError } = await supabase
      .from('user_abo')
      .select('*')
      .eq('user_id', userId)
      .eq('statut', 'actif')
      .maybeSingle();

    if (aboError) {
      logger.error('Erreur lors de la vérification de l\'abonnement existant:', aboError);
      return;
    }

    // --- AJOUT : Éviter l'attribution multiple de crédits IA/Jobi lors de la réactivation du renouvellement automatique ---
    let alreadyRenewed = false;
    if (existingAbo && subscription.current_period_start) {
      try {
        const currentPeriodStart = new Date(subscription.current_period_start * 1000).toISOString();
        let existingDateDebut = null;

        try {
          if (existingAbo.date_debut) {
            existingDateDebut = new Date(existingAbo.date_debut).toISOString();
          }
        } catch (error) {
          logger.error(`Erreur lors de la conversion de date_debut (${existingAbo.date_debut}) en date:`, error);
        }

        if (existingDateDebut && existingDateDebut === currentPeriodStart) {
          alreadyRenewed = true;
          logger.info(`Aucune attribution de crédits IA/Jobi : le renouvellement a déjà été traité pour la période ${currentPeriodStart} (userId=${userId})`);
        }
      } catch (error) {
        logger.error(`Erreur lors de la vérification alreadyRenewed (current_period_start=${subscription.current_period_start}):`, error);
      }
    }

    // Déterminer le type de changement
    let changeType: 'upgrade' | 'downgrade' | 'renewal' | 'unbanned_reactivation' | 'modification'; // Ajout du type pour les réactivations après débannissement et les modifications
    let oldPlan = '';

    // Vérifier si c'est une réactivation après un débannissement
    const isUnbannedReactivation =
      (subscription.metadata && subscription.metadata.is_unbanned_reactivation === 'true') ||
      (subscription.metadata && subscription.metadata.reactivated_reason === 'user_unbanned');

    // Log pour le débogage
    logger.info(`Détection du type de changement d'abonnement initial - userId=${userId}:`, {
      existingAboType: existingAbo ? existingAbo.type_abonnement : null,
      newPlan: plan,
      isUnbannedReactivation,
      metadata: subscription.metadata
    });

    if (existingAbo) {
      oldPlan = existingAbo.type_abonnement;
      const planFromStripeMetadata = subscription.metadata.plan || 'gratuit';

      if (isUnbannedReactivation) {
        // Si c'est une réactivation après un débannissement, on utilise un type spécial
        changeType = 'unbanned_reactivation';
        logger.info(`Détecté comme une réactivation après débannissement pour l'utilisateur ${userId}`);
      } else if (existingAbo.type_abonnement === 'gratuit' && planFromStripeMetadata === 'premium') {
        changeType = 'upgrade';
      } else if (existingAbo.type_abonnement === 'premium' && planFromStripeMetadata === 'gratuit') {
        changeType = 'downgrade';
      } else if (existingAbo.type_abonnement === planFromStripeMetadata) { // Plan type hasn't changed (e.g. premium -> premium)
        // Vérifier que current_period_start est une valeur valide avant de la convertir
        let currentPeriodStartStripe = null;
        try {
          if (subscription.current_period_start) {
            currentPeriodStartStripe = new Date(subscription.current_period_start * 1000).toISOString();
          }
        } catch (error) {
          logger.error(`Erreur lors de la conversion de current_period_start (${subscription.current_period_start}) en date:`, error);
        }

        let dbDateDebut = null;
        try {
          if (existingAbo.date_debut) {
            dbDateDebut = new Date(existingAbo.date_debut).toISOString();
          }
        } catch (error) {
          logger.error(`Erreur lors de la conversion de date_debut (${existingAbo.date_debut}) en date:`, error);
        }

        if (dbDateDebut && currentPeriodStartStripe && currentPeriodStartStripe > dbDateDebut) {
          changeType = 'renewal'; // Actual new billing period has started
        } else {
          // Plan type is same, period has not advanced.
          // This is a modification (e.g. options changed via our backend, or Stripe updated terms like cancel_at_period_end).
          // Not a renewal for credit purposes.
          changeType = 'modification';
          logger.info(`handleSubscriptionChange: Plan type ${planFromStripeMetadata} unchanged, period not advanced. ChangeType='modification'. User: ${userId}`);
        }
      } else {
        // Plan types are different, but not a direct G->P or P->G. E.g. if we introduce 'pro'.
        // For safety, treat as modification regarding credits.
        changeType = 'modification';
        logger.warn(`handleSubscriptionChange: Unclear transition. DB Plan: ${existingAbo.type_abonnement}, Stripe Meta Plan: ${planFromStripeMetadata}. ChangeType='modification'. User: ${userId}`);
      }

      // Synchroniser le renouvellement_auto avec Stripe
      const cancelAtPeriodEnd = subscription.cancel_at_period_end === true;

      // Récupérer la date de fin de période de manière sécurisée
      let currentPeriodEnd = endDate.toISOString();
      try {
        if (subscription.current_period_end) {
          currentPeriodEnd = new Date(subscription.current_period_end * 1000).toISOString();
        }
      } catch (error) {
        logger.error(`Erreur lors de la conversion de current_period_end (${subscription.current_period_end}) en date:`, error);
      }

      // Récupérer la date de début de période de manière sécurisée
      let currentPeriodStart = now.toISOString();
      try {
        if (subscription.current_period_start) {
          currentPeriodStart = new Date(subscription.current_period_start * 1000).toISOString();
        }
      } catch (error) {
        logger.error(`Erreur lors de la conversion de current_period_start (${subscription.current_period_start}) en date:`, error);
      }

      const wasAutoRenew = existingAbo ? existingAbo.renouvellement_auto : true;

      // Vérifier si c'est un renouvellement en comparant les dates
      // Log pour le débogage
      logger.info(`Détection du type de changement d'abonnement - userId=${userId}:`, {
        currentPeriodStart,
        existingAboDateDebut: existingAbo ? existingAbo.date_debut : null,
        isUnbannedReactivation,
        metadata: subscription.metadata
      });

      // Si c'est une réactivation après un débannissement, on le traite comme un type spécial
      let isRenewal = isUnbannedReactivation; // Par défaut, c'est un renouvellement si c'est une réactivation après débannissement

      // Vérifier si c'est un renouvellement en comparant les dates de manière sécurisée
      if (existingAbo && existingAbo.date_debut) {
        try {
          const existingDateDebut = new Date(existingAbo.date_debut);
          const currentPeriodStartDate = new Date(currentPeriodStart);

          if (currentPeriodStartDate > existingDateDebut) {
            isRenewal = true;
          }
        } catch (error) {
          logger.error(`Erreur lors de la comparaison des dates pour isRenewal:`, error);
        }
      }

      await supabase
        .from('user_abo')
        .update({
          type_abonnement: plan,
          montant: subscription.plan?.amount ? subscription.plan.amount / 100 : 0,
          // Mettre à jour la date de début uniquement si c'est un renouvellement
          ...(isRenewal && { date_debut: currentPeriodStart }),
          date_fin: currentPeriodEnd,
          renouvellement_auto: !cancelAtPeriodEnd,
          options: options,
          stripe_subscription_id: subscription.id
        })
        .eq('id', existingAbo.id);

      // Si le renouvellement auto vient d'être désactivé, envoyer un email spécifique
      if (wasAutoRenew && cancelAtPeriodEnd) {
        // Récupérer les infos utilisateur
        const { data: user } = await supabase
          .from('users')
          .select('email')
          .eq('id', userId)
          .single();
        if (user && user.email) {
          // Déchiffrer les données utilisateur
          const decryptedUser = await decryptUserDataAsync(user);

          const { data: userProfil } = await supabase
            .from('user_profil')
            .select('nom, prenom')
            .eq('user_id', userId)
            .single();

          // Déchiffrer les données sensibles du profil
          const decryptedUserProfil = userProfil ? await decryptProfilDataAsync(userProfil) : null;

          const nom = decryptedUserProfil?.nom || 'Utilisateur';
          const prenom = decryptedUserProfil?.prenom || '';
          const fullName = `${prenom} ${nom}`.trim();
          const frontendUrl = process.env.FRONTEND_URL || 'http://localhost:5173';
          // Email spécifique
          const emailHtml = `<!DOCTYPE html>
            <html>
            <head>
              <meta charset=\"utf-8\">
              <style>
                body { font-family: Arial, sans-serif; color: #333; line-height: 1.6; margin: 0; padding: 0; }
                .container { max-width: 600px; margin: 0 auto; padding: 20px; border: 1px solid #e5e7eb; border-radius: 8px; }
                .header { background-color: #FF7A35; color: white; padding: 15px 20px; border-radius: 8px 8px 0 0; margin: -20px -20px 20px; }
                .footer { background-color: #f9fafb; padding: 15px 20px; border-top: 1px solid #e5e7eb; margin: 20px -20px -20px; border-radius: 0 0 8px 8px; font-size: 12px; color: #6b7280; }
                h1 { margin: 0; font-size: 24px; }
                .message-box { background-color: #fff8f3; border-left: 4px solid #FF7A35; padding: 15px; margin: 20px 0; }
                .button { display: inline-block; background-color: #FF7A35; color: white; padding: 12px 24px; text-decoration: none; border-radius: 4px; font-weight: bold; margin-top: 20px; }
                @media (max-width: 600px) { .container { width: 100%; padding: 10px; } .header { padding: 10px; margin: -10px -10px 15px; } .footer { margin: 15px -10px -10px; padding: 10px; } }
              </style>
            </head>
            <body>
              <div class=\"container\">
                <div class=\"header\">
                  <h1>Renouvellement automatique désactivé</h1>
                </div>
                <p>Bonjour <b>${fullName}</b>,</p>
                <div class=\"message-box\">
                  <p>Vous avez demandé l'annulation du renouvellement automatique de votre abonnement <b>Premium</b>.</p>
                  <p>Votre abonnement Premium restera actif jusqu'à la date de fin de votre période actuelle (<b>${new Date(currentPeriodEnd).toLocaleDateString('fr-FR')}</b>), puis passera automatiquement au <b>plan Gratuit</b>.</p>
                  <p>Pendant cette période, vous continuez à bénéficier de tous les avantages Premium. Après la date de fin, certaines fonctionnalités avancées seront limitées.</p>
                </div>
                <p>Vous pouvez à tout moment réactiver un abonnement Premium pour débloquer toutes les fonctionnalités et booster votre activité !</p>
                <a href=\"${frontendUrl}/dashboard/premium\" class=\"button\">Découvrir les offres Premium</a>
                <div class=\"footer\">
                  Si vous avez des questions, notre équipe support est à votre écoute.<br>
                  Merci de votre confiance.<br>
                  <b>L'équipe support</b>
                </div>
              </div>
            </body>
            </html>`;
          await sendSubscriptionStatusChangeEmail(decryptedUser.email, {
            userName: fullName,
            newPlan: plan,
            oldPlan: oldPlan,
            startDate: now.toISOString(),
            endDate: currentPeriodEnd,
            changeType: 'cancel_auto_renew',
            html: emailHtml
        });
        } else {
          logger.warn('Impossible d\'envoyer l\'email de désactivation du renouvellement auto : utilisateur ou email manquant', { userId });
        }
        // Journaliser la désactivation du renouvellement automatique
        await logUserActivity(
          userId,
          'cancel_auto_renew',
          undefined,
          'subscription',
          { plan, oldPlan },
          undefined
        );
      }
    } else {
      // Créer un nouvel abonnement
      changeType = plan === 'premium' ? 'upgrade' : 'renewal';

      // Utiliser la date de début de période de Stripe si disponible
      const startDate = subscription.current_period_start
        ? new Date(subscription.current_period_start * 1000).toISOString()
        : now.toISOString();

      // Utiliser la date de fin de période de Stripe si disponible
      const periodEndDate = subscription.current_period_end
        ? new Date(subscription.current_period_end * 1000).toISOString()
        : endDate.toISOString();

      await supabase
        .from('user_abo')
        .insert({
          user_id: userId,
          type_abonnement: plan,
          statut: 'actif',
          date_debut: startDate,
          date_fin: periodEndDate,
          montant: subscription.plan?.amount ? subscription.plan.amount / 100 : 0,
          renouvellement_auto: true,
          options: options,
          stripe_subscription_id: subscription.id
        });
    }

    // Invalider les caches
    await redis.del(`subscription_status:${userId}`);
    await redis.del(`subscription_limits:${userId}`);
    logger.info(`Abonnement mis à jour pour l'utilisateur ${userId}`);

    // Envoyer une notification par email et dans le dashboard
    // Récupérer les informations de l'utilisateur
    const { data: user } = await supabase
      .from('users')
      .select('email')
      .eq('id', userId)
      .single();

    // Déchiffrer les données utilisateur
    const decryptedUser = user ? await decryptUserDataAsync(user) : null;

    const { data: userProfil } = await supabase
      .from('user_profil')
      .select('nom, prenom')
      .eq('user_id', userId)
      .single();

    // Déchiffrer les données sensibles du profil
    const decryptedUserProfil = userProfil ? await decryptProfilDataAsync(userProfil) : null;

    const nom = decryptedUserProfil?.nom || 'Utilisateur';
    const prenom = decryptedUserProfil?.prenom || '';
    const fullName = `${prenom} ${nom}`.trim();

    // Définir le contenu des notifications en fonction du type de changement
    let notificationTitle = '';
    let notificationContent = '';

    if (changeType === 'unbanned_reactivation') {
      notificationTitle = `Votre abonnement ${plan} a été réactivé`;
      notificationContent = `Suite à la réactivation de votre compte, votre abonnement ${plan} a été automatiquement réactivé jusqu'au ${endDate.toLocaleDateString('fr-FR')}. Notez que cette réactivation ne génère pas de bonus Jobi, contrairement aux renouvellements réguliers.`;
    } else if (changeType === 'upgrade') {
      notificationTitle = 'Bienvenue sur le plan Premium !';
      notificationContent = 'Votre compte a été mis à niveau vers le plan Premium. Profitez de tous les avantages dès maintenant !';
    } else if (changeType === 'downgrade') {
      notificationTitle = 'Passage au plan Gratuit';
      notificationContent = 'Votre abonnement est maintenant passé au plan Gratuit. Certaines fonctionnalités avancées sont désormais limitées.';
    } else if (changeType === 'renewal' && !(plan === 'premium' && subscription.cancel_at_period_end === true)) {
      // N'envoyer la notification de renouvellement que si le renouvellement auto n'est pas désactivé
      notificationTitle = `Abonnement ${plan} renouvelé`;
      notificationContent = `Votre abonnement au plan ${plan} a été renouvelé avec succès jusqu'au ${endDate.toLocaleDateString('fr-FR')}.${plan === 'premium' ? ' Bonus fidélité : 20 Jobi ont été ajoutés à votre compte !' : ''}`;
    } else {
      // Pas de notification spécifique pour les autres cas (ex: modification, ou renewal avec cancel_at_period_end)
      notificationTitle = '';
      notificationContent = '';
    }

    // Envoyer l'email
    // On n'envoie PAS l'email de renouvellement si cancel_at_period_end est true
    if (!isModification && user?.email && !(changeType === 'renewal' && subscription.cancel_at_period_end === true)) {
      // Pour les emails, on traite le type 'unbanned_reactivation' comme un type 'reactivate_after_ban'
      const emailChangeType = changeType === 'unbanned_reactivation' ? 'reactivate_after_ban' : changeType;

      // Récupérer le prix de l'abonnement
      const subscriptionPrice = subscription.plan?.amount ? subscription.plan.amount / 100 : 0;

      await sendSubscriptionStatusChangeEmail(decryptedUser.email, {
        userName: fullName,
        newPlan: plan,
        oldPlan: oldPlan,
        startDate: now.toISOString(),
        endDate: endDate.toISOString(),
        changeType: emailChangeType as any, // Utiliser 'as any' pour éviter les erreurs de type
        features: plan === 'premium' ? {
          advancedStats: true,
          unlimitedPhotos: true,
          prioritySupport: true,
          featuredListing: true
        } : {
          advancedStats: false,
          unlimitedPhotos: false,
          prioritySupport: false,
          featuredListing: false
        },
        price: subscriptionPrice,
        html: generateEnhancedSubscriptionEmail({
          userName: fullName,
          oldPlan: oldPlan,
          newPlan: plan,
          changeType: changeType, // Utiliser le type original pour que le cas 'unbanned_reactivation' soit traité correctement
          startDate: now.toISOString(),
          endDate: subscription.current_period_end ? new Date(subscription.current_period_end * 1000).toISOString() : endDate.toISOString(),
          autoRenew: !subscription.cancel_at_period_end,
          price: subscriptionPrice,
          oldOptions: existingAbo?.options || {},
          newOptions: options,
          frontendUrl: process.env.FRONTEND_URL || 'http://localhost:5173'
      })
      });

      logger.info(`Email de notification de ${changeType} d'abonnement envoyé à ${decryptedUser.email}`);
    }

    // Créer une notification dans le dashboard UNIQUEMENT si le titre et le contenu ne sont pas vides
    if (!isModification && notificationTitle && notificationContent) {
      await supabase
        .from('user_notifications')
        .insert({
          user_id: userId,
          title: notificationTitle,
          content: notificationContent,
          type: 'system',
          is_read: false,
          is_archived: false,
          link: '/dashboard/premium'
        });
    }

    // Journaliser l'action de passage à premium dans l'historique utilisateur
    if (changeType === 'upgrade' && plan === 'premium') {
      await logUserActivity(
        userId,
        'upgrade_premium',
        undefined,
        'subscription',
        { plan, oldPlan },
        undefined
      );

      // Si c'est un upgrade de gratuit vers premium ou une augmentation de la limite de galeries,
      // réactiver les galeries inactives
      if (isUpgradeToPremium || isGalleryLimitIncreased) {
        try {
          // Récupérer la limite de galeries du plan premium
          const { default: subscriptions } = await import('../config/ConfigSubscriptions');
          const galleryLimit = options.galleries || subscriptions.premium.galleries.included;

          // Fonction pour réactiver les galeries inactives
          const reactivateInactiveGalleries = async (userId: string, galleryLimit: number): Promise<number> => {
            try {
              // Récupérer les galeries inactives de l'utilisateur
              const { data: inactiveGalleries, error: galleriesError } = await supabase
                .from('user_gallery')
                .select('id, name')
                .eq('user_id', userId)
                .eq('status', 'inactif')
                .order('created_at', { ascending: false });

              if (galleriesError) {
                logger.error('Erreur lors de la récupération des galeries inactives:', galleriesError);
                return 0;
              }

              if (!inactiveGalleries || inactiveGalleries.length === 0) {
                logger.info(`Aucune galerie inactive à réactiver pour l'utilisateur ${userId}`);
                return 0;
              }

              // Récupérer le nombre de galeries actives
              const { data: activeGalleries, error: activeGalleriesError } = await supabase
                .from('user_gallery')
                .select('id')
                .eq('user_id', userId)
                .eq('status', 'actif')
                .order('created_at', { ascending: false });

              if (activeGalleriesError) {
                logger.error('Erreur lors de la récupération des galeries actives:', activeGalleriesError);
                return 0;
              }

              const activeCount = activeGalleries?.length || 0;
              const availableSlots = galleryLimit - activeCount;

              if (availableSlots <= 0) {
                logger.info(`Aucun emplacement disponible pour réactiver des galeries (limite: ${galleryLimit}, actives: ${activeCount})`);
                return 0;
              }

              // Déterminer combien de galeries peuvent être réactivées
              const galleriesToReactivate = inactiveGalleries.slice(0, availableSlots);

              // Réactiver les galeries
              for (const gallery of galleriesToReactivate) {
                await supabase
                  .from('user_gallery')
                  .update({ status: 'actif' })
                  .eq('id', gallery.id);

                logger.info(`Galerie réactivée pour l'utilisateur ${userId}: ${gallery.name} (${gallery.id})`);
              }

              // Invalider le cache des galeries
              await redis.del(`user_gallery:${userId}`);

              // Invalider également le cache spécifique à l'utilisateur
              const { data: userProfil } = await supabase
                .from('user_profil')
                .select('slug')
                .eq('user_id', userId)
                .single();

              if (userProfil?.slug) {
                await redis.del(`user_gallery_user_specific:${userProfil.slug}`);
              }

              return galleriesToReactivate.length;
            } catch (error) {
              logger.error('Erreur lors de la réactivation des galeries inactives:', error);
              return 0;
            }
          };

          // Réactiver les galeries inactives
          const reactivatedCount = await reactivateInactiveGalleries(userId, galleryLimit);

          if (reactivatedCount > 0) {
            logger.info(`${reactivatedCount} galerie(s) réactivée(s) pour l'utilisateur ${userId} ${
              isUpgradeToPremium
                ? 'suite à l\'upgrade vers Premium'
                : 'suite à l\'augmentation de la limite de galeries'
            }`);

            // Ajouter une notification pour informer l'utilisateur
            await supabase
              .from('user_notifications')
              .insert({
                user_id: userId,
                title: 'Galeries réactivées',
                content: isUpgradeToPremium
                  ? `Suite à votre passage au plan Premium, ${reactivatedCount} galerie(s) ont été réactivées automatiquement.`
                  : `Suite à l'augmentation de votre limite de galeries, ${reactivatedCount} galerie(s) ont été réactivées automatiquement.`,
                type: 'system',
                is_read: false,
                is_archived: false,
                link: '/dashboard/gallery'
              });
          }
        } catch (error) {
          logger.error('Erreur lors de la réactivation des galeries inactives:', error);
        }
      }
    }
    // Journaliser l'action de downgrade (passage à gratuit)
    if (changeType === 'downgrade' && plan === 'gratuit') {
      await logUserActivity(
        userId,
        'downgrade_gratuit',
        undefined,
        'subscription',
        { plan, oldPlan },
        undefined
      );
    }
    // Journaliser l'action de renouvellement d'abonnement
    if (changeType === 'renewal') {
      await logUserActivity(
        userId,
        'renewal_abonnement',
        undefined,
        'subscription',
        { plan, oldPlan },
        undefined
      );
    }
    // Journaliser l'action de réactivation d'abonnement après débannissement
    if (changeType === 'unbanned_reactivation') {
      await logUserActivity(
        userId,
        'reactivation_after_unban',
        undefined,
        'subscription',
        {
          plan,
          oldPlan,
          message: "Réactivation de l'abonnement suite à un débannissement (sans attribution de Jobi)"
        },
        undefined
      );
      logger.info(`Réactivation d'abonnement après débannissement journalisée pour l'utilisateur ${userId}`);
    }


    // Attribution des crédits IA et Jobi UNIQUEMENT lors d'un vrai renouvellement ou d'un upgrade
    if ((changeType === 'renewal' || changeType === 'upgrade') && !isModification && !alreadyRenewed) {
      // Crédits IA
      try {
        const { addFreeAiCredits } = require('../controllers/aiCreditsController');
        const { subscriptions: configSubscriptions } = require('../config/ConfigSubscriptions');
        if (plan === 'premium') {
        // Ajouter des crédits IA pour le plan premium selon la configuration
          const premiumCredits = configSubscriptions.premium.aiCredits.included;
          await addFreeAiCredits(userId, premiumCredits);
          logger.info(`${premiumCredits} crédits IA gratuits ajoutés pour l'utilisateur ${userId} (plan premium)`);
        } else if (plan === 'gratuit') {
        // Ajouter des crédits IA pour le plan gratuit selon la configuration
          const freeCredits = configSubscriptions.gratuit.aiCredits.included;
          await addFreeAiCredits(userId, freeCredits);
          logger.info(`${freeCredits} crédits IA gratuits ajoutés pour l'utilisateur ${userId} (plan gratuit)`);
        }
      } catch (error) {
        logger.error('Erreur lors de l\'ajout des crédits IA gratuits:', error);
      }
      // Jobi - Uniquement pour le plan premium
      if (plan === 'premium') {
        logger.info(`Vérification pour l'attribution de Jobi - userId=${userId}:`, {
          changeType,
          plan,
          isModification
        });
        try {
          const { dbService } = require('../services/db');
          await dbService.createJobiEntrySupplement(userId, {
            montant: 20,
            titre: 'Renouvellement Premium',
            description: '20 Jobi offerts pour votre fidélité au renouvellement de votre abonnement Premium'
          });
          // Créer une notification spécifique pour l'ajout de Jobi
          await supabase
            .from('user_notifications')
            .insert({
              user_id: userId,
              title: 'Bonus fidélité : 20 Jobi reçus !',
              content: 'Vous avez reçu 20 Jobi pour votre fidélité au renouvellement de votre abonnement Premium. Merci !',
              type: 'jobi',
              is_read: false,
              is_archived: false,
              link: '/dashboard/jobi'
            });
          logger.info(`20 Jobi ajoutés pour l'utilisateur ${userId} suite au renouvellement Premium`);
        } catch (error) {
          logger.error('Erreur lors de l\'ajout des 20 Jobi de renouvellement:', error);
        }
      }
    } else {
      // Log détaillé expliquant pourquoi aucun crédit n'a été attribué
      if (changeType !== 'renewal' && changeType !== 'upgrade') {
        logger.info(`Aucun crédit IA/Jobi attribué pour l'utilisateur ${userId} (changeType=${changeType} n'est ni 'renewal' ni 'upgrade')`);
      } else if (isModification) {
        logger.info(`Aucun crédit IA/Jobi attribué pour l'utilisateur ${userId} (isModification=true, simple modification d'abonnement)`);
      } else if (alreadyRenewed) {
        logger.info(`Aucun crédit IA/Jobi attribué pour l'utilisateur ${userId} (alreadyRenewed=true, déjà attribué pour cette période)`);
      } else {
        logger.info(`Aucun crédit IA/Jobi attribué pour l'utilisateur ${userId} (raison inconnue)`);
      }
    }

    // Appliquer le code promo uniquement si l'utilisateur passe en premium
    if (plan === 'premium' && options && options.promoCodeApplied) {
      try {
        const promocodesController = require('../controllers/promocodes').default;
        await promocodesController.applyPromoCode(options.promoCodeApplied, userId, plan);
        logger.info(`Code promo ${options.promoCodeApplied} appliqué (usage enregistré) pour l'utilisateur ${userId}`);
      } catch (error) {
        logger.error(`Erreur lors de l'enregistrement de l'utilisation du code promo (webhook):`, error);
      }
    }

    // Correction zone d'intervention si besoin
    const { default: subscriptions } = await import('../config/ConfigSubscriptions');
    const profilRes = await supabase
      .from('user_profil')
      .select('intervention_zone')
      .eq('user_id', userId)
      .single();
    if (profilRes.data) {
      // Décrypter les données du profil
      const decryptedProfil = await decryptProfilDataAsync(profilRes.data);
      let intervention_zone = decryptedProfil?.intervention_zone || {};
      // Déterminer le plan et les options
      const isPremium = plan === 'premium';
      const planConfig = isPremium ? subscriptions.premium : subscriptions.gratuit;
      const hasFranceEntiere = options && options.franceEntiere === true && planConfig.interventionAreas.franceEntiere.enabled;
      if (hasFranceEntiere) {
        // Si l'option France entière est active, on force les valeurs
        intervention_zone.france_entiere = true;
        intervention_zone.radius = 1000;
        intervention_zone.adresse = 'France entière';
      } else if (options && options.interventionAreas) {
        // Si l'utilisateur a acheté des km supplémentaires, on applique ce rayon
        intervention_zone.france_entiere = false;
        intervention_zone.radius = options.interventionAreas;
        if (intervention_zone.adresse === 'France entière') {
          intervention_zone.adresse = '';
        }
      } else {
        // Sinon, on remet la config par défaut du plan
        intervention_zone.france_entiere = false;
        intervention_zone.radius = planConfig.interventionAreas.included;
        if (intervention_zone.adresse === 'France entière') {
          intervention_zone.adresse = '';
        }
      }
      await supabase
        .from('user_profil')
        .update({ intervention_zone })
        .eq('user_id', userId);
      // Invalider le cache profil utilisateur
      await redis.del(`user:${userId}`);
      await redis.del(`user_deux:${userId}`);
    }

    // Mettre à jour les templates (cartes de visite et flyers) en fonction du plan d'abonnement
    try {
      const templateSubscriptionService = require('../services/templateSubscriptionService').default;
      const result = await templateSubscriptionService.updateTemplatesBasedOnSubscription(userId);

      if (result.success) {
        // Si des templates ont été activés ou désactivés, ajouter une notification
        const activatedBusinessCards = result.result.businessCards.activated.length;
        const deactivatedBusinessCards = result.result.businessCards.deactivated.length;
        const activatedFlyers = result.result.flyers.activated.length;
        const deactivatedFlyers = result.result.flyers.deactivated.length;

        if (activatedBusinessCards > 0 || activatedFlyers > 0) {
          let content = '';
          if (activatedBusinessCards > 0) {
            content += `${activatedBusinessCards} carte(s) de visite ont été automatiquement activées. `;
          }
          if (activatedFlyers > 0) {
            content += `${activatedFlyers} flyer(s) ont été automatiquement activés. `;
          }

          await supabase
            .from('user_notifications')
            .insert({
              user_id: userId,
              title: 'Templates activés',
              content: content.trim(),
              type: 'system',
              is_read: false,
              is_archived: false,
              link: '/dashboard/card-templates'
            });
        }

        if (deactivatedBusinessCards > 0 || deactivatedFlyers > 0) {
          let content = '';
          if (deactivatedBusinessCards > 0) {
            content += `${deactivatedBusinessCards} carte(s) de visite ont été automatiquement désactivées en raison des limitations de votre abonnement. `;
          }
          if (deactivatedFlyers > 0) {
            content += `${deactivatedFlyers} flyer(s) ont été automatiquement désactivés en raison des limitations de votre abonnement. `;
          }
          content += 'Passez à l\'abonnement premium pour les activer à nouveau.';

          await supabase
            .from('user_notifications')
            .insert({
              user_id: userId,
              title: 'Templates désactivés',
              content: content.trim(),
              type: 'system',
              is_read: false,
              is_archived: false,
              link: '/dashboard/premium'
            });
        }

        logger.info(`Templates mis à jour pour l'utilisateur ${userId}: ${activatedBusinessCards} cartes de visite activées, ${deactivatedBusinessCards} désactivées, ${activatedFlyers} flyers activés, ${deactivatedFlyers} désactivés`);
      } else {
        logger.error(`Erreur lors de la mise à jour des templates pour l'utilisateur ${userId}:`, result.error);
      }
    } catch (error) {
      logger.error(`Erreur lors de la mise à jour des templates pour l'utilisateur ${userId}:`, error);
    }
  } catch (error) {
    logger.error('Erreur lors de la mise à jour de l\'abonnement:', error);
  }
}

// Gérer les annulations d'abonnement
export async function handleSubscriptionCanceled(subscription: any) {
  try {
    const userId = subscription.metadata.userId;

    if (!userId) {
      logger.error('UserId manquant dans les métadonnées de l\'abonnement');
      return;
    }

    // Mettre à jour l'abonnement dans la base de données
    await supabase
      .from('user_abo')
      .update({
        statut: 'inactif',
        renouvellement_auto: false,
        type_abonnement: 'gratuit' // Passer à l'abonnement gratuit
      })
      .eq('user_id', userId)
      .eq('stripe_subscription_id', subscription.id);

    // Invalider le cache
    await redis.del(`subscription_status:${userId}`);
    logger.info(`Abonnement annulé pour l'utilisateur ${userId}`);

    // Envoyer une notification à l'utilisateur
    // Récupérer les informations de l'utilisateur
    const { data: user, error: userError } = await supabase
      .from('users')
      .select('email')
      .eq('id', userId)
      .single();

    if (userError || !user) {
      logger.error('Utilisateur non trouvé:', userId);
      return;
    }

    // Déchiffrer les données utilisateur
    const decryptedUser = await decryptUserDataAsync(user);

    // Récupérer le profil utilisateur
    const { data: userProfil } = await supabase
      .from('user_profil')
      .select('nom, prenom')
      .eq('user_id', userId)
      .single();

    // Déchiffrer les données sensibles du profil
    const decryptedUserProfil = userProfil ? await decryptProfilDataAsync(userProfil) : null;

    // Si le profil n'existe pas ou n'a pas de nom/prénom, utiliser des valeurs par défaut
    const nom = decryptedUserProfil?.nom || 'Utilisateur';
    const prenom = decryptedUserProfil?.prenom || '';
    const fullName = `${prenom} ${nom}`.trim();
    const frontendUrl = process.env.FRONTEND_URL || 'http://localhost:5173';

    // Ajuster les fonctionnalités premium
    const adjustmentResult = await adjustPremiumFeatures(userId);

    // Préparer le message de notification avec les ajustements
    let notificationContent = 'Votre abonnement Premium a été annulé. Votre compte est maintenant passé au plan Gratuit.';

    if (adjustmentResult.success && adjustmentResult.summary !== 'Aucun ajustement nécessaire') {
      notificationContent += ` Certaines fonctionnalités ont été ajustées : ${adjustmentResult.summary}.`;
    }

    // Récupérer les informations sur les ajustements pour l'email
    const adjustments = adjustmentResult.adjustments as any;

    // Préparer les paramètres pour l'email
    const emailParams: any = {
      userName: fullName,
      oldPlan: 'premium',
      newPlan: 'gratuit',
      changeType: 'downgrade',
      autoRenew: false,
      endDate: subscription.current_period_end ? new Date(subscription.current_period_end * 1000).toISOString() : new Date().toISOString(),
      frontendUrl
    };

    // Ajouter les informations sur les services désactivés
    if (adjustments?.services?.adjusted) {
      emailParams.disabledServices = adjustments.services.items;
    }

    // Ajouter les informations sur les galeries désactivées
    if (adjustments?.galleries?.adjusted) {
      emailParams.disabledGalleries = adjustments.galleries.items;
    }

    // Ajouter les informations sur les favoris supprimés
    if (adjustments?.favorites?.adjusted) {
      emailParams.disabledFavorites = adjustments.favorites.count;
    }

    // Ajouter les informations sur la zone d'intervention
    if (adjustments?.interventionZone) {
      emailParams.interventionZone = adjustments.interventionZone;
    }

    // Ajouter les informations sur la visibilité du téléphone
    if (adjustments?.phoneVisibility?.adjusted) {
      emailParams.phoneVisibilityDisabled = true;
    }

    // Ajouter les informations sur les cartes de visite désactivées
    if (adjustments?.businessCards?.adjusted) {
      emailParams.disabledBusinessCards = adjustments.businessCards.items;
    }

    // Ajouter les informations sur les flyers désactivés
    if (adjustments?.flyers?.adjusted) {
      emailParams.disabledFlyers = adjustments.flyers.items;
    }

    // Envoyer l'email avec le nouveau type
    if (user && user.email) {
      await sendSubscriptionStatusChangeEmail(decryptedUser.email, {
        userName: fullName,
        newPlan: 'gratuit',
        oldPlan: 'premium',
        changeType: 'cancellation',
        html: generateEnhancedSubscriptionEmail(emailParams)
      });

      // Ajout d'un log pour confirmer l'envoi
      logger.info(`Email de notification d'annulation d'abonnement envoyé à ${decryptedUser.email}`);
    }

    // Créer une notification dans le dashboard
    await supabase
      .from('user_notifications')
      .insert({
        user_id: userId,
        title: 'Abonnement annulé',
        content: notificationContent,
        type: 'system',
        is_read: false,
        is_archived: false,
        link: '/dashboard/premium'
      });

    // Correction zone d'intervention si besoin
    const profilRes = await supabase
      .from('user_profil')
      .select('intervention_zone')
      .eq('user_id', userId)
      .single();
    if (profilRes.data) {
      // Décrypter les données du profil
      const decryptedProfil = await decryptProfilDataAsync(profilRes.data);
      let intervention_zone = decryptedProfil?.intervention_zone || {};
      const planConfig = subscriptions.gratuit;
      if (intervention_zone.france_entiere && !planConfig.interventionAreas.franceEntiere.enabled) {
        intervention_zone.france_entiere = false;
        intervention_zone.radius = planConfig.interventionAreas.included;
        if (intervention_zone.adresse === 'France entière') {
          intervention_zone.adresse = '';
        }
      } else if (intervention_zone.radius > planConfig.interventionAreas.included) {
        intervention_zone.radius = planConfig.interventionAreas.included;
      }
      await supabase
        .from('user_profil')
        .update({ intervention_zone })
        .eq('user_id', userId);
      // Invalider le cache profil utilisateur
      await redis.del(`user:${userId}`);
      await redis.del(`user_deux:${userId}`);
    }
  } catch (error) {
    logger.error('Erreur lors de l\'annulation de l\'abonnement:', error);
  }
}

// Fonction pour effectuer un downgrade d'abonnement premium vers gratuit
export async function performSubscriptionDowngrade(userAbo: any) {
  try {
    // IMPORTANT: Réinitialiser les options aux valeurs par défaut du plan gratuit
    const defaultGratuitOptions = {
      services: subscriptions.gratuit.services.included,
      galleries: subscriptions.gratuit.galleries.included,
      interventionAreas: subscriptions.gratuit.interventionAreas.included,
      conversations_messages_prives: subscriptions.gratuit.conversations_messages_prives.included,
      quotes: subscriptions.gratuit.quotes.included,
      invoices: subscriptions.gratuit.invoices.included,
      planning_slots: subscriptions.gratuit.planning_slots.included,
      missionResponses: subscriptions.gratuit.missionResponses.included,
      favoriteLimit: subscriptions.gratuit.favoriteLimit.included,
      businessCards: subscriptions.gratuit.businessCards.included,
      flyers: subscriptions.gratuit.flyers.included,
      franceEntiere: false // Toujours false pour le plan gratuit
    };

    // Mettre à jour l'abonnement dans la base de données
    await supabase
      .from('user_abo')
      .update({
        type_abonnement: 'gratuit',
        renouvellement_auto: false,
        montant: 0,
        options: defaultGratuitOptions
      })
      .eq('user_id', userAbo.user_id);

    // Invalider le cache
    await redis.del(`subscription_status:${userAbo.user_id}`);
    await redis.del(`subscription_limits:${userAbo.user_id}`);

    logger.info(`Downgrade appliqué pour l'utilisateur ${userAbo.user_id}: premium -> gratuit`);

    // Ajuster les fonctionnalités premium
    const adjustmentResult = await adjustPremiumFeatures(userAbo.user_id);

    // Récupérer les informations de l'utilisateur pour l'email
    const { data: user } = await supabase
      .from('users')
      .select('email')
      .eq('id', userAbo.user_id)
      .single();

    const { data: userProfil } = await supabase
      .from('user_profil')
      .select('nom, prenom')
      .eq('user_id', userAbo.user_id)
      .single();

    if (user && userProfil) {
      // Déchiffrer les données utilisateur
      const decryptedUser = await decryptUserDataAsync(user);
      const decryptedUserProfil = await decryptProfilDataAsync(userProfil);

      const nom = decryptedUserProfil?.nom || 'Utilisateur';
      const prenom = decryptedUserProfil?.prenom || '';
      const fullName = `${prenom} ${nom}`.trim();
      const frontendUrl = process.env.FRONTEND_URL || 'http://localhost:5173';

      // Préparer le message de notification avec les ajustements
      let notificationContent = 'Votre abonnement Premium a expiré et vous êtes désormais sur le plan Gratuit.';

      if (adjustmentResult.success && adjustmentResult.summary !== 'Aucun ajustement nécessaire') {
        notificationContent += ` Certaines fonctionnalités ont été ajustées : ${adjustmentResult.summary}.`;
      }

      // Récupérer les informations sur les ajustements pour l'email
      const adjustments = adjustmentResult.adjustments as any;

      // Préparer les paramètres pour l'email
      const emailParams: any = {
        userName: fullName,
        oldPlan: userAbo.type_abonnement,
        newPlan: 'gratuit',
        changeType: 'downgrade',
        autoRenew: false,
        oldOptions: userAbo.options || {},
        newOptions: defaultGratuitOptions,
        frontendUrl
      };

      // Ajouter les informations sur les services désactivés
      if (adjustments?.services?.adjusted) {
        emailParams.disabledServices = adjustments.services.items;
      }

      // Ajouter les informations sur les galeries désactivées
      if (adjustments?.galleries?.adjusted) {
        emailParams.disabledGalleries = adjustments.galleries.items;
      }

      // Ajouter les informations sur les favoris supprimés
      if (adjustments?.favorites?.adjusted) {
        emailParams.disabledFavorites = adjustments.favorites.count;
      }

      // Ajouter les informations sur la zone d'intervention
      if (adjustments?.interventionZone) {
        emailParams.interventionZone = adjustments.interventionZone;
      }

      // Ajouter les informations sur la visibilité du téléphone
      if (adjustments?.phoneVisibility?.adjusted) {
        emailParams.phoneVisibilityDisabled = true;
      }

      // Ajouter les informations sur les cartes de visite désactivées
      if (adjustments?.businessCards?.adjusted) {
        emailParams.disabledBusinessCards = adjustments.businessCards.items;
      }

      // Ajouter les informations sur les flyers désactivés
      if (adjustments?.flyers?.adjusted) {
        emailParams.disabledFlyers = adjustments.flyers.items;
      }

      // Envoyer l'email de downgrade
      if (decryptedUser.email) {
        await sendSubscriptionStatusChangeEmail(decryptedUser.email, emailParams);
        logger.info(`Email de downgrade envoyé à ${decryptedUser.email} pour l'utilisateur ${userAbo.user_id}`);
      }

      // Ajouter une notification dans l'application
      await supabase
        .from('user_notifications')
        .insert({
          user_id: userAbo.user_id,
          title: 'Passage au plan Gratuit',
          content: notificationContent,
          type: 'system',
          is_read: false,
          is_archived: false,
          link: '/dashboard/premium'
        });

      // Correction zone d'intervention si besoin (France entière -> département)
      if (adjustments?.interventionZone) {
        const intervention_zone = adjustments.interventionZone;
        await supabase
          .from('user_profil')
          .update({ intervention_zone })
          .eq('user_id', userAbo.user_id);
        // Invalider le cache profil utilisateur
        await redis.del(`user:${userAbo.user_id}`);
        await redis.del(`user_deux:${userAbo.user_id}`);
      }
    }

    return {
      success: true,
      adjustments: adjustmentResult
    };
  } catch (error) {
    logger.error(`Erreur lors du downgrade pour l'utilisateur ${userAbo.user_id}:`, error);
    return {
      success: false,
      error: error
    };
  }
}

// Notifier l'utilisateur du prochain prélèvement
export async function notifyUpcomingPayment(invoice: any) {
  try {
    const customerId = invoice.customer;

    // Récupérer l'abonnement associé au client Stripe
    const { data: userAbo, error: aboError } = await supabase
      .from('user_abo')
      .select('user_id, type_abonnement, options')
      .eq('stripe_customer_id', customerId)
      .eq('statut', 'actif')
      .single();

    if (aboError || !userAbo) {
      logger.error('Abonnement non trouvé pour le client Stripe:', customerId);
      return;
    }

    // Récupérer les informations de l'utilisateur
    const { data: user, error: userError } = await supabase
      .from('users')
      .select('email')
      .eq('id', userAbo.user_id)
      .single();

    if (userError || !user) {
      logger.error('Utilisateur non trouvé:', userAbo.user_id);
      return;
    }

    // Déchiffrer les données utilisateur
    const decryptedUser = await decryptUserDataAsync(user);

    // Récupérer le profil utilisateur
    const { data: userProfil } = await supabase
      .from('user_profil')
      .select('nom, prenom')
      .eq('user_id', userAbo.user_id)
      .single();

    // Déchiffrer les données sensibles du profil
    const decryptedUserProfil = userProfil ? await decryptProfilDataAsync(userProfil) : null;

    // Si le profil n'existe pas ou n'a pas de nom/prénom, utiliser des valeurs par défaut
    const nom = decryptedUserProfil?.nom || 'Utilisateur';
    const prenom = decryptedUserProfil?.prenom || '';
    const fullName = `${prenom} ${nom}`.trim();
    // Envoyer une notification de paiement à venir
    const frontendUrl = process.env.FRONTEND_URL || 'http://localhost:5173';

    // Calculer le montant du prochain paiement
    const amount = invoice.amount_due ? (invoice.amount_due / 100) : 0;
    const currency = invoice.currency || 'eur';

    // Date du prochain prélèvement
    const nextPaymentDate = invoice.next_payment_attempt
      ? new Date(invoice.next_payment_attempt * 1000).toLocaleDateString('fr-FR')
      : 'prochainement';

    if (user && user.email) {
      // Envoyer un email de notification de paiement à venir
      const emailHtml = `
        <h2>Bonjour ${fullName},</h2>
        <p>Votre prochain prélèvement pour votre abonnement <strong>${userAbo.type_abonnement}</strong> aura lieu ${nextPaymentDate}.</p>
        <p><strong>Montant :</strong> ${amount}€</p>
        <p>Aucune action n'est requise de votre part, le paiement sera automatiquement prélevé.</p>
        <p>Si vous souhaitez modifier ou annuler votre abonnement, vous pouvez le faire depuis votre <a href="${frontendUrl}/dashboard/premium">espace client</a>.</p>
        <p>Cordialement,<br>L'équipe JobPartiel</p>
      `;

      await queueEmail(
        decryptedUser.email,
        `Prochain prélèvement ${userAbo.type_abonnement} - JobPartiel`,
        emailHtml
      );

      logger.info(`Email de notification de paiement à venir envoyé à ${decryptedUser.email} pour l'utilisateur ${userAbo.user_id}`);
    }

    // Ajouter une notification dans l'application
    await supabase
      .from('user_notifications')
      .insert({
        user_id: userAbo.user_id,
        title: 'Prochain prélèvement',
        content: `Votre prochain prélèvement de ${amount}€ pour votre abonnement ${userAbo.type_abonnement} aura lieu ${nextPaymentDate}.`,
        type: 'system',
        is_read: false,
        is_archived: false,
        link: '/dashboard/premium'
      });

    // Correction zone d'intervention si besoin
    const profilRes = await supabase
      .from('user_profil')
      .select('intervention_zone')
      .eq('user_id', userAbo.user_id)
      .single();
    if (profilRes.data) {
      // Décrypter les données du profil
      const decryptedProfil = await decryptProfilDataAsync(profilRes.data);
      let intervention_zone = decryptedProfil?.intervention_zone || {};
      const planConfig = subscriptions.gratuit;
      // L'option France entière n'est pas disponible dans le plan gratuit
      // Donc on la désactive toujours lors du passage au plan gratuit
      intervention_zone.france_entiere = false;
      intervention_zone.radius = planConfig.interventionAreas.included;
      if (intervention_zone.adresse === 'France entière') {
        intervention_zone.adresse = '';
      }
      await supabase
        .from('user_profil')
        .update({ intervention_zone })
        .eq('user_id', userAbo.user_id);
      // Invalider le cache profil utilisateur
      await redis.del(`user:${userAbo.user_id}`);
      await redis.del(`user_deux:${userAbo.user_id}`);
    }
  } catch (error) {
    logger.error('Erreur lors de l\'envoi de la notification de prochain prélèvement:', error);
  }
}

// Fonction pour ajuster les fonctionnalités premium lors d'un downgrade
async function adjustPremiumFeatures(userId: string) {
  try {
    const freeConfig = subscriptions.gratuit;

    // Résultats des ajustements
    const adjustments: {
      services: { adjusted: boolean, count: number, items: Array<{id: string, titre: string}> },
      galleries: { adjusted: boolean, count: number, items: Array<{id: string, titre: string}> },
      favorites: { adjusted: boolean, count: number },
      phoneVisibility: { adjusted: boolean },
      missionResponses: { adjusted: boolean },
      planningSlots: { adjusted: boolean, count: number },
      businessCards: { adjusted: boolean, count: number, items: Array<{id: string, name: string}> },
      flyers: { adjusted: boolean, count: number, items: Array<{id: string, name: string}> }
    } = {
      services: { adjusted: false, count: 0, items: [] },
      galleries: { adjusted: false, count: 0, items: [] },
      favorites: { adjusted: false, count: 0 },
      phoneVisibility: { adjusted: false },
      missionResponses: { adjusted: false },
      planningSlots: { adjusted: false, count: 0 },
      businessCards: { adjusted: false, count: 0, items: [] },
      flyers: { adjusted: false, count: 0, items: [] }
    };

    // 1. Ajuster les services
    const serviceLimit = freeConfig.services.included;
    const { data: userServices, error: servicesError } = await supabase
      .from('user_services')
      .select('id, titre')
      .eq('user_id', userId)
      .eq('statut', 'actif')
      .order('created_at', { ascending: false });

    if (!servicesError && userServices && userServices.length > serviceLimit) {
      const servicesToDisable = userServices.slice(serviceLimit);

      for (const service of servicesToDisable) {
        await supabase
          .from('user_services')
          .update({ statut: 'inactif' })
          .eq('id', service.id);

        logger.info(`Service désactivé pour l'utilisateur ${userId}: ${service.titre} (${service.id})`);
      }

      // Invalider le cache des services
      await redis.del(`user_services:${userId}`);

      adjustments.services = {
        adjusted: true,
        count: servicesToDisable.length,
        items: servicesToDisable.map(s => ({ id: s.id, titre: s.titre }))
      };
    }

    // 2. Ajuster les galeries
    const galleryLimit = freeConfig.galleries.included;
    const { data: userGalleries, error: galleriesError } = await supabase
      .from('user_gallery')
      .select('id, name, status')
      .eq('user_id', userId)
      .eq('status', 'actif')
      .order('created_at', { ascending: false });

    if (!galleriesError && userGalleries && userGalleries.length > galleryLimit) {
      const galleriesToDisable = userGalleries.slice(galleryLimit);

      // Désactiver les galeries excédentaires au lieu de les supprimer
      for (const gallery of galleriesToDisable) {
        // Mettre à jour le statut de la galerie à 'inactif'
        await supabase
          .from('user_gallery')
          .update({ status: 'inactif' })
          .eq('id', gallery.id);

        logger.info(`Galerie désactivée pour l'utilisateur ${userId}: ${gallery.name} (${gallery.id})`);
      }

      // Invalider le cache des galeries
      await redis.del(`user_gallery:${userId}`);
      // Invalider également le cache spécifique à l'utilisateur
      const { data: userProfil } = await supabase
        .from('user_profil')
        .select('slug')
        .eq('user_id', userId)
        .single();

      if (userProfil?.slug) {
        await redis.del(`user_gallery_user_specific:${userProfil.slug}`);
      }

      adjustments.galleries = {
        adjusted: true,
        count: galleriesToDisable.length,
        items: galleriesToDisable.map(g => ({ id: g.id, titre: g.name }))
      };
    }

    // 3. Ajuster les favoris
    const favoriteLimit = freeConfig.favoriteLimit.included;
    const { data: userFavorites, error: favoritesError } = await supabase
      .from('user_favorites')
      .select('id, target_user_id')
      .eq('user_id', userId)
      .order('created_at', { ascending: false });

    if (!favoritesError && userFavorites && userFavorites.length > favoriteLimit) {
      const favoritesToRemove = userFavorites.slice(favoriteLimit);

      for (const favorite of favoritesToRemove) {
        await supabase
          .from('user_favorites')
          .delete()
          .eq('id', favorite.id);

        logger.info(`Favori supprimé pour l'utilisateur ${userId}: ${favorite.id}`);
      }

      // Invalider le cache des favoris
      await redis.del(`user_favorites:${userId}`);

      adjustments.favorites = {
        adjusted: true,
        count: favoritesToRemove.length
      };
    }

    // 4. Désactiver la visibilité du téléphone
    if (!freeConfig.phoneVisibility.enabled) {
      const { data: userProfil } = await supabase
        .from('user_profil')
        .select('phone_visible')
        .eq('user_id', userId)
        .single();

      if (userProfil && userProfil.phone_visible) {
        await supabase
          .from('user_profil')
          .update({ phone_visible: false })
          .eq('user_id', userId);

        adjustments.phoneVisibility = { adjusted: true };

        // Invalider le cache du profil
        await redis.del(`user:${userId}`);
        await redis.del(`user_deux:${userId}`);
      }
    }

    // 5. Ajuster les cartes de visite
    const businessCardsLimit = freeConfig.businessCards.included;
    const { data: userBusinessCards, error: businessCardsError } = await supabase
      .from('card_templates')
      .select('id, name, type')
      .eq('user_id', userId)
      .in('type', ['business_card', 'business_card_landscape'])
      .order('created_at', { ascending: false });

    if (!businessCardsError && userBusinessCards && userBusinessCards.length > businessCardsLimit) {
      const cardsToDisable = userBusinessCards.slice(businessCardsLimit);

      for (const card of cardsToDisable) {
        // Mettre à jour le statut de la carte à 'inactif' en ajoutant un préfixe au nom
        await supabase
          .from('card_templates')
          .update({
            name: `[DÉSACTIVÉ] ${card.name}`,
            is_public: false
          })
          .eq('id', card.id);

        logger.info(`Carte de visite désactivée pour l'utilisateur ${userId}: ${card.name} (${card.id})`);
      }

      // Invalider le cache des templates
      await redis.del(`card_editor:templates:${userId}`);

      adjustments.businessCards = {
        adjusted: true,
        count: cardsToDisable.length,
        items: cardsToDisable.map(c => ({ id: c.id, name: c.name }))
      };
    }

    // 6. Ajuster les flyers
    const flyersLimit = freeConfig.flyers.included;
    const { data: userFlyers, error: flyersError } = await supabase
      .from('card_templates')
      .select('id, name, type')
      .eq('user_id', userId)
      .in('type', ['flyer', 'flyer_landscape'])
      .order('created_at', { ascending: false });

    if (!flyersError && userFlyers && userFlyers.length > flyersLimit) {
      const flyersToDisable = userFlyers.slice(flyersLimit);

      for (const flyer of flyersToDisable) {
        // Mettre à jour le statut du flyer à 'inactif' en ajoutant un préfixe au nom
        await supabase
          .from('card_templates')
          .update({
            name: `[DÉSACTIVÉ] ${flyer.name}`,
            is_public: false
          })
          .eq('id', flyer.id);

        logger.info(`Flyer désactivé pour l'utilisateur ${userId}: ${flyer.name} (${flyer.id})`);
      }

      // Invalider le cache des templates
      await redis.del(`card_editor:templates:${userId}`);

      adjustments.flyers = {
        adjusted: true,
        count: flyersToDisable.length,
        items: flyersToDisable.map(f => ({ id: f.id, name: f.name }))
      };
    }

    // Générer un résumé des ajustements
    const adjustmentSummary = [];

    if (adjustments.services.adjusted) {
      adjustmentSummary.push(`${adjustments.services.count} service(s) désactivé(s)`);
    }

    if (adjustments.galleries.adjusted) {
      adjustmentSummary.push(`${adjustments.galleries.count} galerie(s) désactivée(s)`);
    }

    if (adjustments.favorites.adjusted) {
      adjustmentSummary.push(`${adjustments.favorites.count} favori(s) supprimé(s)`);
    }

    if (adjustments.phoneVisibility.adjusted) {
      adjustmentSummary.push(`Visibilité du téléphone désactivée`);
    }

    if (adjustments.businessCards.adjusted) {
      adjustmentSummary.push(`${adjustments.businessCards.count} carte(s) de visite désactivée(s)`);
    }

    if (adjustments.flyers.adjusted) {
      adjustmentSummary.push(`${adjustments.flyers.count} flyer(s) désactivé(s)`);
    }

    return {
      success: true,
      adjustments,
      summary: adjustmentSummary.length > 0 ? adjustmentSummary.join(', ') : 'Aucun ajustement nécessaire'
    };
  } catch (error) {
    logger.error(`Erreur lors de l'ajustement des fonctionnalités premium pour l'utilisateur ${userId}:`, error);
    return {
      success: false,
      adjustments: {},
      summary: 'Erreur lors de l\'ajustement des fonctionnalités'
    };
  }
}

// Fonction pour vérifier et appliquer les downgrades planifiés
export async function checkAndApplyScheduledDowngrades() {
  try {
    const now = new Date();
    // Récupérer tous les abonnements premium expirés (actifs ou annulés)
    const { data: abosToDowngrade, error } = await supabase
      .from('user_abo')
      .select('*')
      .in('statut', ['actif', 'annule']) // ✅ Inclure les abonnements annulés par admin
      .eq('type_abonnement', 'premium')
      // .eq('renouvellement_auto', false)
      .lt('date_fin', now.toISOString());

      console.log('abonnements à downgrader', abosToDowngrade);

    if (error) {
      logger.error('Erreur lors de la récupération des abonnements à downgrader:', error);
      return;
    }

    for (const abo of abosToDowngrade || []) {
      // Utiliser la fonction dédiée pour le downgrade
      const downgradeResult = await performSubscriptionDowngrade(abo);

      if (downgradeResult.success) {
        logger.info(`Downgrade automatique réussi pour l'utilisateur ${abo.user_id}: premium -> gratuit`);
      } else {
        logger.error(`Erreur lors du downgrade automatique pour l'utilisateur ${abo.user_id}:`, downgradeResult.error);
        continue; // Passer au suivant en cas d'erreur
      }


    }
    logger.info(`${(abosToDowngrade || []).length} abonnements ont été downgradés automatiquement`);
  } catch (error) {
    logger.error('Erreur lors de l\'application des downgrades planifiés:', error);
  }
}

// Fonction utilitaire pour vérifier si l'utilisateur est premium et obtenir ses limites
export const getUserSubscriptionLimits = async (userId: string) => {
  try {
    // Vérifier si les données sont en cache
    const cacheKey = `subscription_limits:${userId}`;
    const cachedData = await redis.get(cacheKey);

    if (cachedData) {
      // Utiliser les données du cache
      logger.info(`Utilisation du cache pour les limites d'abonnement de l'utilisateur ${userId}`);
      return JSON.parse(cachedData);
    }

    // Si pas de cache, récupérer les données de la base de données
    // Vérifier si l'utilisateur a un abonnement premium
    const { data: userAbo, error: aboError } = await supabase
      .from('user_abo')
      .select('type_abonnement, options, date_debut, date_fin, renouvellement_auto, montant, stripe_subscription_id')
      .eq('user_id', userId)
      .eq('statut', 'actif')
      .maybeSingle();

    const isPremium = !aboError && userAbo?.type_abonnement === 'premium';

    // Récupérer les informations de l'abonnement
    const abonnementType = isPremium ? 'premium' : 'gratuit';
    const options = userAbo?.options || {};
    const startDate = userAbo?.date_debut || null;
    const endDate = userAbo?.date_fin || null;
    const autoRenew = userAbo?.renouvellement_auto || false;
    const price = userAbo?.montant || 0;
    const stripeSubscriptionId = userAbo?.stripe_subscription_id || null;

    // Récupérer les limites configurées pour ce type d'abonnement,
    // en tenant compte des options personnalisées si elles existent

    // Pour les devis (quotes)
    const baseQuoteLimit = isPremium
      ? subscriptions.premium.quotes.included
      : subscriptions.gratuit.quotes.included;

    // Utiliser la valeur personnalisée si elle existe, sinon la valeur de base
    const quoteLimit = options.quotes
      ? options.quotes
      : baseQuoteLimit;

    // Pour les factures (invoices)
    const baseInvoiceLimit = isPremium
      ? subscriptions.premium.invoices.included
      : subscriptions.gratuit.invoices.included;

    // Utiliser la valeur personnalisée si elle existe, sinon la valeur de base
    const invoiceLimit = options.invoices
      ? options.invoices
      : baseInvoiceLimit;

    // Pour d'autres limites qui pourraient être personnalisées...
    // Par exemple, pour les services, conversations, etc.
    const serviceLimit = options.services
      ? options.services
      : (isPremium ? subscriptions.premium.services.included : subscriptions.gratuit.services.included);

    const galleriesLimit = options.galleries
      ? options.galleries
      : (isPremium ? subscriptions.premium.galleries.included : subscriptions.gratuit.galleries.included);

    const interventionAreasLimit = options.interventionAreas
      ? options.interventionAreas
      : (isPremium ? subscriptions.premium.interventionAreas.included : subscriptions.gratuit.interventionAreas.included);

    const conversationsLimit = options.conversations_messages_prives
      ? options.conversations_messages_prives
      : (isPremium ? subscriptions.premium.conversations_messages_prives.included : subscriptions.gratuit.conversations_messages_prives.included);

    // Pour l'historique des logs
    const historyLogsLimit = options.history_logs
      ? options.history_logs
      : (isPremium ? subscriptions.premium.history_logs.included : subscriptions.gratuit.history_logs.included);

    // Pour les créneaux de planning
    const planningSlotsLimit = options.planning_slots
      ? options.planning_slots
      : (isPremium ? subscriptions.premium.planning_slots?.included || 0 : subscriptions.gratuit.planning_slots?.included || 0);

    // Pour les réponses/candidatures aux missions
    const missionResponsesLimit = options.missionResponses
      ? options.missionResponses
      : (isPremium ? subscriptions.premium.missionResponses.included : subscriptions.gratuit.missionResponses.included);

    // Pour la limite de favoris
    const baseFavoriteLimit = isPremium
      ? subscriptions.premium.favoriteLimit.included
      : subscriptions.gratuit.favoriteLimit.included;
    const favoriteLimit = options.favoriteLimit
      ? options.favoriteLimit
      : baseFavoriteLimit;

    // Pour la limite de cartes de visite
    const baseBusinessCardsLimit = isPremium
      ? subscriptions.premium.businessCards.included
      : subscriptions.gratuit.businessCards.included;
    const businessCardsLimit = options.businessCards
      ? options.businessCards
      : baseBusinessCardsLimit;

    // Pour la limite de flyers
    const baseFlyersLimit = isPremium
      ? subscriptions.premium.flyers.included
      : subscriptions.gratuit.flyers.included;
    const flyersLimit = options.flyers
      ? options.flyers
      : baseFlyersLimit;

    const subscriptionLimits = {
      isPremium,
      plan: abonnementType,
      options,
      startDate,
      endDate,
      autoRenew,
      price,
      stripeSubscriptionId,
      quoteLimit,
      invoiceLimit,
      serviceLimit,
      galleriesLimit,
      interventionAreasLimit,
      conversationsLimit,
      historyLogsLimit,
      planningSlotsLimit,
      missionResponsesLimit,
      favoriteLimit,
      businessCardsLimit,
      flyersLimit,
      // Fonctionnalités spécifiques au plan
      features: isPremium ? {
        advancedStats: true,
        unlimitedPhotos: true,
        prioritySupport: true,
        featuredListing: true
      } : {
        advancedStats: false,
        unlimitedPhotos: false,
        prioritySupport: false,
        featuredListing: false
      }
    };

    // Mettre en cache les données pour 5 minutes (300 secondes)
    await redis.set(cacheKey, JSON.stringify(subscriptionLimits), 'EX', 300);

    return subscriptionLimits;
  } catch (error) {
    logger.error('Erreur lors de la récupération des informations d\'abonnement:', error);
    // En cas d'erreur, retourner les valeurs par défaut pour l'abonnement gratuit
    return {
      isPremium: false,
      plan: 'gratuit',
      options: {},
      startDate: null,
      endDate: null,
      autoRenew: false,
      price: 0,
      stripeSubscriptionId: null,
      quoteLimit: subscriptions.gratuit.quotes.included,
      invoiceLimit: subscriptions.gratuit.invoices.included,
      serviceLimit: subscriptions.gratuit.services.included,
      galleriesLimit: subscriptions.gratuit.galleries.included,
      interventionAreasLimit: subscriptions.gratuit.interventionAreas.included,
      conversationsLimit: subscriptions.gratuit.conversations_messages_prives.included,
      historyLogsLimit: subscriptions.gratuit.history_logs.included,
      planningSlotsLimit: subscriptions.gratuit.planning_slots?.included || 0,
      missionResponsesLimit: subscriptions.gratuit.missionResponses.included,
      favoriteLimit: subscriptions.gratuit.favoriteLimit.included,
      businessCardsLimit: subscriptions.gratuit.businessCards.included,
      flyersLimit: subscriptions.gratuit.flyers.included,
      features: {
        advancedStats: false,
        unlimitedPhotos: false,
        prioritySupport: false,
        featuredListing: false
      }
    };
  }
};

// Utilitaire pour générer une comparaison détaillée des options d'abonnement
function generateSubscriptionComparisonHTML(oldPlan: string, newPlan: string, oldOptions: any = {}, newOptions: any = {}) {
  const oldConfig = oldPlan === 'premium' ? subscriptions.premium : subscriptions.gratuit;
  const newConfig = newPlan === 'premium' ? subscriptions.premium : subscriptions.gratuit;

  // Formatage des options pour affichage
  const formatOptions = (options: any, config: any) => {
    const result: any = {};

    // Service limits
    result.services = options.services || config.services.included;
    result.galleries = options.galleries || config.galleries.included;
    result.interventionAreas = options.interventionAreas || config.interventionAreas.included;
    result.franceEntiere = options.franceEntiere || false;
    result.conversations = options.conversations_messages_prives || config.conversations_messages_prives.included;
    result.missionResponses = options.missionResponses || config.missionResponses.included;
    result.quotes = options.quotes || config.quotes.included;
    result.invoices = options.invoices || config.invoices.included;
    result.historyLogs = options.history_logs || config.history_logs.included;
    result.planningSlots = options.planning_slots || (config.planning_slots?.included || 0);
    result.favoriteLimit = options.favoriteLimit || config.favoriteLimit?.included || 0;
    result.aiCredits = options.aiCredits || config.aiCredits?.included || 0;
    result.businessCards = options.businessCards || config.businessCards?.included || 0;
    result.flyers = options.flyers || config.flyers?.included || 0;

    return result;
  };

  const oldOpts = formatOptions(oldOptions, oldConfig);
  const newOpts = formatOptions(newOptions, newConfig);

  // Construction du tableau de comparaison
  const tableRows = `
    <tr>
      <th style="text-align: left; padding: 8px; border-bottom: 1px solid #eee;">Fonctionnalité</th>
      <th style="text-align: center; padding: 8px; border-bottom: 1px solid #eee;">Ancienne valeur</th>
      <th style="text-align: center; padding: 8px; border-bottom: 1px solid #eee;">Nouvelle valeur</th>
    </tr>
    <tr style="${oldOpts.services !== newOpts.services ? 'background-color: #fff8f3;' : ''}">
      <td style="padding: 8px; border-bottom: 1px solid #eee;">Services</td>
      <td style="text-align: center; padding: 8px; border-bottom: 1px solid #eee;">${oldOpts.services} ${oldOpts.services === 999 ? '(illimité)' : ''}</td>
      <td style="text-align: center; padding: 8px; border-bottom: 1px solid #eee;">${newOpts.services} ${newOpts.services === 999 ? '(illimité)' : ''}</td>
    </tr>
    <tr style="${oldOpts.galleries !== newOpts.galleries ? 'background-color: #fff8f3;' : ''}">
      <td style="padding: 8px; border-bottom: 1px solid #eee;">Galeries photos</td>
      <td style="text-align: center; padding: 8px; border-bottom: 1px solid #eee;">${oldOpts.galleries} ${oldOpts.galleries === 999 ? '(illimité)' : ''}</td>
      <td style="text-align: center; padding: 8px; border-bottom: 1px solid #eee;">${newOpts.galleries} ${newOpts.galleries === 999 ? '(illimité)' : ''}</td>
    </tr>
    <tr style="${oldOpts.interventionAreas !== newOpts.interventionAreas || oldOpts.franceEntiere !== newOpts.franceEntiere ? 'background-color: #fff8f3;' : ''}">
      <td style="padding: 8px; border-bottom: 1px solid #eee;">Zone d'intervention</td>
      <td style="text-align: center; padding: 8px; border-bottom: 1px solid #eee;">${oldOpts.franceEntiere ? 'France entière' : `${oldOpts.interventionAreas} km`}</td>
      <td style="text-align: center; padding: 8px; border-bottom: 1px solid #eee;">${newOpts.franceEntiere ? 'France entière' : `${newOpts.interventionAreas} km`}</td>
    </tr>
    <tr style="${oldOpts.conversations !== newOpts.conversations ? 'background-color: #fff8f3;' : ''}">
      <td style="padding: 8px; border-bottom: 1px solid #eee;">Conversations privées</td>
      <td style="text-align: center; padding: 8px; border-bottom: 1px solid #eee;">${oldOpts.conversations} ${oldOpts.conversations === 999 ? '(illimité)' : ''}</td>
      <td style="text-align: center; padding: 8px; border-bottom: 1px solid #eee;">${newOpts.conversations} ${newOpts.conversations === 999 ? '(illimité)' : ''}</td>
    </tr>
    <tr style="${oldOpts.missionResponses !== newOpts.missionResponses ? 'background-color: #fff8f3;' : ''}">
      <td style="padding: 8px; border-bottom: 1px solid #eee;">Réponses aux missions</td>
      <td style="text-align: center; padding: 8px; border-bottom: 1px solid #eee;">${oldOpts.missionResponses} ${oldOpts.missionResponses === 999 ? '(illimité)' : ''}</td>
      <td style="text-align: center; padding: 8px; border-bottom: 1px solid #eee;">${newOpts.missionResponses} ${newOpts.missionResponses === 999 ? '(illimité)' : ''}</td>
    </tr>
    <tr style="${oldOpts.quotes !== newOpts.quotes ? 'background-color: #fff8f3;' : ''}">
      <td style="padding: 8px; border-bottom: 1px solid #eee;">Devis</td>
      <td style="text-align: center; padding: 8px; border-bottom: 1px solid #eee;">${oldOpts.quotes} ${oldOpts.quotes === 999 ? '(illimité)' : ''}</td>
      <td style="text-align: center; padding: 8px; border-bottom: 1px solid #eee;">${newOpts.quotes} ${newOpts.quotes === 999 ? '(illimité)' : ''}</td>
    </tr>
    <tr style="${oldOpts.invoices !== newOpts.invoices ? 'background-color: #fff8f3;' : ''}">
      <td style="padding: 8px; border-bottom: 1px solid #eee;">Factures</td>
      <td style="text-align: center; padding: 8px; border-bottom: 1px solid #eee;">${oldOpts.invoices} ${oldOpts.invoices === 999 ? '(illimité)' : ''}</td>
      <td style="text-align: center; padding: 8px; border-bottom: 1px solid #eee;">${newOpts.invoices} ${newOpts.invoices === 999 ? '(illimité)' : ''}</td>
    </tr>
    <tr style="${oldOpts.historyLogs !== newOpts.historyLogs ? 'background-color: #fff8f3;' : ''}">
      <td style="padding: 8px; border-bottom: 1px solid #eee;">Historique des logs</td>
      <td style="text-align: center; padding: 8px; border-bottom: 1px solid #eee;">${oldOpts.historyLogs} ${oldOpts.historyLogs === 150 || oldOpts.historyLogs === 999 ? '(complet)' : '(limité)'}</td>
      <td style="text-align: center; padding: 8px; border-bottom: 1px solid #eee;">${newOpts.historyLogs} ${newOpts.historyLogs === 150 || newOpts.historyLogs === 999 ? '(complet)' : '(limité)'}</td>
    </tr>
    <tr style="${oldOpts.planningSlots !== newOpts.planningSlots ? 'background-color: #fff8f3;' : ''}">
      <td style="padding: 8px; border-bottom: 1px solid #eee;">Créneaux de planning</td>
      <td style="text-align: center; padding: 8px; border-bottom: 1px solid #eee;">${oldOpts.planningSlots} ${oldOpts.planningSlots === 999 ? '(illimité)' : ''}</td>
      <td style="text-align: center; padding: 8px; border-bottom: 1px solid #eee;">${newOpts.planningSlots} ${newOpts.planningSlots === 999 ? '(illimité)' : ''}</td>
    </tr>
    <tr style="${oldOpts.favoriteLimit !== newOpts.favoriteLimit ? 'background-color: #fff8f3;' : ''}">
      <td style="padding: 8px; border-bottom: 1px solid #eee;">Favoris maximum</td>
      <td style="text-align: center; padding: 8px; border-bottom: 1px solid #eee;">${oldOpts.favoriteLimit} ${oldOpts.favoriteLimit === 999 ? '(illimité)' : ''}</td>
      <td style="text-align: center; padding: 8px; border-bottom: 1px solid #eee;">${newOpts.favoriteLimit} ${newOpts.favoriteLimit === 999 ? '(illimité)' : ''}</td>
    </tr>
    <tr style="${oldOpts.aiCredits !== newOpts.aiCredits ? 'background-color: #fff8f3;' : ''}">
      <td style="padding: 8px; border-bottom: 1px solid #eee;">Crédits IA mensuels</td>
      <td style="text-align: center; padding: 8px; border-bottom: 1px solid #eee;">${oldOpts.aiCredits} ${oldOpts.aiCredits === 999 ? '(illimité)' : ''}</td>
      <td style="text-align: center; padding: 8px; border-bottom: 1px solid #eee;">${newOpts.aiCredits} ${newOpts.aiCredits === 999 ? '(illimité)' : ''}</td>
    </tr>
    <tr style="${oldOpts.businessCards !== newOpts.businessCards ? 'background-color: #fff8f3;' : ''}">
      <td style="padding: 8px; border-bottom: 1px solid #eee;">Cartes de visite</td>
      <td style="text-align: center; padding: 8px; border-bottom: 1px solid #eee;">${oldOpts.businessCards} ${oldOpts.businessCards === 999 ? '(illimité)' : ''}</td>
      <td style="text-align: center; padding: 8px; border-bottom: 1px solid #eee;">${newOpts.businessCards} ${newOpts.businessCards === 999 ? '(illimité)' : ''}</td>
    </tr>
    <tr style="${oldOpts.flyers !== newOpts.flyers ? 'background-color: #fff8f3;' : ''}">
      <td style="padding: 8px; border-bottom: 1px solid #eee;">Flyers</td>
      <td style="text-align: center; padding: 8px; border-bottom: 1px solid #eee;">${oldOpts.flyers} ${oldOpts.flyers === 999 ? '(illimité)' : ''}</td>
      <td style="text-align: center; padding: 8px; border-bottom: 1px solid #eee;">${newOpts.flyers} ${newOpts.flyers === 999 ? '(illimité)' : ''}</td>
    </tr>
  `;

  // Fonctionnalités Premium supplémentaires
  const premiumFeatures = `
    <tr>
      <th colspan="3" style="text-align: left; padding: 8px; border-bottom: 1px solid #eee; background-color: #f9f9f9;">Fonctionnalités Premium</th>
    </tr>
    <tr style="${oldPlan !== newPlan ? 'background-color: #fff8f3;' : ''}">
      <td style="padding: 8px; border-bottom: 1px solid #eee;">Statistiques avancées</td>
      <td style="text-align: center; padding: 8px; border-bottom: 1px solid #eee;">${oldPlan === 'premium' ? '✅' : '❌'}</td>
      <td style="text-align: center; padding: 8px; border-bottom: 1px solid #eee;">${newPlan === 'premium' ? '✅' : '❌'}</td>
    </tr>
    <tr style="${oldPlan !== newPlan ? 'background-color: #fff8f3;' : ''}">
      <td style="padding: 8px; border-bottom: 1px solid #eee;">Photos illimitées</td>
      <td style="text-align: center; padding: 8px; border-bottom: 1px solid #eee;">${oldPlan === 'premium' ? '✅' : '❌'}</td>
      <td style="text-align: center; padding: 8px; border-bottom: 1px solid #eee;">${newPlan === 'premium' ? '✅' : '❌'}</td>
    </tr>
    <tr style="${oldPlan !== newPlan ? 'background-color: #fff8f3;' : ''}">
      <td style="padding: 8px; border-bottom: 1px solid #eee;">Support prioritaire</td>
      <td style="text-align: center; padding: 8px; border-bottom: 1px solid #eee;">${oldPlan === 'premium' ? '✅' : '❌'}</td>
      <td style="text-align: center; padding: 8px; border-bottom: 1px solid #eee;">${newPlan === 'premium' ? '✅' : '❌'}</td>
    </tr>
    <tr style="${oldPlan !== newPlan ? 'background-color: #fff8f3;' : ''}">
      <td style="padding: 8px; border-bottom: 1px solid #eee;">Annonces en évidence</td>
      <td style="text-align: center; padding: 8px; border-bottom: 1px solid #eee;">${oldPlan === 'premium' ? '✅' : '❌'}</td>
      <td style="text-align: center; padding: 8px; border-bottom: 1px solid #eee;">${newPlan === 'premium' ? '✅' : '❌'}</td>
    </tr>
    <tr style="${oldPlan !== newPlan ? 'background-color: #fff8f3;' : ''}">
      <td style="padding: 8px; border-bottom: 1px solid #eee;">Visibilité des numéros sur les profils</td>
      <td style="text-align: center; padding: 8px; border-bottom: 1px solid #eee;">${oldConfig.phoneVisibility?.enabled ? '✅' : '❌'}</td>
      <td style="text-align: center; padding: 8px; border-bottom: 1px solid #eee;">${newConfig.phoneVisibility?.enabled ? '✅' : '❌'}</td>
    </tr>
    <tr style="${oldPlan !== newPlan ? 'background-color: #fff8f3;' : ''}">
      <td style="padding: 8px; border-bottom: 1px solid #eee;">Bonus fidélité 20 Jobi</td>
      <td style="text-align: center; padding: 8px; border-bottom: 1px solid #eee;">${oldPlan === 'premium' ? '✅' : '❌'}</td>
      <td style="text-align: center; padding: 8px; border-bottom: 1px solid #eee;">${newPlan === 'premium' ? '✅' : '❌'}</td>
    </tr>
  `;

  // Construction du tableau complet
  return `
    <div style="margin: 20px 0;">
      <table style="width: 100%; border-collapse: collapse; border: 1px solid #eee; font-size: 14px;">
        ${tableRows}
        ${premiumFeatures}
      </table>
    </div>
  `;
}

// Fonction pour générer un email amélioré pour les changements d'abonnement
function generateEnhancedSubscriptionEmail(params: {
  userName: string,
  oldPlan: string,
  newPlan: string,
  changeType: 'upgrade' | 'downgrade' | 'renewal' | 'modification' | 'cancel_auto_renew' | 'reactivate_auto_renew' | 'renewal_reminder' | 'unbanned_reactivation',
  startDate?: string,
  endDate?: string,
  autoRenew?: boolean,
  price?: number,
  oldOptions?: any,
  newOptions?: any,
  promoCodeApplied?: string,
  discountAmount?: number,
  discountType?: 'percentage' | 'fixed',
  frontendUrl: string,
  // Informations sur les services actuels pour les avertissements de downgrade
  serviceCount?: number,
  galleryCount?: number,
  favoriteCount?: number,
  interventionZone?: number,
  conversationCount?: number,
  businessCardCount?: number,
  flyerCount?: number,
  // Informations sur les services désactivés (pour les emails de downgrade)
  disabledServices?: Array<{id: string, titre: string}>,
  disabledGalleries?: Array<{id: string, titre: string}>,
  disabledFavorites?: number,
  phoneVisibilityDisabled?: boolean,
  disabledBusinessCards?: Array<{id: string, name: string}>,
  disabledFlyers?: Array<{id: string, name: string}>
}) {
  const {
    userName,
    oldPlan,
    newPlan,
    changeType,
    startDate,
    endDate,
    autoRenew = true,
    price = 0,
    oldOptions = {},
    newOptions = {},
    promoCodeApplied,
    discountAmount,
    discountType,
    frontendUrl
  } = params;

  // Titre et message principal en fonction du type de changement
  let title = '';
  let mainMessage = '';
  let additionalMessage = '';

  const formattedStartDate = startDate ? new Date(startDate).toLocaleDateString('fr-FR') : '';
  const formattedEndDate = endDate ? new Date(endDate).toLocaleDateString('fr-FR') : '';

  switch (changeType) {
    case 'upgrade':
      title = 'Bienvenue sur le plan Premium !';
      mainMessage = `
        <p>Félicitations ! Votre compte a été mis à niveau vers le <b>plan Premium</b>.</p>
        <p>Votre abonnement est maintenant actif${formattedStartDate ? ` depuis le ${formattedStartDate}` : ''}
        ${formattedEndDate ? ` jusqu'au ${formattedEndDate}` : ''}
        et sera ${autoRenew ? '' : '<b>ne sera pas</b> '}automatiquement renouvelé à la fin de votre période d'abonnement.</p>
        ${promoCodeApplied ? `<p>Vous avez bénéficié du code promo <b>${promoCodeApplied}</b>
        avec une réduction de ${discountType === 'percentage' ? `${discountAmount}%` : `${discountAmount}€`}.</p>` : ''}
      `;
      additionalMessage = `
        <p>Explorez toutes les fonctionnalités de votre nouvel abonnement et profitez pleinement de vos avantages Premium !</p>
        <p><strong>Crédits IA :</strong> Votre abonnement Premium inclut ${subscriptions.premium.aiCredits.included} crédits IA par mois (ajoutés uniquement lorsque vous avez moins de ${subscriptions.premium.aiCredits.included} crédits). Ces crédits vous permettent de générer du contenu avec l'intelligence artificielle sur JobPartiel.</p>
      `;
      break;
    case 'downgrade':
      // Construire la liste des services désactivés
      let disabledServicesList = '';
      let disabledGalleriesList = '';
      let disabledFavoritesInfo = '';
      let zoneInfo = '';
      let phoneVisibilityInfo = '';

      // Vérifier si des informations sur les services désactivés sont disponibles
      if (params.disabledServices && params.disabledServices.length > 0) {
        disabledServicesList = `
          <li>
            <strong>${params.disabledServices.length} service(s) ont été désactivés</strong> :
            <ul>
              ${params.disabledServices.map(service =>
                `<li>${service.titre}</li>`
              ).join('')}
            </ul>
          </li>
        `;
      }

      // Vérifier si des informations sur les galeries désactivées sont disponibles
      if (params.disabledGalleries && params.disabledGalleries.length > 0) {
        disabledGalleriesList = `
          <li>
            <strong>${params.disabledGalleries.length} galerie(s) ont été désactivées</strong> :
            <ul>
              ${params.disabledGalleries.map(gallery =>
                `<li>${gallery.titre}</li>`
              ).join('')}
            </ul>
          </li>
        `;
      }

      // Vérifier si des informations sur les favoris supprimés sont disponibles
      if (params.disabledFavorites && params.disabledFavorites > 0) {
        disabledFavoritesInfo = `<li><strong>${params.disabledFavorites} favori(s) ont été supprimés</strong></li>`;
      }

      // Vérifier si la zone d'intervention a été modifiée
      if (params.interventionZone) {
        zoneInfo = `<li>Votre <strong>zone d'intervention</strong> a été réduite à 15 km (était : ${params.interventionZone} km)</li>`;
      }

      // Vérifier si la visibilité du téléphone a été désactivée
      if (params.phoneVisibilityDisabled) {
        phoneVisibilityInfo = `<li>La <strong>visibilité de votre numéro de téléphone</strong> a été désactivée</li>`;
      }

      // Vérifier si des informations sur les cartes de visite désactivées sont disponibles
      let disabledBusinessCardsList = '';
      if (params.disabledBusinessCards && params.disabledBusinessCards.length > 0) {
        disabledBusinessCardsList = `
          <li>
            <strong>${params.disabledBusinessCards.length} carte(s) de visite ont été désactivées</strong> :
            <ul>
              ${params.disabledBusinessCards.map(card =>
                `<li>${card.name}</li>`
              ).join('')}
            </ul>
          </li>
        `;
      }

      // Vérifier si des informations sur les flyers désactivés sont disponibles
      let disabledFlyersList = '';
      if (params.disabledFlyers && params.disabledFlyers.length > 0) {
        disabledFlyersList = `
          <li>
            <strong>${params.disabledFlyers.length} flyer(s) ont été désactivés</strong> :
            <ul>
              ${params.disabledFlyers.map(flyer =>
                `<li>${flyer.name}</li>`
              ).join('')}
            </ul>
          </li>
        `;
      }

      // Construire la liste des ajustements
      const adjustmentsList = [
        disabledServicesList,
        disabledGalleriesList,
        disabledFavoritesInfo,
        zoneInfo,
        phoneVisibilityInfo,
        disabledBusinessCardsList,
        disabledFlyersList
      ].filter(item => item !== '')
        .join('');

      // Message d'ajustement par défaut si aucune information spécifique n'est disponible
      const defaultAdjustments = `
        <li>Les services au-delà de 2 ont été désactivés</li>
        <li>Les galeries photos au-delà de 1 ont été désactivées</li>
        <li>La zone d'intervention a été limitée à 15 km</li>
        <li>Les conversations privées sont limitées à 2</li>
        <li>Les favoris au-delà de 3 ont été supprimés</li>
        <li>La visibilité de votre numéro de téléphone a été désactivée</li>
        <li>Les cartes de visite au-delà de 1 ont été désactivées</li>
        <li>Les flyers au-delà de 1 ont été désactivés</li>
      `;

      title = 'Votre abonnement a changé';
      mainMessage = `
        <p>Votre abonnement est maintenant passé du <b>plan ${oldPlan}</b> au <b>plan ${newPlan}</b>.</p>
        <p>Vous continuez à bénéficier de l'accès à vos données, mais certaines fonctionnalités avancées sont désormais limitées.</p>

        <div style="margin: 20px 0; padding: 15px; background-color: #ffebee; border-left: 4px solid #f44336; color: #d32f2f;">
          <p style="margin: 0; font-weight: bold; font-size: 16px;">⚠️ IMPORTANT</p>
          <p style="margin-top: 10px;">Suite au passage au plan Gratuit, certains de vos services et fonctionnalités ont été automatiquement ajustés :</p>
          <ul style="margin-top: 5px; padding-left: 20px;">
            ${adjustmentsList || defaultAdjustments}
          </ul>
          <p style="margin-top: 10px;">Pour retrouver l'accès à toutes vos fonctionnalités, vous pouvez réactiver votre abonnement Premium à tout moment.</p>
        </div>
      `;
      additionalMessage = `
        <p>Vous pouvez à tout moment réactiver un abonnement Premium pour débloquer toutes les fonctionnalités et booster votre activité !</p>
      `;
      break;
    case 'renewal':
      title = 'Renouvellement de votre abonnement';
      mainMessage = `
        <p>Votre abonnement au <b>plan ${newPlan}</b> a été renouvelé avec succès.</p>
        <p>Votre abonnement est maintenant actif${formattedStartDate ? ` depuis le ${formattedStartDate}` : ''}
        ${formattedEndDate ? ` jusqu'au ${formattedEndDate}` : ''}${price ? ` au tarif de ${price}€/mois` : ''}.</p>
        ${newPlan === 'premium' ? '<p><strong>Bonus fidélité :</strong> 20 Jobi ont été ajoutés à votre compte pour ce renouvellement !</p>' : ''}
        ${newPlan === 'premium' ? `<p><strong>Crédits IA :</strong> ${subscriptions.premium.aiCredits.included} crédits IA ont été ajoutés à votre compte (uniquement si vous aviez moins de ${subscriptions.premium.aiCredits.included} crédits).</p>` : ''}
      `;
      additionalMessage = `
        <p>Nous vous remercions pour votre confiance et restons à votre disposition pour toute question.</p>
      `;
      break;
    case 'modification':
      title = 'Votre abonnement a été modifié';
      mainMessage = `
        <p>Votre abonnement <b>plan ${newPlan}</b> a été modifié avec succès.</p>
        <p>Les nouvelles options et tarifs sont désormais actifs (${price ? `${price}€/mois` : 'tarif inchangé'}).</p>
        ${promoCodeApplied ? `<p>Vous avez bénéficié du code promo <b>${promoCodeApplied}</b>
        avec une réduction de ${discountType === 'percentage' ? `${discountAmount}%` : `${discountAmount}€`}.</p>` : ''}
        <div style="background-color: rgba(255, 0, 0, 0.15); border-left: 4px solid #FF0000; padding: 15px; margin: 20px 0; color: #000; font-weight: bold;">
          <span style="display: block; font-size: 16px; margin-bottom: 5px;">⚠️ IMPORTANT</span>
          Le montant de votre prochain prélèvement peut différer en raison de calculs au prorata. Pour voir le détail complet de votre facturation, accédez à votre espace personnel et cliquez sur le bouton "Portail abonnement".
          ${changeType === 'modification' ? `
          <div style="margin-top: 15px; text-align: center;">
            <a href="${frontendUrl}/dashboard/premium" style="display: inline-block; background-color: #FF0000; color: white; padding: 10px 20px; text-decoration: none; border-radius: 4px; font-weight: bold;">
              🔍 Accéder à mon espace personnel
            </a>
          </div>` : ''}
        </div>
      `;
      additionalMessage = `
        <p>Vous pouvez consulter ou gérer votre abonnement à tout moment depuis votre espace personnel ou accéder directement à votre portail client Stripe pour visualiser les détails de facturation.</p>
      `;
      break;
    case 'cancel_auto_renew':
      // Récupérer les informations sur les services actuels de l'utilisateur
      let serviceWarning = '';
      let galleryWarning = '';
      let favoriteWarning = '';
      let zoneWarning = '';
      let conversationWarning = '';

      // Vérifier si des informations sur les services actuels sont disponibles dans les options
      if (params.serviceCount && params.serviceCount > subscriptions.gratuit.services.included) {
        const excessServices = params.serviceCount - subscriptions.gratuit.services.included;
        serviceWarning = `<li><strong>${excessServices} service(s)</strong> seront désactivés (limite : 2 services)</li>`;
      }

      if (params.galleryCount && params.galleryCount > subscriptions.gratuit.galleries.included) {
        const excessGalleries = params.galleryCount - subscriptions.gratuit.galleries.included;
        galleryWarning = `<li><strong>${excessGalleries} galerie(s)</strong> seront désactivées (limite : 1 galerie)</li>`;
      }

      if (params.favoriteCount && params.favoriteCount > subscriptions.gratuit.favoriteLimit.included) {
        const excessFavorites = params.favoriteCount - subscriptions.gratuit.favoriteLimit.included;
        favoriteWarning = `<li><strong>${excessFavorites} favori(s)</strong> seront supprimés (limite : 3 favoris)</li>`;
      }

      if (params.interventionZone && params.interventionZone > subscriptions.gratuit.interventionAreas.included) {
        zoneWarning = `<li>Votre <strong>zone d'intervention</strong> sera réduite de ${params.interventionZone} km à 15 km</li>`;
      }

      if (params.conversationCount && params.conversationCount > subscriptions.gratuit.conversations_messages_prives.included) {
        const excessConversations = params.conversationCount - subscriptions.gratuit.conversations_messages_prives.included;
        conversationWarning = `<li><strong>${excessConversations} conversation(s)</strong> seront limitées (limite : 2 conversations)</li>`;
      }

      // Vérifier les cartes de visite
      let businessCardWarning = '';
      if (params.businessCardCount && params.businessCardCount > subscriptions.gratuit.businessCards.included) {
        const excessBusinessCards = params.businessCardCount - subscriptions.gratuit.businessCards.included;
        businessCardWarning = `<li><strong>${excessBusinessCards} carte(s) de visite</strong> seront désactivées (limite : ${subscriptions.gratuit.businessCards.included} carte(s))</li>`;
      }

      // Vérifier les flyers
      let flyerWarning = '';
      if (params.flyerCount && params.flyerCount > subscriptions.gratuit.flyers.included) {
        const excessFlyers = params.flyerCount - subscriptions.gratuit.flyers.included;
        flyerWarning = `<li><strong>${excessFlyers} flyer(s)</strong> seront désactivés (limite : ${subscriptions.gratuit.flyers.included} flyer(s))</li>`;
      }

      // Construire la liste des avertissements
      const warningsList = [
        serviceWarning,
        galleryWarning,
        favoriteWarning,
        zoneWarning,
        conversationWarning,
        businessCardWarning,
        flyerWarning
      ].filter(warning => warning !== '')
        .join('');

      // Message d'avertissement par défaut si aucune information spécifique n'est disponible
      const defaultWarning = `
        <li>Services : maximum 2 services pour le plan gratuit</li>
        <li>Galeries photos : maximum 1 galerie pour le plan gratuit</li>
        <li>Zone d'intervention : maximum 15 km pour le plan gratuit</li>
        <li>Conversations privées : maximum 2 pour le plan gratuit</li>
        <li>Favoris : maximum 3 pour le plan gratuit</li>
        <li>Cartes de visite : maximum ${subscriptions.gratuit.businessCards.included} pour le plan gratuit</li>
        <li>Flyers : maximum ${subscriptions.gratuit.flyers.included} pour le plan gratuit</li>
      `;

      title = 'Renouvellement automatique désactivé';
      mainMessage = `
        <p>Vous avez demandé l'annulation du renouvellement automatique de votre abonnement <b>${oldPlan}</b>.</p>
        <p>Votre abonnement ${oldPlan} restera actif jusqu'à la date de fin de votre période actuelle (<b>${formattedEndDate}</b>),
        puis passera automatiquement au <b>plan Gratuit</b>.</p>
        <p>Pendant cette période, vous continuez à bénéficier de tous les avantages ${oldPlan}.
        Après la date de fin, certaines fonctionnalités avancées seront limitées.</p>

        <div style="margin: 20px 0; padding: 15px; background-color: #ffebee; border-left: 4px solid #f44336; color: #d32f2f;">
          <p style="margin: 0; font-weight: bold; font-size: 16px;">⚠️ ATTENTION</p>
          <p style="margin-top: 10px;">À la fin de votre abonnement Premium, les services en excès seront automatiquement désactivés car ils dépasseront la limite du plan gratuit.</p>
          <ul style="margin-top: 5px; padding-left: 20px;">
            ${warningsList || defaultWarning}
          </ul>
          <p style="margin-top: 10px;">Pour éviter cela, vous pouvez désactiver manuellement certains services avant la fin de votre abonnement ou renouveler votre abonnement Premium.</p>
        </div>
      `;
      additionalMessage = `
        <p>Vous pouvez à tout moment réactiver un abonnement Premium pour débloquer toutes les fonctionnalités !</p>
      `;
      break;
    case 'reactivate_auto_renew':
      title = 'Renouvellement automatique réactivé';
      mainMessage = `
        <p>Le renouvellement automatique de votre abonnement <b>Premium</b> a bien été réactivé.</p>
        <p>Votre abonnement continuera sans interruption à la fin de la période actuelle.</p>
      `;
      additionalMessage = `
        <p>Vous pouvez consulter ou gérer votre abonnement à tout moment depuis votre espace personnel.</p>
      `;
      break;
    case 'renewal_reminder':
      title = 'Rappel de renouvellement';
      mainMessage = `
        <p>Votre abonnement <b>${newPlan}</b> est sur le point de se renouveler.</p>
        <p>Le montant de <b>${price}€/mois</b> sera prélevé automatiquement à la fin de votre période actuelle.</p>
        <p>Vous pouvez gérer votre abonnement à tout moment depuis votre espace personnel.</p>
        ${newPlan === 'premium' ? `<p><strong>Rappel des avantages Premium :</strong> Lors du renouvellement, vous recevrez 20 Jobi en bonus fidélité et ${subscriptions.premium.aiCredits.included} crédits IA (si vous avez moins de ${subscriptions.premium.aiCredits.included} crédits).</p>` : ''}
      `;
      additionalMessage = `
        <p>Nous vous rappelons que votre abonnement est sur le point de se renouveler. Si vous souhaitez le prolonger, veuillez vous rendre sur votre espace personnel et cliquer sur le bouton "Renouveler".</p>
      `;
      break;
    case 'unbanned_reactivation':
      title = 'Réactivation de votre abonnement';
      mainMessage = `
        <p>Suite à la réactivation de votre compte, votre abonnement au <b>plan ${newPlan}</b> a été automatiquement réactivé.</p>
        <p>Votre abonnement est maintenant actif${formattedStartDate ? ` depuis le ${formattedStartDate}` : ''}
        ${formattedEndDate ? ` jusqu'au ${formattedEndDate}` : ''}${price ? ` au tarif de ${price}€/mois` : ''}.</p>
        <p><strong>Note importante :</strong> Cette réactivation suite à un débannissement ne génère pas de bonus Jobi. Les bonus de 20 Jobi sont uniquement attribués lors des renouvellements réguliers d'abonnement.</p>
        ${newPlan === 'premium' ? `<p><strong>Crédits IA :</strong> Votre abonnement Premium inclut ${subscriptions.premium.aiCredits.included} crédits IA par mois (ajoutés uniquement lorsque vous avez moins de ${subscriptions.premium.aiCredits.included} crédits).</p>` : ''}
      `;
      additionalMessage = `
        <p>Nous vous remercions pour votre confiance et restons à votre disposition pour toute question.</p>
      `;
      break;
  }

  // Construction de l'email complet
  const subscriptionComparison = generateSubscriptionComparisonHTML(oldPlan, newPlan, oldOptions, newOptions);

  return `<!DOCTYPE html>
  <html>
  <head>
    <meta charset="utf-8">
    <style>
      body { font-family: Arial, sans-serif; color: #333; line-height: 1.6; margin: 0; padding: 0; }
      .container { max-width: 600px; margin: 0 auto; padding: 20px; border: 1px solid #e5e7eb; border-radius: 8px; }
      .header { background-color: #FF7A35; color: white; padding: 15px 20px; border-radius: 8px 8px 0 0; margin: -20px -20px 20px; }
      .footer { background-color: #f9fafb; padding: 15px 20px; border-top: 1px solid #e5e7eb; margin: 20px -20px -20px; border-radius: 0 0 8px 8px; font-size: 12px; color: #6b7280; }
      h1 { margin: 0; font-size: 24px; }
      .message-box { background-color: #fff8f3; border-left: 4px solid #FF7A35; padding: 15px; margin: 20px 0; }
      .feature-list { background-color: #f3f4f6; padding: 15px; border-radius: 4px; margin-bottom: 20px; }
      .feature-item { padding: 5px 0; }
      .feature-item:before { content: "✅ "; }
      .button { display: inline-block; background-color: #FF7A35; color: white; padding: 12px 24px; text-decoration: none; border-radius: 4px; font-weight: bold; margin-top: 20px; }
      .comparison-title { font-size: 18px; font-weight: bold; margin: 20px 0 10px; }
      .price-box { background-color: #f3f4f6; border-radius: 4px; padding: 10px 15px; display: inline-block; margin-bottom: 15px; }
      .price-value { font-size: 20px; font-weight: bold; color: #FF7A35; }
      .price-details { display: block; font-size: 12px; color: #666; }
      @media (max-width: 600px) { .container { width: 100%; padding: 10px; } .header { padding: 10px; margin: -10px -10px 15px; } .footer { margin: 15px -10px -10px; padding: 10px; } }
    </style>
  </head>
  <body>
    <div class="container">
      <div class="header">
        <h1>${title}</h1>
      </div>
      <p>Bonjour <b>${userName}</b>,</p>
      <div class="message-box">
        ${mainMessage}
      </div>

      ${changeType === 'renewal' || changeType === 'upgrade' || changeType === 'unbanned_reactivation' || price > 0 ? `
      <div class="price-box">
        <span class="price-value">${price}€ / mois</span>
        <span class="price-details">${autoRenew ? 'Renouvellement automatique' : 'Sans renouvellement automatique'}</span>
      </div>
      ` : ''}

      <p class="comparison-title">Détails de votre abonnement :</p>
      ${subscriptionComparison}

      ${additionalMessage}

      <a href="${frontendUrl}/dashboard/premium" class="button">Gérer mon abonnement</a>
      <div class="footer">
        Si vous avez des questions, notre équipe support est à votre écoute.<br>
        Merci de votre confiance.<br>
        <b>L'équipe support</b>
      </div>
    </div>
  </body>
  </html>`;
}

// Route pour réactiver le renouvellement automatique d'un abonnement Premium
router.post('/reactivate-auto-renew', async (req: Request, res: Response): Promise<void> => {
  try {
    const userId = req.user?.userId;
    if (!userId) {
      res.status(401).json({
        success: false,
        message: 'Utilisateur non authentifié',
        toastType: 'error'
      });
      return;
    }

    // Récupérer l'abonnement premium actif
    const { data: existingAbo, error } = await supabase
      .from('user_abo')
      .select('*')
      .eq('user_id', userId)
      .eq('statut', 'actif')
      .eq('type_abonnement', 'premium')
      .maybeSingle();

    if (error || !existingAbo || !existingAbo.stripe_subscription_id) {
      res.status(404).json({
        success: false,
        message: 'Aucun abonnement Premium actif à réactiver',
        toastType: 'error'
      });
      return;
    }

    // Récupérer l'abonnement Stripe
    const subscription = await stripe.subscriptions.retrieve(existingAbo.stripe_subscription_id);

    if (!subscription.cancel_at_period_end) {
      res.status(400).json({
        success: false,
        message: 'Le renouvellement automatique est déjà actif',
        toasttype: 'system'
      });
    }

    // Réactiver le renouvellement automatique sur Stripe
    await stripe.subscriptions.update(existingAbo.stripe_subscription_id, {
      cancel_at_period_end: false
    });

    // Mettre à jour la base de données
    await supabase
      .from('user_abo')
      .update({ renouvellement_auto: true })
      .eq('id', existingAbo.id);

    // Invalider le cache
    await redis.del(`subscription_status:${userId}`);

    // Récupérer les infos utilisateur pour l'email et la notification
    const { data: user } = await supabase
      .from('users')
      .select('email')
      .eq('id', userId)
      .single();
    const { data: userProfil } = await supabase
      .from('user_profil')
      .select('nom, prenom')
      .eq('user_id', userId)
      .single();

    // Déchiffrer les données utilisateur
    const decryptedUser = user ? await decryptUserDataAsync(user) : null;

    // Déchiffrer les données sensibles du profil
    const decryptedUserProfil = userProfil ? await decryptProfilDataAsync(userProfil) : null;

    const nom = decryptedUserProfil?.nom || 'Utilisateur';
    const prenom = decryptedUserProfil?.prenom || '';
    const fullName = `${prenom} ${nom}`.trim();
    const frontendUrl = process.env.FRONTEND_URL || 'http://localhost:5173';

    // Envoi d'un email d'information
    if (decryptedUser && decryptedUser.email) {
      await sendSubscriptionStatusChangeEmail(decryptedUser.email, {
        userName: fullName,
        newPlan: 'premium',
        oldPlan: 'premium',
        changeType: 'reactivate_auto_renew',
        html: generateEnhancedSubscriptionEmail({
          userName: fullName,
          oldPlan: 'premium',
          newPlan: 'premium',
          changeType: 'reactivate_auto_renew',
          frontendUrl
      })
      });
    }

    // Créer une notification dans le dashboard
    await supabase
      .from('user_notifications')
      .insert({
        user_id: userId,
        title: 'Renouvellement automatique réactivé',
        content: 'Le renouvellement automatique de votre abonnement Premium a bien été réactivé. Votre abonnement continuera sans interruption.',
        type: 'system',
        is_read: false,
        is_archived: false,
        link: '/dashboard/premium'
      });

    // Journaliser la réactivation du renouvellement automatique
    await logUserActivity(
      userId,
      'reactivate_auto_renew',
      undefined,
      'subscription',
      { plan: 'premium', oldPlan: 'premium' },
      undefined
    );

    res.json({
      success: true,
      message: 'Le renouvellement automatique a été réactivé avec succès.',
      toastType: 'success'
    });
    return;
  } catch (error) {
    logger.error('Erreur lors de la réactivation du renouvellement auto:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur lors de la réactivation du renouvellement automatique',
      toastType: 'error'
    });
    return;
  }
});

/**
 * Envoie un email de rappel 7 jours avant le renouvellement automatique d'un abonnement premium
 */
export async function send7DaysBeforeRenewalReminder() {
  try {
    const now = new Date();
    const in7Days = new Date(now.getTime() + 7 * 24 * 60 * 60 * 1000);
    // On ne prend que la date (pas l'heure)
    const in7DaysStr = in7Days.toISOString().slice(0, 10);
    // Récupérer tous les abonnements premium actifs avec renouvellement auto et date_fin dans 7 jours
    const { data: abos, error } = await supabase
      .from('user_abo')
      .select('*')
      .eq('statut', 'actif')
      .eq('type_abonnement', 'premium')
      .eq('renouvellement_auto', true)
      .gte('date_fin', in7DaysStr + 'T00:00:00.000Z')
      .lte('date_fin', in7DaysStr + 'T23:59:59.999Z');
    if (error) {
      logger.error('Erreur lors de la récupération des abonnements pour rappel 7 jours:', error);
      return;
    }
    for (const abo of abos || []) {
      // Vérifier si un rappel a déjà été envoyé (clé Redis)
      const reminderKey = `reminder:email:abo7days:${abo.id}`;
      const alreadySent = await redis.get(reminderKey);
      if (alreadySent) continue;
      // Récupérer l'email et le nom de l'utilisateur
      const { data: user } = await supabase
        .from('users')
        .select('email')
        .eq('id', abo.user_id)
        .single();
      const { data: userProfil } = await supabase
        .from('user_profil')
        .select('nom, prenom')
        .eq('user_id', abo.user_id)
        .single();

      // Déchiffrer les données utilisateur
      const decryptedUser = user ? await decryptUserDataAsync(user) : null;

      // Déchiffrer les données sensibles du profil
      const decryptedUserProfil = userProfil ? await decryptProfilDataAsync(userProfil) : null;

      const nom = decryptedUserProfil?.nom || 'Utilisateur';
      const prenom = decryptedUserProfil?.prenom || '';
      const fullName = `${prenom} ${nom}`.trim();
      const frontendUrl = process.env.FRONTEND_URL || 'http://localhost:5173';
      if (decryptedUser && decryptedUser.email) {
        await sendSubscriptionStatusChangeEmail(decryptedUser.email, {
          userName: fullName,
          newPlan: 'premium',
          oldPlan: 'premium',
          changeType: 'renewal_reminder',
          startDate: abo.date_debut,
          endDate: abo.date_fin,
          autoRenew: true,
          price: abo.montant,
          html: `<!DOCTYPE html><html><head><meta charset='utf-8'><style>body{font-family:Arial,sans-serif;color:#333;line-height:1.6;margin:0;padding:0}.container{max-width:600px;margin:0 auto;padding:20px;border:1px solid #e5e7eb;border-radius:8px}.header{background-color:#FF7A35;color:white;padding:15px 20px;border-radius:8px 8px 0 0;margin:-20px -20px 20px}.footer{background-color:#f9fafb;padding:15px 20px;border-top:1px solid #e5e7eb;margin:20px -20px -20px;border-radius:0 0 8px 8px;font-size:12px;color:#6b7280}h1{margin:0;font-size:24px}.message-box{background-color:#fff8f3;border-left:4px solid #FF7A35;padding:15px;margin:20px 0}.button{display:inline-block;background-color:#FF7A35;color:white;padding:12px 24px;text-decoration:none;border-radius:4px;font-weight:bold;margin-top:20px}@media(max-width:600px){.container{width:100%;padding:10px}.header{padding:10px;margin:-10px -10px 15px}.footer{margin:15px -10px -10px;padding:10px}}</style></head><body><div class='container'><div class='header'><h1>Rappel : renouvellement automatique dans 7 jours</h1></div><p>Bonjour <b>${fullName}</b>,</p><div class='message-box'><p>Votre abonnement <b>Premium</b> sera automatiquement renouvelé dans 7 jours, le <b>${new Date(abo.date_fin).toLocaleDateString('fr-FR')}</b>.</p><p>Le montant de <b>${abo.montant}€</b> sera prélevé automatiquement.</p><p><strong>Rappel des avantages Premium :</strong> Lors du renouvellement, vous recevrez 20 Jobi en bonus fidélité et ${subscriptions.premium.aiCredits.included} crédits IA (si vous avez moins de ${subscriptions.premium.aiCredits.included} crédits).</p></div><p>Vous pouvez gérer votre abonnement à tout moment depuis votre espace personnel.</p><a href='${frontendUrl}/dashboard/premium' class='button'>Gérer mon abonnement</a><div class='footer'>Si vous avez des questions, notre équipe support est à votre écoute.<br>Merci de votre confiance.<br><b>L'équipe support</b></div></div></body></html>`
      });
        // Marquer comme envoyé pour éviter les doublons
        await redis.set(reminderKey, 'sent', 'EX', 8 * 24 * 60 * 60); // expire dans 8 jours
        logger.info(`Email de rappel 7 jours avant renouvellement envoyé à ${decryptedUser.email}`);
      }
    }
  } catch (error) {
    logger.error('Erreur lors de l\'envoi du rappel 7 jours avant renouvellement:', error);
  }
}

export default router;