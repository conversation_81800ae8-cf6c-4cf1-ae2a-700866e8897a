import { Router } from 'express';
import { generateMissionStep, restoreCreditsAfterError } from '../controllers/missionAssistantController';
import { authMiddleware } from '../middleware/authMiddleware';
import rateLimit from 'express-rate-limit';
import { asyncHandler } from '../utils/inputValidation';
import { checkAiConsent } from '../middleware/aiConsentMiddleware';

const router = Router();

// Rate limiter pour les requêtes de génération IA
const missionAssistantLimiter = rateLimit({
  windowMs: 60 * 1000, // 1 minute
  max: 30, // 30 requêtes maximum par minute
  message: {
    message: 'Trop de requêtes pour l\'assistant de mission. Veuillez réessayer dans 1 minute.',
    success: false,
    toastType: 'error'
  },
  standardHeaders: true,
  legacyHeaders: false
});

// Middleware d'authentification pour toutes les routes
router.use(authMiddleware.authenticateToken);

// Route pour générer une étape de mission
router.post('/generate-step', missionAssistantLimiter, checkAiConsent, asyncHandler(generateMissionStep));

// Route pour restituer des crédits en cas d'erreur de génération complète
router.post('/restore-credits', asyncHandler(restoreCreditsAfterError));

export default router;
