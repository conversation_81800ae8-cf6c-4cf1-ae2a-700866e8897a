import { Router, Request, Response } from 'express';
import { supabase } from '../config/supabase';
import logger from '../utils/logger';
import { UserController } from '../controllers/user';
import { authMiddleware } from '../middleware/authMiddleware';
import { rateLimit } from 'express-rate-limit';
import { redis } from '../config/redis';
import { validateFileUpload } from '../middleware/fileValidation';
import { featuredPhotosController } from '../controllers/featuredPhotos';
import { asyncHandler } from '../utils/inputValidation';
import { decryptProfilDataAsync, decryptUserDataAsync } from '../utils/encryption';

import { onlineStatusService } from '../services/onlineStatus';
import { dbService } from '../services/db';
import { body, validationResult } from 'express-validator';
import { uploadEntrepriseVerificationDocs, listEntrepriseVerificationDocs, validateEntrepriseVerificationDoc, forceUserVerificationStatus, getMyEntrepriseVerificationDocs } from '../controllers/user';
import { sendEntrepriseRemindInfosEmail } from '../services/emailServiceModeration';

export const userRoutes = Router();

// Initialisation du contrôleur
const userControllerInstance = new UserController();

// Rate limiter pour la mise à jour du type d'utilisateur
const updateTypeLimiter = rateLimit({
  windowMs: 1 * 30 * 1000, // 30 secondes
  max: 100, // 100 requêtes maximum par fenêtre
  message: {
    message: 'Vous avez atteint le nombre maximum de tentatives de changement du type utilisateur. Veuillez réessayer dans 1 minute.',
    success: false,
    toastType: 'error'
  },
  standardHeaders: true,
  legacyHeaders: false,
});

// Rate limiter pour la mise à jour du profil
const updateProfilLimiter = rateLimit({
  windowMs: 1 * 30 * 1000, // 30 secondes
  max: 100, // 100 requêtes maximum par fenêtre
  message: {
    message: 'Vous avez atteint le nombre maximum de tentatives de mise à jour du profil. Veuillez réessayer dans 1 minute.',
    success: false,
    toastType: 'error'
  },
  standardHeaders: true,
  legacyHeaders: false,
});

// Configuration du rate limiter pour les galeries
const galleryRateLimiter = rateLimit({
  windowMs: 1 * 30 * 1000, // 30 secondes
  max: 100, // Limite chaque IP à 100 requêtes par fenêtre
  message: 'Vous avez atteint le nombre maximum de tentatives de création de galerie. Veuillez réessayer dans quelques minutes.'
});

// Configuration du rate limiter pour les photos mises en avant
const featuredPhotoRateLimiter = rateLimit({
  windowMs: 1 * 30 * 1000, // 30 secondes
  max: 100, // Limite chaque IP à 100 requêtes par fenêtre
  message: {
    message: 'Vous avez atteint le nombre maximum de tentatives d\'upload de photos. Veuillez réessayer dans quelques minutes.',
    success: false,
    toastType: 'error'
  }
});

// Rate limiter pour le statut en ligne
const onlineStatusLimiter = rateLimit({
  windowMs: 30 * 1000, // 30 secondes
  max: 30, // 30 requêtes par fenêtre (1 toutes les 1 seconde en moyenne)
  message: {
    message: 'Trop de requêtes de mise à jour du statut',
    success: false,
    toastType: 'error'
  },
  // Ignorer les requêtes qui dépassent la limite plutôt que de renvoyer une erreur
  // Cela permet de ne pas perturber l'expérience utilisateur
  handler: (req, res, next, options) => {
    res.status(200).json({ success: true, limited: true });
  }
});

// Taux limite pour les requêtes de récupération de staff (admins et modos)
const staffRateLimit = rateLimit({
  windowMs: 5 * 60 * 1000, // 5 minutes
  max: 20, // 20 requêtes par fenêtre
  standardHeaders: true,
  legacyHeaders: false,
  message: { message: 'Trop de requêtes, veuillez réessayer plus tard', success: false, toastType: 'error' }
});

// Rate limiter pour les changements de mot de passe et email (sécurité critique)
const securityChangesLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 5, // 5 tentatives maximum par fenêtre
  message: {
    message: 'Trop de tentatives de modification de sécurité. Veuillez réessayer dans 15 minutes.',
    success: false,
    toastType: 'error'
  },
  standardHeaders: true,
  legacyHeaders: false,
});

// Rate limiter pour les suppressions de compte (très restrictif)
const accountDeletionLimiter = rateLimit({
  windowMs: 60 * 60 * 1000, // 1 heure
  max: 10, // 10 tentatives maximum par heure
  message: {
    message: 'Trop de tentatives de suppression de compte. Veuillez réessayer dans 1 heure.',
    success: false,
    toastType: 'error'
  },
  standardHeaders: true,
  legacyHeaders: false,
});

// Rate limiter pour les uploads de photos (profil et bannière)
const photoUploadLimiter = rateLimit({
  windowMs: 1 * 60 * 1000, // 1 minutes
  max: 15, // 15 uploads maximum par fenêtre
  message: {
    message: 'Trop d\'uploads de photos. Veuillez réessayer dans 1 minute.',
    success: false,
    toastType: 'error'
  },
  standardHeaders: true,
  legacyHeaders: false,
});

// Rate limiter pour les recherches d'utilisateurs
const userSearchLimiter = rateLimit({
  windowMs: 1 * 60 * 1000, // 1 minute
  max: 50, // 50 recherches par minute
  message: {
    message: 'Trop de recherches d\'utilisateurs. Veuillez réessayer dans 1 minute.',
    success: false,
    toastType: 'error'
  },
  standardHeaders: true,
  legacyHeaders: false,
});

// Rate limiter pour les préférences utilisateur
const preferencesLimiter = rateLimit({
  windowMs: 2 * 60 * 1000, // 2 minutes
  max: 30, // 30 modifications par fenêtre
  message: {
    message: 'Trop de modifications de préférences. Veuillez réessayer dans 2 minutes.',
    success: false,
    toastType: 'error'
  },
  standardHeaders: true,
  legacyHeaders: false,
});

// Rate limiter pour les actions administratives (très restrictif)
const adminActionsLimiter = rateLimit({
  windowMs: 5 * 60 * 1000, // 5 minutes
  max: 10, // 10 actions admin par fenêtre
  message: {
    message: 'Trop d\'actions administratives. Veuillez réessayer dans 5 minutes.',
    success: false,
    toastType: 'error'
  },
  standardHeaders: true,
  legacyHeaders: false,
});

// Rate limiter pour les documents de vérification
const verificationDocsLimiter = rateLimit({
  windowMs: 1 * 60 * 1000, // 1 minutes
  max: 30, // 30 actions par fenêtre
  message: {
    message: 'Trop de requêtes sur les documents de vérification. Veuillez réessayer dans 1 minute.',
    success: false,
    toastType: 'error'
  },
  standardHeaders: true,
  legacyHeaders: false,
});

// Route pour récupérer le profil de l'utilisateur connecté
userRoutes.get('/profil',
  authMiddleware.authenticateToken,
  userControllerInstance.getCurrentUser
);

// Route pour mettre à jour le type d'utilisateur (protégée par authentification et rate limiter)
userRoutes.put('/updateType',
  authMiddleware.authenticateToken,
  updateTypeLimiter,
  userControllerInstance.updateUserType
);

// Route pour mettre à jour le profil utilisateur (protégée par authentification et rate limiter)
userRoutes.put('/updateProfil',
  authMiddleware.authenticateToken,
  updateProfilLimiter,
  userControllerInstance.updateUserProfil
);

// Route pour changer le mot de passe
userRoutes.post('/change-password',
  authMiddleware.authenticateToken,
  securityChangesLimiter,
  asyncHandler(userControllerInstance.changePassword)
);

// Route pour changer l'email
userRoutes.post('/change-email',
  authMiddleware.authenticateToken,
  securityChangesLimiter,
  asyncHandler(userControllerInstance.changeEmail)
);

// Route pour demander la suppression du compte (envoi email de confirmation)
userRoutes.post('/delete/request',
  authMiddleware.authenticateToken,
  accountDeletionLimiter,
  asyncHandler(userControllerInstance.requestAccountDeletion.bind(userControllerInstance))
);

// Route pour confirmer la suppression avec re-authentification
userRoutes.post('/delete/confirm',
  accountDeletionLimiter,
  asyncHandler(userControllerInstance.confirmAccountDeletion.bind(userControllerInstance))
);

// Route pour exécuter la suppression (anonymisation) finale
userRoutes.post('/delete/execute',
  accountDeletionLimiter,
  asyncHandler(userControllerInstance.executeAccountDeletion.bind(userControllerInstance))
);

// Route pour l'upload de la photo de profil
userRoutes.post(
  '/profil/photo',
  authMiddleware.authenticateToken,
  photoUploadLimiter,
  asyncHandler(userControllerInstance.updateProfilPhoto)
);

// Route pour l'upload de la bannière de profil
userRoutes.post(
  '/profil/banner',
  authMiddleware.authenticateToken,
  photoUploadLimiter,
  asyncHandler(userControllerInstance.updateBannerPhoto)
);

// Route pour la suppression de la bannière de profil
userRoutes.delete(
  '/profil/banner',
  authMiddleware.authenticateToken,
  photoUploadLimiter,
  asyncHandler(userControllerInstance.deleteBannerPhoto)
);

// Route pour la mise à jour de l'avatar
userRoutes.put('/profil/avatar', 
  authMiddleware.authenticateToken, 
  photoUploadLimiter,
  asyncHandler(async (req: Request, res: Response) => {
  try {
    const  userId = req.body.userId;
    const  avatarUrl  = req.body.avatarUrl;
    console.log('avatarUrl : ', avatarUrl);
    console.log('userId : ', userId);

    if (!userId || !avatarUrl) {
      logger.error('Données manquantes pour la mise à jour de l\'avatar', { userId, hasAvatarUrl: !!avatarUrl });
      res.status(400).json({ error: 'Données manquantes pour la mise à jour de l\'avatar' });
      return;
    }

    // Mise à jour de l'URL de l'avatar dans la base de données
    const { data, error } = await supabase
      .from('user_profil')
      .update({
        photo_url: avatarUrl
      })
      .eq('user_id', userId);

    if (error) {
      logger.error('Erreur lors de la mise à jour de l\'avatar dans la base de données', { error, userId });
      res.status(500).json({ error: 'Erreur lors de la mise à jour de l\'avatar dans la base de données' });
      return;
    }else{
      // Supprimer le cache Redis
      const cacheKey = `user:${userId}`;
      const cacheKey_deux = `user_deux:${userId}`;
      await redis.del(cacheKey);
      await redis.del(cacheKey_deux);
      logger.info('Cache Redis 1 supprimé, updateUser :', { cacheKey });
      logger.info('Cache Redis 2 supprimé, updateUser :', { cacheKey_deux });
    }

    logger.info('Avatar mis à jour avec succès', { userId });
    res.status(200).json({ message: 'Avatar mis à jour avec succès', data });

  } catch (error) {
    logger.error('Erreur serveur lors de la mise à jour de l\'avatar', { error });
    res.status(500).json({ error: 'Erreur serveur lors de la mise à jour de l\'avatar' });
  }
}));

// Route pour récupérer la liste des avatars disponibles
userRoutes.get('/avatars', 
  authMiddleware.authenticateToken, 
  userSearchLimiter,
  asyncHandler(async (req: Request, res: Response) => {
  try {
    // Vérifier le cache Redis pour les avatars
    const cachedAvatars = await redis.get('avatars');
    if (cachedAvatars) {
      logger.info('Avatars récupérés depuis le cache Redis');
      res.status(200).json({ avatars: JSON.parse(cachedAvatars) });
      return;
    }

    // Récupérer tous les avatars avec une limite plus élevée
    const { data, error } = await supabase
      .storage
      .from('photo_profil')
      .list('avatar', {
        limit: 1000,
        sortBy: { column: 'name', order: 'asc' }
      });

    if (error) {
      logger.error('Erreur lors de la récupération des avatars', { error });
      res.status(500).json({ error: 'Erreur lors de la récupération des avatars' });
      return;
    }

    if (!data || data.length === 0) {
      res.status(200).json({ avatars: [] });
      return;
    }

    const avatarUrls = data.map(file => {
      const { data: { publicUrl } } = supabase
        .storage
        .from('photo_profil')
        .getPublicUrl(`avatar/${file.name}`);
      return publicUrl;
    });

    // Mettre en cache les avatars dans Redis
    await redis.set('avatars', JSON.stringify(avatarUrls), 'EX', 7200); // Expiration de 2 heures

    logger.info(`Nombre d'avatars récupérés: ${avatarUrls.length}`);
    res.status(200).json({ avatars: avatarUrls });
  } catch (error) {
    logger.error('Erreur lors de la récupération des avatars', { error });
    res.status(500).json({ error: 'Erreur serveur' });
  }
}));

// Route pour créer une galerie
userRoutes.post('/gallery',
  authMiddleware.authenticateToken,
  validateFileUpload,
  galleryRateLimiter,
  asyncHandler(userControllerInstance.createGallery)
);

// Route pour mettre à jour une galerie
userRoutes.put('/gallery/:id',
  authMiddleware.authenticateToken,
  validateFileUpload,
  galleryRateLimiter,
  asyncHandler(userControllerInstance.updateGallery)
);

// Route pour supprimer une galerie
userRoutes.delete('/gallery/:id',
  authMiddleware.authenticateToken,
  validateFileUpload,
  galleryRateLimiter,
  asyncHandler(userControllerInstance.deleteGallery)
);

// Route pour activer/désactiver une galerie
userRoutes.patch('/gallery/:id/status',
  authMiddleware.authenticateToken,
  galleryRateLimiter,
  asyncHandler(userControllerInstance.toggleGalleryStatus)
);

// Route pour récupérer les galeries d'un utilisateur
userRoutes.get('/galleries',
  authMiddleware.authenticateToken,
  galleryRateLimiter,
  asyncHandler(userControllerInstance.getGalleries)
);

// Route pour récupérer les galeries d'un utilisateur spécifique
userRoutes.get('/galleries/:slug', 
  userSearchLimiter,
  asyncHandler(async (req: Request, res: Response) => {
  try {
    const { slug } = req.params;

    // Vérifier si les galeries sont dans le cache
    const cacheKey = `user_gallery_user_specific:${slug}`;
    const cachedGalleries = await redis.get(cacheKey);
    if (cachedGalleries) {
      logger.info('🚀 Récupération des galeries d\'un utilisateur spécifique depuis le cache :', cachedGalleries);
      res.json(JSON.parse(cachedGalleries));
      return;
    }

    // D'abord, récupérer l'ID de l'utilisateur à partir du slug
    const { data: userData, error: userError } = await supabase
      .from('user_profil')
      .select('user_id')
      .eq('slug', slug)
      .single();
    if (userError || !userData) {
      res.status(404).json({ error: 'Utilisateur non trouvé' });
      return;
    }

    // Vérifier si l'utilisateur connecté consulte son propre profil
    const isOwnProfile = req.user && req.user.userId === userData.user_id;

    // Récupérer les galeries de l'utilisateur depuis Supabase avec le comptage des photos
    // Si c'est le profil de l'utilisateur connecté, récupérer toutes les galeries (actives et inactives)
    // Sinon, récupérer uniquement les galeries actives
    let query = supabase
      .from('user_gallery')
      .select(`
        *,
        photos:user_gallery_photos (*)
      `)
      .eq('user_id', userData.user_id)
      .order('created_at', { ascending: false });

    // Filtrer par statut actif uniquement si ce n'est pas le profil de l'utilisateur connecté
    if (!isOwnProfile) {
      query = query.eq('status', 'actif');
    }

    const { data: galleries, error } = await query;

    if (error) throw error;

    // Transformer les données pour inclure le comptage des photos
    const galleriesWithCount = galleries?.map(gallery => ({
      id: gallery.id,
      name: gallery.name,
      description: gallery.description,
      cover_image: gallery.cover_image,
      createdAt: gallery.created_at,
      imagesCount: Array.isArray(gallery.photos) ? gallery.photos.length : 0,
      photos: gallery.photos
    })) || [];
    await redis.set(cacheKey, JSON.stringify(galleriesWithCount), 'EX', 600);
    logger.info('🚀 Galeries récupérées avec succès et mises en cache');

    res.json(galleriesWithCount);
  } catch (error) {
    logger.error('Erreur lors de la récupération des galeries:', error);
    res.status(500).json({ message: 'Erreur lors de la récupération des galeries' });
  }
}));

// Route pour ajouter plusieurs photos à une galerie (upload multiple)
userRoutes.post('/gallery/:id/photos',
  authMiddleware.authenticateToken,
  validateFileUpload,
  galleryRateLimiter,
  asyncHandler(userControllerInstance.addPhotosToGallery)
);

// Route pour ajouter une photo à une galerie (upload unique - compatibilité)
userRoutes.post('/gallery/:id/photo',
  authMiddleware.authenticateToken,
  validateFileUpload,
  galleryRateLimiter,
  asyncHandler(userControllerInstance.addPhotoToGallery)
);

// Route pour mettre à jour une photo dans une galerie
userRoutes.put('/gallery/:id/photo/:photoId',
  authMiddleware.authenticateToken,
  galleryRateLimiter,
  asyncHandler(userControllerInstance.updatePhotoInGallery)
);

// Route pour supprimer une photo d'une galerie
userRoutes.delete('/gallery/:id/photo/:photoId',
  authMiddleware.authenticateToken,
  galleryRateLimiter,
  asyncHandler(userControllerInstance.deletePhotoFromGallery)
);

// Route pour récupérer les photos d'une galerie
userRoutes.get('/gallery/:id/photos',
  authMiddleware.authenticateToken,
  galleryRateLimiter,
  asyncHandler(userControllerInstance.getPhotosFromGallery)
);

// Routes pour les photos mises en avant
userRoutes.post('/featured-photos',
  authMiddleware.authenticateToken,
  featuredPhotoRateLimiter,
  validateFileUpload,
  asyncHandler(featuredPhotosController.uploadFeaturedPhoto)
);

// Route pour récupérer les photos mises en avant de l'utilisateur connecté (moi)
userRoutes.get('/featured-photos',
  authMiddleware.authenticateToken,
  userSearchLimiter,
  asyncHandler(featuredPhotosController.getFeaturedPhotos)
);

// Route pour récupérer les photos mises en avant d'un utilisateur spécifique
userRoutes.get('/featured-photos/:slug', 
  userSearchLimiter,
  asyncHandler(async (req: Request, res: Response): Promise<void> => {
  try {
    const { slug } = req.params;

    // Vérifier si les photos mises en avant sont dans le cache
    const cacheKey = `user_featured_photos_user_specific:${slug}`;
    const cachedPhotos = await redis.get(cacheKey);

    if (cachedPhotos) {
      logger.info('🚀 Photos mises en avant récupérées avec succès depuis le cache :', cachedPhotos);
      res.json(JSON.parse(cachedPhotos));
      return;
    }

    // D'abord, récupérer l'ID de l'utilisateur à partir du slug
    const { data: userData, error: userError } = await supabase
      .from('user_profil')
      .select('user_id')
      .eq('slug', slug)
      .single();

    if (userError || !userData) {
      res.status(404).json({ error: 'Utilisateur non trouvé' });
      return;
    }

    // Récupérer les photos mises en avant de l'utilisateur depuis Supabase
    const { data: photos, error } = await supabase
      .from('user_featured_photos')
      .select(`
        id,
        photo_url,
        caption,
        created_at
      `)
      .eq('user_id', userData.user_id)
      .order('created_at', { ascending: false });

    if (error) throw error;

    // Mettre en cache les photos dans Redis
    await redis.set(cacheKey, JSON.stringify(photos), 'EX', 600); // Cache pour 10 minutes
    logger.info('🚀 Photos mises en avant récupérées avec succès et mises en cache :', photos);

    res.json({
      success: true,
      photos: photos || []
    });
  } catch (error) {
    logger.error('Erreur lors de la récupération des photos mises en avant:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur lors de la récupération des photos mises en avant'
    });
  }
}));

userRoutes.delete('/featured-photos/:photoId',
  authMiddleware.authenticateToken,
  featuredPhotoRateLimiter,
  asyncHandler(featuredPhotosController.deleteFeaturedPhoto)
);

userRoutes.put('/featured-photos/:photoId/caption',
  authMiddleware.authenticateToken,
  featuredPhotoRateLimiter,
  asyncHandler(featuredPhotosController.updateFeaturedPhotoCaption)
);

// Route pour récupérer le profil public d'un utilisateur
userRoutes.get('/profil/:slug', 
  userSearchLimiter,
  asyncHandler(async (req: Request, res: Response): Promise<void> => {
  try {
    const { slug } = req.params;

    // Vérifier si le profil est dans le cache
    const cacheKey = `user_profil_public:${slug}`;
    const cachedProfil = await redis.get(cacheKey);

    if (cachedProfil) {
      logger.info('🚀 Profil récupéré avec succès depuis le cache :', cachedProfil);
      res.json(JSON.parse(cachedProfil));
      return;
    }

    // Récupérer l'ID de l'utilisateur à partir du slug (sans filtrer sur profil_visible)
    const { data: userProfil, error: userProfilError } = await supabase
      .from('user_profil')
      .select('user_id, profil_visible')
      .eq('slug', slug)
      .single();

    if (userProfilError || !userProfil) {
      res.status(404).json({ message: 'Utilisateur non trouvé' });
      return;
    }

    if (!userProfil.profil_visible) {
      res.status(200).json({ masqué: true, message: 'Ce profil est en cours de modération' });
      return;
    }

    const userId = userProfil.user_id;

    // Vérifier et mettre à jour le statut en ligne
    const isOnline = await onlineStatusService.verifyAndUpdateOnlineStatus(userId);

    // Récupérer les données de l'utilisateur depuis Supabase
    const { data: userData, error } = await supabase
      .from('users')
      .select(`
        id,
        profil_verifier,
        identite_verifier,
        entreprise_verifier,
        assurance_verifier,
        is_online,
        email_verifier,
        profil_actif,
        user_type,
        date_inscription,
        role,
        profil:user_profil!inner (id, user_id, nom, prenom, telephone, telephone_prive, numero, adresse, ville, code_postal, pays, bio, photo_url, banner_url, banner_position, banner_position_offset, mode_vacance, intervention_zone, type_de_profil, nom_entreprise, prenom_entreprise, statut_entreprise, siren_entreprise, code_ape_entreprise, categorie_entreprise, effectif_entreprise, date_insee_creation_entreprise, date_categorie_entreprise, date_derniere_mise_a_jour_entreprise_insee, date_derniere_mise_a_jour_du_client_entreprise, slug, profil_visible, date_validation_document_identite, date_validation_document_entreprise, date_validation_document_assurance, slogan, created_at, updated_at)
      `)
      .eq('id', userId)
      .single();

    if (error) throw error;
    if (!userData) {
      res.status(404).json({ message: 'Utilisateur non trouvé' });
      return;
    }

    // Déchiffrer les données sensibles du profil
    const decryptedProfil = userData.profil ? (Array.isArray(userData.profil) ? 
      await Promise.all(userData.profil.map(async (p: any) => await decryptProfilDataAsync(p))) : 
      await decryptProfilDataAsync(userData.profil)) : null;

    // Vérifier si l'utilisateur est premium
    const { data: subscription, error: subscriptionError } = await supabase
      .from('user_abo')
      .select('type_abonnement, statut')
      .eq('user_id', userId)
      .eq('statut', 'actif')
      .order('date_debut', { ascending: false })
      .limit(1)
      .single();

    const isPremium = subscription && subscription.type_abonnement !== 'gratuit' && subscription.statut === 'actif';

    // Récupérer les services de l'utilisateur demandé
    const { data: services, error: servicesError } = await supabase
      .from('user_services')
      .select(`
        id,
        category_id,
        subcategory_id,
        titre,
        description,
        tarif_horaire,
        horaires,
        statut,
        created_at,
        updated_at
      `)
      .eq('user_id', userId)
      .eq('statut', 'actif');

    if (servicesError) {
      logger.error('Erreur lors de la récupération des services:', servicesError);
      res.status(500).json({ message: 'Erreur lors de la récupération des services' });
      return;
    }

    // Récupérer les galeries actives de l'utilisateur demandé
    const { data: galleries, error: galleriesError } = await supabase
      .from('user_gallery')
      .select(`
        id,
        name,
        description,
        cover_image,
        photos:user_gallery_photos (
          id,
          photo_url,
          caption,
          order_index
        )
      `)
      .eq('user_id', userId)
      .eq('status', 'actif');

    if (galleriesError) throw galleriesError;

    // Récupérer les photos mises en avant de l'utilisateur demandé
    const { data: featuredPhotos, error: featuredError } = await supabase
      .from('user_featured_photos')
      .select(`
        id,
        photo_url,
        caption,
        created_at
      `)
      .eq('user_id', userId)
      .order('created_at', { ascending: false });

    if (featuredError) throw featuredError;

    // Transformation des données pour correspondre au format attendu
    const formattedData = {
      success: true,
      id: userData.id,
      profil_verifier: userData.profil_verifier,
      identite_verifier: userData.identite_verifier,
      entreprise_verifier: userData.entreprise_verifier,
      assurance_verifier: userData.assurance_verifier,
      is_online: isOnline, // Utiliser le statut vérifié
      email_verifier: userData.email_verifier,
      profil_actif: userData.profil_actif,
      user_type: userData.user_type,
      date_inscription: userData.date_inscription,
      role: userData.role,
      profil: {
        data: {
          ...(Array.isArray(decryptedProfil) ? decryptedProfil[0] : decryptedProfil),
          nom: (Array.isArray(decryptedProfil) ? decryptedProfil[0] : decryptedProfil)?.nom?.[0]?.toUpperCase() + '.' || null,
          // Ne retourner que la ville et le pays pour l'adresse publique
          numero: undefined,
          adresse: undefined,
          code_postal: undefined,
          // Garder uniquement ville et pays (utiliser les données décryptées)
          ville: (Array.isArray(decryptedProfil) ? decryptedProfil[0] : decryptedProfil)?.ville,
          pays: (Array.isArray(decryptedProfil) ? decryptedProfil[0] : decryptedProfil)?.pays,
          telephone: (Array.isArray(decryptedProfil) ? decryptedProfil[0] : decryptedProfil)?.telephone_prive ? "Numéro Masqué" : (Array.isArray(decryptedProfil) ? decryptedProfil[0] : decryptedProfil)?.telephone,
          telephone_prive: (Array.isArray(decryptedProfil) ? decryptedProfil[0] : decryptedProfil)?.telephone_prive,
          intervention_zone: {
            center: (Array.isArray(decryptedProfil) ? decryptedProfil[0] : decryptedProfil)?.intervention_zone?.center,
            radius: (Array.isArray(decryptedProfil) ? decryptedProfil[0] : decryptedProfil)?.intervention_zone?.radius,
            france_entiere: (Array.isArray(decryptedProfil) ? decryptedProfil[0] : decryptedProfil)?.intervention_zone?.france_entiere
          }
        }
      },
      services: services || [],
      galleryFolders: galleries || [],
      featuredPhotos: featuredPhotos || [],
      isPremium: isPremium // Ajouter le statut premium ici
    };

    // Mettre en cache le profil dans Redis
    await redis.set(cacheKey, JSON.stringify(formattedData), 'EX', 300);
    // logger.info('🚀 Profil récupéré avec succès et mises en cache :', formattedData);

    // logger.info('Données formatées pour le profil public:', formattedData);
    res.json(formattedData);
  } catch (error) {
    logger.error('Erreur lors de la récupération du profil public:', error);
    res.status(500).json({ message: 'Erreur serveur lors de la récupération du profil' });
  }
}));

// Route pour mettre à jour l'activité de l'utilisateur (savoir si il est en ligne ou non)
userRoutes.post('/activity',
  authMiddleware.authenticateToken,
  onlineStatusLimiter,
  asyncHandler(async (req: Request, res: Response): Promise<void> => {
    try {
      const userId = req.user?.userId;
      if (!userId) {
        res.status(401).json({ message: 'Non autorisé' });
        return;
      }
      // logger.info('Demande de mise à jour de l\'activité de l\'utilisateur:', userId);

      // Log moins fréquent pour les demandes de mise à jour
      const result = await onlineStatusService.updateUserActivity(userId);

      if (result.limited) {
        // Si la mise à jour a été limitée (car trop récente), on renvoie quand même un succès
        // pour ne pas perturber l'expérience utilisateur
        res.status(200).json({ success: true, limited: true });
        return;
      }

      if (!result.success) {
        res.status(500).json({
          message: 'Erreur lors de la mise à jour de l\'activité',
          success: false
        });
        return;
      }

      res.status(200).json({ success: true });
    } catch (error) {
      logger.error('Erreur lors de la mise à jour de l\'activité:', error);
      res.status(500).json({ message: 'Erreur serveur' });
    }
  })
);

// Route pour vérifier le statut en ligne d'un utilisateur
userRoutes.get('/online-status/:userId', 
  userSearchLimiter,
  asyncHandler(async (req: Request, res: Response): Promise<void> => {
  try {
    const { userId } = req.params;
    logger.info('Vérification du statut en ligne pour l\'utilisateur:', userId);
    const isOnline = await onlineStatusService.checkUserOnlineStatus(userId);
    res.status(200).json({ isOnline });
  } catch (error) {
    logger.error('Erreur lors de la vérification du statut en ligne:', error);
    res.status(500).json({ message: 'Erreur serveur' });
  }
}));

// Route pour récupérer le slug d'un utilisateur à partir de son ID
userRoutes.get('/get-slug/:userId', 
  userSearchLimiter,
  asyncHandler(async (req: Request, res: Response): Promise<void> => {
  try {
    const { userId } = req.params;
    const cacheKey = `user_slug:${userId}`;
    const cachedSlug = await redis.get(cacheKey);

    if (cachedSlug) {
      logger.info('🚀 Slug récupéré avec succès depuis le cache :', cachedSlug);
      res.json({
        success: true,
        slug: JSON.parse(cachedSlug)
      });
      return;
    }

    // Récupérer le slug depuis la table user_profil
    const { data: userProfil, error } = await supabase
      .from('user_profil')
      .select('slug')
      .eq('user_id', userId)
      .single();

    if (error) throw error;

    if (!userProfil) {
      res.status(404).json({
        success: false,
        message: 'Utilisateur non trouvé'
      });
      return;
    }

    // Mettre en cache le slug dans Redis
    await redis.set(cacheKey, JSON.stringify(userProfil.slug), 'EX', 1800); // Cache pour 30 minutes
    logger.info('🚀 Slug récupéré avec succès et mises en cache :', userProfil.slug);

    res.json({
      success: true,
      slug: userProfil.slug
    });

  } catch (error) {
    logger.error('Erreur lors de la récupération du slug:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur lors de la récupération du slug'
    });
  }
}));

// Route pour récupérer les informations du profil utilisateur et ses services pour générer un pré-message quand on fait une proposition de mission (offre de mission)
userRoutes.get('/pre-message-info',
  authMiddleware.authenticateToken,
  userSearchLimiter,
  asyncHandler(async (req: Request, res: Response): Promise<void> => {
    try {
      const userId = req.user?.userId;
      if (!userId) {
        res.status(401).json({ message: 'Non autorisé' });
      }

      logger.info('Récupération des informations pour le pré-message de l\'utilisateur:', userId);

      // Vérifier le cache Redis
      const cacheKey = `pre_message_info:${userId}`;
      const cachedData = await redis.get(cacheKey);

      if (cachedData) {
        logger.info('Informations pré-message récupérées depuis le cache Redis');
        res.json(JSON.parse(cachedData));
        return;
      }

      // Récupérer les données de l'utilisateur depuis Supabase
      const { data: userData, error: userError } = await supabase
        .from('users')
        .select(`
          id,
          profil_verifier,
          identite_verifier,
          entreprise_verifier,
          assurance_verifier,
          user_type,
          profil:user_profil (
            nom,
            prenom,
            bio,
            type_de_profil,
            nom_entreprise,
            statut_entreprise
          )
        `)
        .eq('id', userId)
        .single();

      if (userError) {
        logger.error('Erreur lors de la récupération des données utilisateur:', userError);
        res.status(500).json({ message: 'Erreur lors de la récupération des données utilisateur' });
        return;
      }

      // Déchiffrer les données sensibles du profil
      if (userData.profil && userData.profil.length > 0) {
        userData.profil = await Promise.all(userData.profil.map(async (profil: any) => await decryptProfilDataAsync(profil)));
      }

      // Récupérer les services actifs de l'utilisateur
      const { data: services, error: servicesError } = await supabase
        .from('user_services')
        .select(`
          id,
          titre,
          category_id,
          subcategory_id
        `)
        .eq('user_id', userId)
        .eq('statut', 'actif');

      if (servicesError) {
        logger.error('Erreur lors de la récupération des services:', servicesError);
        res.status(500).json({ message: 'Erreur lors de la récupération des services' });
        return;
      }

      // Extraire les données du profil (qui peut être un tableau ou un objet)
      const profilData = Array.isArray(userData.profil) ? userData.profil[0] : userData.profil;

      // Déchiffrer les données de profil
      const decryptedProfilData = profilData ? await decryptProfilDataAsync(profilData) : null;

      // Formater les données pour le frontend
      const preMessageInfo = {
        profil: {
          nom: decryptedProfilData?.nom || '',
          prenom: decryptedProfilData?.prenom || '',
          bio: decryptedProfilData?.bio || '',
          type_de_profil: decryptedProfilData?.type_de_profil || '',
          nom_entreprise: decryptedProfilData?.nom_entreprise || '',
          statut_entreprise: decryptedProfilData?.statut_entreprise || ''
        },
        user_type: userData.user_type,
        services: services || [],
        verifications: {
          profil_verifier: userData.profil_verifier,
          identite_verifier: userData.identite_verifier,
          entreprise_verifier: userData.entreprise_verifier,
          assurance_verifier: userData.assurance_verifier
        }
      };

      // Mettre en cache dans Redis (expiration de 1 heure)
      await redis.set(cacheKey, JSON.stringify(preMessageInfo), 'EX', 300);

      res.json(preMessageInfo);
    } catch (error) {
      logger.error('Erreur lors de la récupération des informations pour le pré-message:', error);
      res.status(500).json({ message: 'Erreur serveur' });
    }
  })
);

// Récupération des utilisateurs administrateurs et modérateurs
userRoutes.get('/staff', 
  authMiddleware.authenticateToken, 
  staffRateLimit, 
  asyncHandler(userControllerInstance.getStaffUsers)
);

// Route pour récupérer les utilisateurs connus (favoris, offres reçues/envoyées)
userRoutes.get('/recup_users_connus_jobi', 
  authMiddleware.authenticateToken, 
  userSearchLimiter,
  asyncHandler(async (req: Request, res: Response) => {
  try {
    const userId = req.user?.userId;
    if (!userId) {
      res.status(401).json({
        success: false,
        error: 'Non autorisé'
      });
    }

    // Vérifier le cache Redis
    const cacheKey = `users_connus:${userId}`;
    const cachedData = await redis.get(cacheKey);

    if (cachedData) {
      logger.info('Utilisateurs connus récupérés depuis le cache Redis');
      return res.json(JSON.parse(cachedData));
    }

    // Récupérer les favoris
    const { data: favoris, error: favorisError } = await dbService.supabase
      .from('user_favorites')
      .select(`
        id,
        favorite_user_id
      `)
      .eq('user_id', userId);

    if (favorisError) {
      logger.error('Erreur lors de la récupération des favoris:', favorisError);
      // Si la table user_favorites n'existe pas, on retourne un tableau vide
      if (favorisError.code === '42P01') {
        return res.json({
          success: true,
          users: []
        });
      }
      throw favorisError;
    }

    // Récupérer les informations détaillées des favoris
    let favorisDetails: any[] = [];
    if (favoris && favoris.length > 0) {
      const favorisIds = favoris.map((f: { favorite_user_id: string }) => f.favorite_user_id);

      // Récupérer les profils des favoris
      const { data: favorisProfiles, error: favorisProfilesError } = await dbService.supabase
        .from('user_profil')
        .select('user_id, slug, photo_url, nom, prenom')
        .in('user_id', favorisIds);

      if (favorisProfilesError) {
        logger.error('Erreur lors de la récupération des profils des favoris:', favorisProfilesError);
      } else if (favorisProfiles) {
        favorisDetails = await Promise.all(favorisProfiles.map(async (profile: { user_id: string, slug: string, photo_url: string | null, nom: string | null, prenom: string | null }) => {
          // Déchiffrer les données de profil
          const decryptedProfile = await decryptProfilDataAsync(profile);
          return {
            id: profile.user_id,
            usertag: profile.slug || 'utilisateur',
            avatar_url: profile.photo_url,
            type: 'favori' as const,
            nom: decryptedProfile.nom || '',
            prenom: decryptedProfile.prenom || ''
          };
        }));
      }
    }

    // Initialiser les tableaux pour stocker les résultats
    let jobbeursData: any[] = [];
    let proprietairesData: any[] = [];

    try {
      // Récupérer les IDs des missions de l'utilisateur
      const { data: missionsData } = await dbService.supabase
        .from('user_missions')
        .select('id')
        .eq('user_id', userId);

      const missionIds = missionsData?.map((m: { id: string | number }) => m.id) || [];

      // Récupérer les jobbeurs qui ont fait des propositions sur ces missions
      if (missionIds.length > 0) {
        const { data: propositionsData } = await dbService.supabase
          .from('user_mission_candidature')
          .select('jobbeur_id')
          .in('mission_id', missionIds);

        const jobbeurIds = [...new Set(propositionsData?.map((p: { jobbeur_id: string | number }) => p.jobbeur_id) || [])];

        // Récupérer les profils des jobbeurs
        if (jobbeurIds.length > 0) {
          const { data: jobbeurProfiles } = await dbService.supabase
            .from('user_profil')
            .select('user_id, slug, photo_url, nom, prenom')
            .in('user_id', jobbeurIds);

          jobbeursData = await Promise.all(jobbeurProfiles?.map(async (profile: { user_id: string, slug: string, photo_url: string | null, nom: string | null, prenom: string | null }) => {
            // Déchiffrer les données de profil
            const decryptedProfile = await decryptProfilDataAsync(profile);
            return {
              id: profile.user_id,
              usertag: profile.slug || 'utilisateur',
              avatar_url: profile.photo_url,
              type: 'offre_recue' as const,
              nom: decryptedProfile.nom || '',
              prenom: decryptedProfile.prenom || ''
            };
          }) || []);
        }
      }

      // Récupérer les missions auxquelles l'utilisateur a postulé
      const { data: postulationsData } = await dbService.supabase
        .from('user_mission_candidature')
        .select('mission_id')
        .eq('jobbeur_id', userId);

      const postulationMissionIds = [...new Set(postulationsData?.map((p: { mission_id: string | number }) => p.mission_id) || [])];

      // Récupérer les propriétaires de ces missions
      if (postulationMissionIds.length > 0) {
        const { data: missionsProprietairesData } = await dbService.supabase
          .from('user_missions')
          .select(`
            id,
            user_id
          `)
          .in('id', postulationMissionIds);

        const proprietaireIds = [...new Set(missionsProprietairesData?.map((m: { user_id: string | number }) => m.user_id) || [])];

        // Récupérer les profils des propriétaires
        if (proprietaireIds.length > 0) {
          const { data: proprietaireProfiles } = await dbService.supabase
            .from('user_profil')
            .select('user_id, slug, photo_url, nom, prenom')
            .in('user_id', proprietaireIds);

          proprietairesData = await Promise.all(proprietaireProfiles?.map(async (profile: { user_id: string, slug: string, photo_url: string | null, nom: string | null, prenom: string | null }) => {
            // Déchiffrer les données de profil
            const decryptedProfile = await decryptProfilDataAsync(profile);
            return {
              id: profile.user_id,
              usertag: profile.slug || 'utilisateur',
              avatar_url: profile.photo_url,
              type: 'offre_envoyee' as const,
              nom: decryptedProfile.nom || '',
              prenom: decryptedProfile.prenom || ''
            };
          }) || []);
        }
      }
    } catch (error: any) {
      // Si l'erreur est due à une table manquante, on continue avec les données disponibles
      if (error.code !== '42P01') {
        throw error;
      }
      logger.warn('Table mission_propositions ou missions non disponible, continuons avec les favoris uniquement');
    }

    // Log des données avant formatage
    logger.info('Favoris après formatage:', favorisDetails);
    logger.info('Jobbeurs après formatage:', jobbeursData);
    logger.info('Propriétaires après formatage:', proprietairesData);

    // Combiner tous les utilisateurs
    const users = [
      ...favorisDetails,
      ...jobbeursData,
      ...proprietairesData
    ];

    // Supprimer les doublons en gardant la première occurrence
    const uniqueUsers = users.filter((user, index, self) =>
      index === self.findIndex((u) => u.id === user.id)
    );

    // Log final des données formatées
    logger.info('Utilisateurs formatés:', uniqueUsers);

    const response = {
      success: true,
      users: uniqueUsers
    };

    // Mettre en cache pour 5 minutes
    await redis.setex(cacheKey, 300, JSON.stringify(response));

    res.json(response);

  } catch (error) {
    logger.error('Erreur lors de la récupération des utilisateurs connus:', error);
    res.status(500).json({
      success: false,
      error: 'Une erreur est survenue lors de la récupération des utilisateurs'
    });
  }
}));

// Route pour rechercher des utilisateurs
userRoutes.get('/search', 
  authMiddleware.authenticateToken, 
  userSearchLimiter,
  async (req, res) => {
  try {
    const userId = req.user?.userId;
    const query = req.query.query as string;
    const searchById = req.query.searchById === 'true';

    if (!userId || !query) {
      res.status(400).json({
        success: false,
        error: 'Paramètres manquants'
      });
      return;
    }

    // Si on recherche par ID spécifiquement, faire une recherche directe
    if (searchById || /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(query)) {
      logger.info('Recherche directe par ID utilisateur:', query);

      // Rechercher directement par ID utilisateur
      const { data: userProfile, error: profileError } = await dbService.supabase
        .from('user_profil')
        .select('user_id, slug, photo_url, nom, prenom')
        .eq('user_id', query)
        .single();

      if (!profileError && userProfile) {
        // Déchiffrer les données de profil
        const decryptedProfile = await decryptProfilDataAsync(userProfile);
        const directResult = {
          id: userProfile.user_id,
          usertag: userProfile.slug || 'utilisateur',
          avatar_url: userProfile.photo_url,
          type: 'direct_search' as const,
          nom: decryptedProfile.nom || '',
          prenom: decryptedProfile.prenom || ''
        };

        const response = {
          success: true,
          users: [directResult]
        };

        logger.info('Utilisateur trouvé par recherche directe:', directResult);
        res.json(response);
        return;
      } else {
        logger.info('Aucun utilisateur trouvé pour l\'ID:', query);
        res.json({
          success: true,
          users: []
        });
        return;
      }
    }

    // Vérifier le cache Redis pour les recherches normales
    const cacheKey = `user_search:${userId}:${query}`;
    const cachedResults = await redis.get(cacheKey);

    if (cachedResults) {
      logger.info('Résultats de recherche récupérés depuis le cache Redis');
      res.json(JSON.parse(cachedResults));
      return;
    }

    // Récupérer les favoris
    const { data: favoris, error: favorisError } = await dbService.supabase
      .from('user_favorites')
      .select(`
        id,
        favorite_user_id
      `)
      .eq('user_id', userId);

    if (favorisError && favorisError.code !== '42P01') {
      throw favorisError;
    }

    // Récupérer les informations détaillées des favoris
    let favorisDetails: any[] = [];
    if (favoris && favoris.length > 0) {
      const favorisIds = favoris.map((f: { favorite_user_id: string }) => f.favorite_user_id);

      const { data: favorisProfiles, error: favorisProfilesError } = await dbService.supabase
        .from('user_profil')
        .select('user_id, slug, photo_url, nom, prenom')
        .in('user_id', favorisIds);

      if (!favorisProfilesError && favorisProfiles) {
        favorisDetails = await Promise.all(favorisProfiles.map(async (profile: { user_id: string, slug: string, photo_url: string | null, nom: string | null, prenom: string | null }) => {
          // Déchiffrer les données de profil
          const decryptedProfile = await decryptProfilDataAsync(profile);
          return {
            id: profile.user_id,
            usertag: profile.slug || 'utilisateur',
            avatar_url: profile.photo_url,
            type: 'favori' as const,
            nom: decryptedProfile.nom || '',
            prenom: decryptedProfile.prenom || ''
          };
        }));
      }
    }

    // Initialiser les tableaux pour stocker les résultats
    let jobbeursData: any[] = [];
    let proprietairesData: any[] = [];

    // Récupérer les IDs des missions de l'utilisateur
    const { data: missionsData } = await dbService.supabase
      .from('user_missions')
      .select('id')
      .eq('user_id', userId);

    const missionIds = missionsData?.map((m: { id: string | number }) => m.id) || [];

    // Récupérer les jobbeurs qui ont fait des propositions sur ces missions
    if (missionIds.length > 0) {
      const { data: propositionsData } = await dbService.supabase
        .from('user_mission_candidature')
        .select('jobbeur_id')
        .in('mission_id', missionIds);

      const jobbeurIds = [...new Set(propositionsData?.map((p: { jobbeur_id: string | number }) => p.jobbeur_id) || [])];

      if (jobbeurIds.length > 0) {
        const { data: jobbeurProfiles } = await dbService.supabase
          .from('user_profil')
          .select('user_id, slug, photo_url, nom, prenom')
          .in('user_id', jobbeurIds);

        jobbeursData = await Promise.all(jobbeurProfiles?.map(async (profile: { user_id: string, slug: string, photo_url: string | null, nom: string | null, prenom: string | null }) => {
          // Déchiffrer les données de profil
          const decryptedProfile = await decryptProfilDataAsync(profile);
          return {
            id: profile.user_id,
            usertag: profile.slug || 'utilisateur',
            avatar_url: profile.photo_url,
            type: 'offre_recue' as const,
            nom: decryptedProfile.nom || '',
            prenom: decryptedProfile.prenom || ''
          };
        }) || []);
      }
    }

    // Récupérer les missions auxquelles l'utilisateur a postulé
    const { data: postulationsData } = await dbService.supabase
      .from('user_mission_candidature')
      .select('mission_id')
      .eq('jobbeur_id', userId);

    const postulationMissionIds = [...new Set(postulationsData?.map((p: { mission_id: string | number }) => p.mission_id) || [])];

    if (postulationMissionIds.length > 0) {
      const { data: missionsProprietairesData } = await dbService.supabase
        .from('user_missions')
        .select(`
          id,
          user_id
        `)
        .in('id', postulationMissionIds);

      const proprietaireIds = [...new Set(missionsProprietairesData?.map((m: { user_id: string | number }) => m.user_id) || [])];

      if (proprietaireIds.length > 0) {
        const { data: proprietaireProfiles } = await dbService.supabase
          .from('user_profil')
          .select('user_id, slug, photo_url, nom, prenom')
          .in('user_id', proprietaireIds);

        proprietairesData = await Promise.all(proprietaireProfiles?.map(async (profile: { user_id: string, slug: string, photo_url: string | null, nom: string | null, prenom: string | null }) => {
          // Déchiffrer les données de profil
          const decryptedProfile = await decryptProfilDataAsync(profile);
          return {
            id: profile.user_id,
            usertag: profile.slug || 'utilisateur',
            avatar_url: profile.photo_url,
            type: 'offre_envoyee' as const,
            nom: decryptedProfile.nom || '',
            prenom: decryptedProfile.prenom || ''
          };
        }) || []);
      }
    }

    // Récupérer les utilisateurs avec qui on a échangé des Jobi
    const { data: jobiHistorique } = await dbService.supabase
      .from('jobi_historique')
      .select('*')
      .order('date_creation', { ascending: false });

    let jobiUsers: any[] = [];
    if (jobiHistorique) {
      const userTags = new Set<string>();
      jobiHistorique.forEach((transaction: any) => {
        const description = transaction.description;
        const tagMatch = description.match(/@([^\s)]+)/);
        if (tagMatch && tagMatch[1]) {
          userTags.add(tagMatch[1]);
        }
      });

      if (userTags.size > 0) {
        const { data: jobiProfiles } = await dbService.supabase
          .from('user_profil')
          .select('user_id, slug, photo_url, nom, prenom')
          .in('slug', Array.from(userTags));

        if (jobiProfiles) {
          jobiUsers = await Promise.all(jobiProfiles.map(async (profile: any) => {
            // Déchiffrer les données de profil
            const decryptedProfile = await decryptProfilDataAsync(profile);

            const lastTransaction = jobiHistorique.find((t: any) =>
              t.description.includes(`@${profile.slug}`)
            );

            return {
              id: profile.user_id,
              usertag: profile.slug,
              avatar_url: profile.photo_url,
              type: 'jobi_echange' as const,
              nom: decryptedProfile.nom || '',
              prenom: decryptedProfile.prenom || '',
              dernierEchange: lastTransaction ? {
                montant: Math.abs(lastTransaction.montant),
                date: lastTransaction.date_creation,
                type: lastTransaction.description.includes('Transfert de') ? 'envoi' : 'reception'
              } : null
            };
          }));
        }
      }
    }

    // Combiner tous les utilisateurs
    const allUsers = [
      ...favorisDetails,
      ...jobbeursData,
      ...proprietairesData,
      ...jobiUsers
    ];

    // Supprimer les doublons en gardant la première occurrence
    const uniqueUsers = allUsers.filter((user, index, self) =>
      index === self.findIndex((u) => u.id === user.id)
    );

    // Filtrer les utilisateurs selon la recherche
    const filteredUsers = uniqueUsers.filter(user =>
      user.id === query || // Recherche exacte par ID
      user.usertag.toLowerCase().includes(query.toLowerCase()) ||
      user.nom.toLowerCase().includes(query.toLowerCase()) ||
      user.prenom.toLowerCase().includes(query.toLowerCase())
    );

    // Log des utilisateurs formatés
    logger.info('Utilisateurs formatés pour la recherche:', filteredUsers);

    const response = {
      success: true,
      users: filteredUsers
    };

    // Mettre en cache pour 2 minutes
    await redis.setex(cacheKey, 120, JSON.stringify(response));

    res.json(response);

  } catch (error) {
    logger.error('Erreur lors de la recherche d\'utilisateurs:', error);
    res.status(500).json({
      success: false,
      error: 'Une erreur est survenue lors de la recherche'
    });
  }
});

// Route pour récupérer les détails (nom, prénom) d'un lot d'utilisateurs à partir de leurs IDs
userRoutes.post('/details-batch', 
  authMiddleware.authenticateToken, 
  userSearchLimiter,
  async (req, res) => {
  try {
    const userId = req.user?.userId;
    const { userIds } = req.body;

    if (!userId || !userIds || !Array.isArray(userIds) || userIds.length === 0) {
      res.status(400).json({
        success: false,
        error: 'Paramètres manquants ou invalides'
      });
    }

    // Limiter le nombre d'IDs pour éviter les requêtes trop lourdes
    const limitedUserIds = userIds.slice(0, 50);

    // Récupérer les profils des utilisateurs demandés
    const { data: userProfiles, error: profilesError } = await dbService.supabase
      .from('user_profil')
      .select('user_id, nom, prenom')
      .in('user_id', limitedUserIds);

    if (profilesError) {
      logger.error('Erreur lors de la récupération des profils utilisateurs:', profilesError);
      throw profilesError;
    }

    // Déchiffrer et formater les résultats
    const formattedUsers = userProfiles?.map(async (profile: { user_id: string, nom: string, prenom: string }) => {
      const decryptedProfile = await decryptProfilDataAsync(profile);
      return {
        id: profile.user_id,
        nom: decryptedProfile.nom,
        prenom: decryptedProfile.prenom
      };
    }) || [];

    // Attendre que tous les profils soient déchiffrés
    const resolvedFormattedUsers = await Promise.all(formattedUsers);

    res.json({
      success: true,
      users: resolvedFormattedUsers
    });

  } catch (error) {
    logger.error('Erreur lors de la récupération des détails utilisateurs:', error);
    res.status(500).json({
      success: false,
      error: 'Une erreur est survenue lors de la récupération des détails utilisateurs'
    });
  }
});

// Route pour récupérer les informations d'un utilisateur par son slug (usertag)
userRoutes.get('/profile-by-slug/:slug', 
  authMiddleware.authenticateToken, 
  userSearchLimiter,
  async (req: Request, res: Response): Promise<void> => {
  try {
    const { slug } = req.params;

    if (!slug) {
      res.status(400).json({
        success: false,
        message: 'Slug utilisateur requis'
      });
    }

    // Vérifier le cache Redis
    const cacheKey = `user_profile_by_slug:${slug}`;
    const cachedProfile = await redis.get(cacheKey);

    if (cachedProfile) {
      logger.info('Profil utilisateur récupéré depuis le cache Redis');
      res.json(JSON.parse(cachedProfile));
      return;
    }

    // Rechercher le profil utilisateur par son slug
    const { data: profileData, error: profileError } = await supabase
      .from('user_profil')
      .select('*')
      .eq('slug', slug)
      .single();

    if (profileError) {
      console.error('Erreur lors de la récupération du profil:', profileError);
      res.status(500).json({
        success: false,
        message: 'Erreur lors de la récupération du profil utilisateur'
      });
      return;
    }

    if (!profileData) {
      res.status(404).json({
        success: false,
        message: 'Utilisateur non trouvé'
      });
      return;
    }

    // Récupérer les informations utilisateur
    const { data: userData, error: userError } = await supabase
      .from('users')
      .select('id, role, email_verifier, profil_verifier, identite_verifier, entreprise_verifier, assurance_verifier')
      .eq('id', profileData.user_id)
      .single();

    if (userError) {
      console.error('Erreur lors de la récupération des données utilisateur:', userError);
      res.status(500).json({
        success: false,
        message: 'Erreur lors de la récupération des données utilisateur'
      });
      return;
    }

    // Déchiffrer les données de profil
    const decryptedProfileData = await decryptProfilDataAsync(profileData);

    // Fusionner les données du profil et de l'utilisateur
    const userProfile = {
      id: userData.id,
      slug: decryptedProfileData.slug,
      nom: decryptedProfileData.nom,
      prenom: decryptedProfileData.prenom,
      photo_url: decryptedProfileData.photo_url,
      role: userData.role,
      email_verifier: userData.email_verifier,
      profil_verifier: userData.profil_verifier,
      identite_verifier: userData.identite_verifier,
      entreprise_verifier: userData.entreprise_verifier,
      assurance_verifier: userData.assurance_verifier
    };

    const response = {
      success: true,
      user: userProfile
    };

    // Mettre en cache pour 5 minutes
    await redis.setex(cacheKey, 300, JSON.stringify(response));

    res.json(response);

  } catch (error) {
    console.error('Erreur lors de la récupération du profil par slug:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur serveur lors de la récupération du profil'
    });
  }
});

// Routes pour l'historique et les logs
userRoutes.get(
  '/login-history',
  authMiddleware.authenticateToken,
  userSearchLimiter,
  asyncHandler(userControllerInstance.getUserLoginHistory)
);

// Route pour récupérer l'historique des actions
userRoutes.get(
  '/actions-history',
  authMiddleware.authenticateToken,
  userSearchLimiter,
  asyncHandler(userControllerInstance.getUserActionsHistory)
);

// Route pour récupérer les actions publiques récentes d'un utilisateur
userRoutes.get(
  '/public-actions/:userId',
  userSearchLimiter,
  asyncHandler(userControllerInstance.getPublicUserActions)
);

// Route pour récupérer le profil public d'un utilisateur (accessible sans authentification)
userRoutes.get('/public-profile/:slug', 
  userSearchLimiter,
  asyncHandler(async (req: Request, res: Response): Promise<void> => {
  try {
    const { slug } = req.params;

    // Vérifier si le profil est dans le cache
    const cacheKey = `user_public_profile:${slug}`;
    const cachedProfil = await redis.get(cacheKey);

    if (cachedProfil) {
      logger.info('🚀 Profil public récupéré avec succès depuis le cache');
      res.json(JSON.parse(cachedProfil));
      return;
    }

    // Récupérer l'ID de l'utilisateur à partir du slug
    const { data: userProfil, error: userProfilError } = await supabase
      .from('user_profil')
      .select('user_id, profil_visible')
      .eq('slug', slug)
      .single();

    if (userProfilError || !userProfil) {
      res.status(404).json({ message: 'Utilisateur non trouvé' });
      return;
    }

    if (!userProfil.profil_visible) {
      res.status(200).json({ masqué: true, message: 'Ce profil est en cours de modération' });
      return;
    }

    const userId = userProfil.user_id;

    // Récupérer les données de l'utilisateur depuis Supabase
    const { data: userData, error } = await supabase
      .from('users')
      .select(`
        id,
        profil_verifier,
        identite_verifier,
        entreprise_verifier,
        assurance_verifier,
        user_type,
        date_inscription,
        profil:user_profil!inner (*)
      `)
      .eq('id', userId)
      .single();

    if (error) {
      logger.error(`❌ Erreur lors de la récupération du profil pour l'utilisateur: ${userId}`, error);
      res.status(500).json({ message: 'Erreur lors de la récupération du profil' });
      return;
    }

    // Récupérer les avis de l'utilisateur
    const { data: reviews, error: reviewsError } = await supabase
      .from('user_reviews')
      .select('id, author_id, note, commentaire, qualites, defauts, reponse, reponse_date, created_at') // Sélectionner plus de champs
      .eq('target_user_id', userId)
      .eq('statut', 'visible'); // Ne récupérer que les avis visibles

    // Type pour un avis avec les informations de l'auteur
    interface ReviewWithAuthor {
      id: string;
      author_id: string;
      note: number;
      commentaire?: string;
      qualites?: string[];
      defauts?: string[];
      reponse?: string;
      reponse_date?: string;
      created_at: string;
      author?: {
        id: string;
        profil?: [
          {
            prenom?: string;
            nom?: string;
            photo_url?: string;
          }
        ];
      };
    }

    let reviewsWithAuthors: <AUTHORS>
    if (reviews && reviews.length > 0) {
      // Récupérer tous les auteurs d'avis en une seule requête
      const authorIds = [...new Set(reviews.map(r => r.author_id))];
      const { data: authorsData } = await supabase
        .from('users')
        .select('id, profil:user_profil!user_profil_user_id_fkey (prenom, nom, photo_url)')
        .in('id', authorIds);

      reviewsWithAuthors = await Promise.all(reviews.map(async (review) => {
        const author = authorsData?.find(a => a.id === review.author_id);

        if (author?.profil?.[0]) {
          // Déchiffrer les données de profil de l'auteur
          const decryptedAuthorProfil = await decryptProfilDataAsync(author.profil[0]);

          // Masquer le nom de famille de l'auteur pour l'affichage public des avis
          const maskedNom = decryptedAuthorProfil?.nom
            ? `${decryptedAuthorProfil.nom.toUpperCase().slice(0, 1)}...`
            : '';

          return {
            ...review,
            author: {
              id: author.id,
              profil: [{ ...decryptedAuthorProfil, nom: maskedNom }]
            }
          } as ReviewWithAuthor;
        }

        return {
          ...review,
          author: author
            ? {
                id: author.id,
                profil: author.profil || []
              }
            : undefined
        } as ReviewWithAuthor;
      }));
    }

    // Calculer la note moyenne
    let rating = 0;
    let total_reviews = 0;
    if (reviews && reviews.length > 0) {
      total_reviews = reviews.length;
      rating = reviews.reduce((sum, review) => sum + review.note, 0) / total_reviews;
    }

    // Vérifier si l'utilisateur est premium
    const { data: subscription, error: subscriptionError } = await supabase
      .from('user_abo')
      .select('type_abonnement, statut')
      .eq('user_id', userId)
      .eq('statut', 'actif')
      .order('date_debut', { ascending: false })
      .limit(1)
      .single();

    const isPremium = subscription && subscription.type_abonnement !== 'gratuit' && subscription.statut === 'actif';

    // Récupérer les services de l'utilisateur
    const { data: services, error: servicesError } = await supabase
      .from('user_services')
      .select('*')
      .eq('user_id', userId)
      .order('created_at', { ascending: false });

    // Récupérer les galeries de l'utilisateur
    const { data: galleries, error: galleriesError } = await supabase
      .from('user_gallery')
      .select(`
        id,
        name,
        description,
        cover_image,
        status,
        created_at,
        photos:user_gallery_photos (
          id,
          photo_url,
          caption,
          order_index
        )
      `)
      .eq('user_id', userId)
      .eq('status', 'actif')
      .order('created_at', { ascending: false });

    // Formater les galeries pour inclure le nombre d'images
    const formattedGalleries = galleries ? galleries.map(gallery => ({
      id: gallery.id,
      name: gallery.name,
      description: gallery.description,
      cover_image: gallery.cover_image,
      status: gallery.status,
      createdAt: gallery.created_at,
      imagesCount: Array.isArray(gallery.photos) ? gallery.photos.length : 0,
      photos: gallery.photos
    })) : [];

    // Récupérer les photos mises en avant de l'utilisateur
    const { data: featuredPhotos, error: featuredPhotosError } = await supabase
      .from('user_featured_photos')
      .select(`
        id,
        photo_url,
        caption,
        created_at
      `)
      .eq('user_id', userId)
      .order('created_at', { ascending: false });

    // Extraire les données du profil pour faciliter l'accès
    const profilData = Array.isArray(userData.profil) ? userData.profil[0] : userData.profil;

    // Déchiffrer les données de profil
    const decryptedProfilData = profilData ? await decryptProfilDataAsync(profilData) : null;

    // Formater les données pour le frontend
    const formattedData = {
      id: '',
      firstName: decryptedProfilData?.prenom || '',
      lastName: decryptedProfilData?.nom || '',
      email: '', // Ne pas exposer l'email dans le profil public
      photo_url: decryptedProfilData?.photo_url || '',
      banner_url: decryptedProfilData?.banner_url || '', // Ajouter la bannière
      banner_position: decryptedProfilData?.banner_position || 'center',
      banner_position_offset: decryptedProfilData?.banner_position_offset || 0,
      location: `${decryptedProfilData?.ville || ''}, ${decryptedProfilData?.code_postal || ''}`,
      bio: decryptedProfilData?.bio || '',
      hourly_rate: decryptedProfilData?.tarif_horaire || 0,
      profil_verifier: userData.profil_verifier,
      identite_verifier: userData.identite_verifier,
      entreprise_verifier: userData.entreprise_verifier,
      assurance_verifier: userData.assurance_verifier,
      profil_complet: true, // Simplification pour le profil public
      rating: rating,
      total_reviews: total_reviews,
      completion_rate: 100, // Simplification pour le profil public
      telephone: decryptedProfilData?.telephone_prive === false ? decryptedProfilData?.telephone : '', // Inclure le téléphone si telephone_prive est false
      telephone_prive: decryptedProfilData?.telephone_prive === undefined ? true : decryptedProfilData?.telephone_prive, // Inclure la valeur de telephone_prive
      ville: decryptedProfilData?.ville || '',
      code_postal: decryptedProfilData?.code_postal || '',
      pays: decryptedProfilData?.pays || '',
      responseTime: { average: 0, lastWeek: 0 }, // Données simplifiées
      connectionsCount: 0, // Données simplifiées
      companyInfo: {
        type_de_profil: decryptedProfilData?.type_de_profil || '',
        nom_entreprise: decryptedProfilData?.nom_entreprise || '',
        prenom_entreprise: decryptedProfilData?.prenom_entreprise || '',
        statut_entreprise: decryptedProfilData?.statut_entreprise || '',
        siren_entreprise: decryptedProfilData?.siren_entreprise || '',
        code_ape_entreprise: decryptedProfilData?.code_ape_entreprise || '',
        categorie_entreprise: decryptedProfilData?.categorie_entreprise || '',
        effectif_entreprise: decryptedProfilData?.effectif_entreprise || '',
        date_insee_creation_entreprise: decryptedProfilData?.date_insee_creation_entreprise || '',
      },
      isPremium: isPremium,
      // Modification: Inclure les données complètes de la zone d'intervention
      intervention_zone: decryptedProfilData?.intervention_zone ? {
        ville: decryptedProfilData?.ville || '',
        code_postal: decryptedProfilData?.code_postal || '',
        center: Array.isArray(decryptedProfilData.intervention_zone.center) ? decryptedProfilData.intervention_zone.center : undefined,
        radius: decryptedProfilData.intervention_zone.radius !== undefined && decryptedProfilData.intervention_zone.radius !== null ? decryptedProfilData.intervention_zone.radius : undefined,
        france_entiere: decryptedProfilData.intervention_zone.france_entiere !== undefined ? decryptedProfilData.intervention_zone.france_entiere : undefined
      } : undefined,
      mode_vacance: decryptedProfilData?.mode_vacance || false,
      slogan: decryptedProfilData?.slogan || '',
      slug: decryptedProfilData?.slug,
      date_inscription: userData.date_inscription,
      services: services || [],
      galleries: formattedGalleries,
      featuredPhotos: featuredPhotos || [],
      reviews: reviewsWithAuthors || []
    };

    // Mettre en cache les données
    await redis.set(cacheKey, JSON.stringify(formattedData), 'EX', 600); // Cache pour 10 minutes
    res.json(formattedData);
  } catch (error) {
    logger.error('❌ Erreur lors de la récupération du profil public:', error);
    res.status(500).json({ message: 'Erreur serveur lors de la récupération du profil' });
  }
}));

// Route pour récupérer les types d'actions disponibles
userRoutes.get(
  '/action-types',
  authMiddleware.authenticateToken,
  userSearchLimiter,
  asyncHandler(userControllerInstance.getActionTypes)
);

// Route pour mettre à jour les préférences SEO de l'utilisateur
userRoutes.put('/update-seo-preferences',
  authMiddleware.authenticateToken,
  preferencesLimiter,
  body('seo_indexable').isBoolean().withMessage('La préférence SEO doit être un booléen'),
  asyncHandler(async (req: Request, res: Response): Promise<void> => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        res.status(400).json({
          success: false,
          message: 'Données invalides',
          errors: errors.array()
        });
        return;
      }

      const userId = req.user?.userId;
      const { seo_indexable } = req.body;

      if (!userId) {
        res.status(401).json({
          success: false,
          message: 'Non autorisé'
        });
        return;
      }

      // Mettre à jour les préférences SEO
      const { error } = await supabase
        .from('user_profil')
        .update({ seo_indexable })
        .eq('user_id', userId);

      if (error) {
        logger.error('Erreur lors de la mise à jour des préférences SEO:', error);
        res.status(500).json({
          success: false,
          message: 'Erreur lors de la mise à jour des préférences SEO'
        });
        return;
      }

      // Invalider le cache des stats SEO
      const cacheKey = `seo_stats:${userId}`;
      await redis.del(cacheKey);
      logger.info(`Cache SEO invalidé pour userId: ${userId}`);

      // Enregistrer le consentement RGPD
      const consentData = {
        user_id: userId,
        consent_given: seo_indexable,
        ip_address: req.ip || req.socket.remoteAddress,
        user_agent: req.get('User-Agent'),
        consent_method: 'settings_page',
        revoked_date: seo_indexable ? null : new Date().toISOString()
      };

      const { error: consentError } = await supabase
        .from('user_seo_consent')
        .insert(consentData);

      if (consentError) {
        logger.error('Erreur lors de l\'enregistrement du consentement SEO:', consentError);
        // Ne pas faire échouer la requête pour cette erreur
      }

      // Invalider le cache du sitemap
      await redis.del('sitemap_profiles_api');
      await redis.del('sitemap_index_xml');

      // Invalider le cache du profil utilisateur (tous les caches liés à cet utilisateur)
      const cacheKeys = [
        `user:${userId}`,
        `user_deux:${userId}`,
        `user_profil:${userId}`,
        `user_profil_public:*`,
        `user_public_profile:*`
      ];

      for (const key of cacheKeys) {
        if (key.includes('*')) {
          const keys = await redis.keys(key);
          if (keys.length > 0) {
            await redis.del(...keys);
          }
        } else {
          await redis.del(key);
        }
      }

      logger.info(`Préférences SEO mises à jour pour l'utilisateur ${userId}: ${seo_indexable}`);

      res.json({
        success: true,
        message: 'Préférences SEO mises à jour avec succès',
        seo_indexable
      });

    } catch (error) {
      logger.error('Erreur lors de la mise à jour des préférences SEO:', error);
      res.status(500).json({
        success: false,
        message: 'Erreur serveur'
      });
    }
  })
);

// Route pour récupérer les statistiques de promotion SEO
userRoutes.get('/seo-promotion-stats',
  authMiddleware.authenticateToken,
  userSearchLimiter,
  asyncHandler(async (req: Request, res: Response): Promise<void> => {
    try {
      const userId = req.user?.userId;

      if (!userId) {
        res.status(401).json({
          success: false,
          message: 'Non autorisé'
        });
        return;
      }

      // Vérifier le cache Redis pour éviter les recalculs fréquents
      const cacheKey = `seo_stats:${userId}`;
      const cachedStats = await redis.get(cacheKey);

      if (cachedStats) {
        logger.info(`Stats SEO récupérées depuis le cache pour userId: ${userId}`);
        res.json({
          success: true,
          stats: JSON.parse(cachedStats)
        });
        return;
      }

      // Récupérer les informations utilisateur
      const { data: user, error: userError } = await supabase
        .from('users')
        .select('created_at')
        .eq('id', userId)
        .single();

      if (userError) {
        logger.error('Erreur lors de la récupération de l\'utilisateur:', userError);
        res.status(500).json({
          success: false,
          message: 'Erreur serveur'
        });
        return;
      }

      // Récupérer le profil utilisateur
      let profil: any = null;

      try {
        const { data, error } = await supabase
          .from('user_profil')
          .select('nom, prenom, bio, photo_url, ville, code_postal, seo_indexable')
          .eq('user_id', userId)
          .single();

        if (error) {
          throw error;
        }
        // Déchiffrer les données de profil
        profil = await decryptProfilDataAsync(data);
      } catch (profilError: any) {
        logger.error('Erreur lors de la récupération du profil:', profilError);

        // Si l'erreur est due à la colonne seo_indexable qui n'existe pas, on continue avec une valeur par défaut
        if (profilError.message && profilError.message.includes('seo_indexable')) {
          logger.info('Colonne seo_indexable non trouvée, utilisation de la valeur par défaut false');

          try {
            // Récupérer le profil sans seo_indexable
            const { data: profilSansSeO, error: profilSansSeOError } = await supabase
              .from('user_profil')
              .select('nom, prenom, bio, photo_url, ville, code_postal')
              .eq('user_id', userId)
              .single();

            if (profilSansSeOError) {
              throw profilSansSeOError;
            }

            // Déchiffrer les données et ajouter seo_indexable avec valeur par défaut
            const decryptedProfilSansSeO = await decryptProfilDataAsync(profilSansSeO);
            profil = { ...decryptedProfilSansSeO, seo_indexable: false };
          } catch (fallbackError) {
            logger.error('Erreur lors de la récupération du profil sans SEO:', fallbackError);
            res.status(500).json({
              success: false,
              message: 'Erreur serveur'
            });
            return;
          }
        } else {
          res.status(500).json({
            success: false,
            message: 'Erreur serveur'
          });
          return;
        }
      }

      if (!profil) {
        res.status(404).json({
          success: false,
          message: 'Profil non trouvé'
        });
        return;
      }

      // Calculer la complétude du profil selon la même logique que le frontend
      // Récupérer les services de l'utilisateur
      const { data: userServices, error: servicesError } = await supabase
        .from('user_services')
        .select('*')
        .eq('user_id', userId)
        .eq('statut', 'actif');

      if (servicesError) {
        logger.error('Erreur lors de la récupération des services:', servicesError);
      }

      const hasServices = !!(userServices && userServices.length > 0);

      // Récupérer les galeries de l'utilisateur avec le nombre de photos
      const { data: galleries, error: galleriesError } = await supabase
        .from('user_gallery')
        .select(`
          id,
          user_gallery_photos!gallery_id(count)
        `)
        .eq('user_id', userId);

      if (galleriesError) {
        logger.error('Erreur lors de la récupération des galeries:', galleriesError);
      }

      const hasGalleryWithPhotos = !!(galleries && galleries.some(gallery => {
        const photoCount = gallery.user_gallery_photos?.[0]?.count || 0;
        return photoCount > 0;
      }));

      // Définir les critères de complétude selon la logique du frontend
      type ImportanceLevel = 'high' | 'medium' | 'low';

      interface ProfileField {
        name: string;
        isComplete: boolean;
        importance: ImportanceLevel;
      }

      const fields: ProfileField[] = [
        {
          name: 'Photo de profil',
          isComplete: !!profil.photo_url,
          importance: 'high'
        },
        {
          name: 'Nom et prénom',
          isComplete: !!profil.nom && !!profil.prenom,
          importance: 'high'
        },
        {
          name: 'Numéro de téléphone',
          isComplete: !!profil.telephone,
          importance: 'high'
        },
        {
          name: 'Adresse complète',
          isComplete: (() => {
            // Vérifier que tous les champs d'adresse sont remplis
            return !!(
              profil.numero &&
              profil.adresse && profil.adresse.length > 1 &&
              profil.ville && profil.ville.length > 1 &&
              profil.code_postal && profil.code_postal.length >= 4 &&
              profil.pays && profil.pays.length > 1 &&
              // S'assurer que ce ne sont pas des valeurs par défaut
              profil.ville !== 'undefined' &&
              profil.code_postal !== 'undefined' &&
              profil.pays !== 'undefined' &&
              profil.adresse !== 'undefined'
            );
          })(),
          importance: 'high'
        },
        {
          name: 'A propos',
          isComplete: !!profil.bio && profil.bio.length >= 10 && profil.bio !== '<p><br></p>',
          importance: 'high'
        },
        {
          name: 'Services proposés',
          isComplete: hasServices,
          importance: 'high'
        },
        {
          name: 'Zone d\'intervention',
          isComplete: !!profil.intervention_zone?.center &&
            !(profil.intervention_zone.center[0] === 48.8566 && profil.intervention_zone.center[1] === 2.3522),
          importance: 'high'
        },
        {
          name: 'Informations du profil',
          isComplete: !!profil.type_de_profil && profil.type_de_profil !== '',
          importance: 'high'
        },
        {
          name: 'Galerie avec photos',
          isComplete: hasGalleryWithPhotos,
          importance: 'high'
        }
      ];

      const weights: Record<ImportanceLevel, number> = {
        high: 3,
        medium: 2,
        low: 1
      };

      const totalWeight = fields.reduce((sum, field) => sum + weights[field.importance], 0);
      const completedWeight = fields.reduce((sum, field) =>
        sum + (field.isComplete ? weights[field.importance] : 0), 0
      );

      const profileCompleteness = Math.round((completedWeight / totalWeight) * 100);

      // Compter les propositions/offres envoyées par l'utilisateur
      const { count: propositionsCount, error: propositionsError } = await supabase
        .from('user_mission_candidature')
        .select('*', { count: 'exact', head: true })
        .eq('jobbeur_id', userId);

      if (propositionsError) {
        logger.error('Erreur lors du comptage des propositions:', propositionsError);
      }

      const missionsCompleted = propositionsCount || 0;

      // Debug: Vérifier la valeur de seo_indexable
      logger.info(`Debug SEO - userId: ${userId}, seo_indexable brut: ${profil.seo_indexable}, type: ${typeof profil.seo_indexable}`);

      const stats = {
        registrationDate: user.created_at,
        profileCompleteness,
        missionsCompleted,
        seoIndexable: Boolean(profil.seo_indexable), // Conversion explicite en booléen
        lastSeoPrompt: null // À implémenter si vous voulez tracker côté serveur
      };

      // Mettre en cache les stats pour 5 minutes
      await redis.set(cacheKey, JSON.stringify(stats), 'EX', 300);

      res.json({
        success: true,
        stats
      });

    } catch (error) {
      logger.error('Erreur lors de la récupération des stats SEO:', error);
      res.status(500).json({
        success: false,
        message: 'Erreur serveur'
      });
    }
  })
);

// Route pour marquer une promotion SEO comme affichée
userRoutes.post('/seo-promotion-shown',
  authMiddleware.authenticateToken,
  preferencesLimiter,
  body('trigger').isString().withMessage('Le trigger est requis'),
  asyncHandler(async (req: Request, res: Response): Promise<void> => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        res.status(400).json({
          success: false,
          message: 'Données invalides',
          errors: errors.array()
        });
        return;
      }

      const userId = req.user?.userId;
      const { trigger } = req.body;

      if (!userId) {
        res.status(401).json({
          success: false,
          message: 'Non autorisé'
        });
        return;
      }

      // Enregistrer l'affichage de la promotion (optionnel - pour analytics)
      logger.info(`Promotion SEO affichée pour l'utilisateur ${userId} avec trigger: ${trigger}`);

      res.json({
        success: true,
        message: 'Promotion marquée comme affichée'
      });

    } catch (error) {
      logger.error('Erreur lors du marquage de la promotion SEO:', error);
      res.status(500).json({
        success: false,
        message: 'Erreur serveur'
      });
    }
  })
);

userRoutes.put('/update-email-preferences',
  authMiddleware.authenticateToken,
  preferencesLimiter,
  body('preferences').isObject().withMessage('Les préférences doivent être un objet valide'),
  async (req, res): Promise<void> => {
    try {
      // Valider la requête
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        res.status(400).json({
          success: false,
          message: 'Données de requête invalides',
          errors: errors.array()
        });
      }

      const userId = req.user?.userId;
      const { preferences } = req.body;

      logger.info('Mise à jour des préférences email pour userId:', userId);

      // Structure des préférences attendue
      if (!preferences.email_notifications) {
        res.status(400).json({
          success: false,
          message: 'Les préférences doivent inclure email_notifications'
        });
      }

      // Récupérer les préférences actuelles
      const { data: currentUser } = await supabase
        .from('users')
        .select('notification_preferences')
        .eq('id', userId)
        .single();

      // Fusionner les préférences existantes avec les nouvelles
      const currentPreferences = currentUser?.notification_preferences || {};
      let updatedPreferences = {
        ...currentPreferences,
        email_enabled: preferences.email_enabled !== undefined ? preferences.email_enabled : true,
        email_notifications: preferences.email_notifications
      };

      // Mise à niveau: Si le nouveau format n'est pas encore présent, créer une structure par défaut
      if (!currentPreferences.email_enabled && !currentPreferences.email_notifications) {
        updatedPreferences = {
          email_enabled: true,
          email_notifications: {
            nouvelles_offres: true,
            connexion: true,
            messages: true,
            evaluations: true,
            missions: true,
            paiements: true,
            systeme: true,
            badges: true
          },
          sms_enabled: false,
          app_enabled: true
        };
      }

      // Mettre à jour la base de données avec les préférences mises à jour
      const { error } = await supabase
        .from('users')
        .update({ notification_preferences: updatedPreferences })
        .eq('id', userId);

      if (error) {
        logger.error('Erreur lors de la mise à jour des préférences de notification:', error);
        res.status(500).json({
          success: false,
          message: 'Erreur lors de la mise à jour des préférences'
        });
        return;
      }

      // Mettre à jour le cache Redis
      const cacheKey = `email_preferences:${userId}`;
      await redis.set(cacheKey, JSON.stringify(updatedPreferences), 'EX', 300);

      res.json({
        success: true,
        message: 'Préférences de notification mises à jour avec succès'
      });
    } catch (error) {
      logger.error('Erreur serveur lors de la mise à jour des préférences:', error);
      res.status(500).json({
        success: false,
        message: 'Erreur serveur'
      });
    }
  }
);

userRoutes.get('/email-preferences',
  authMiddleware.authenticateToken,
  preferencesLimiter,
  async (req, res): Promise<void> => {
    try {
      const userId = req.user?.userId;

      // Vérifier si les données sont en cache
      const cacheKey = `email_preferences:${userId}`;
      const cachedPreferences = await redis.get(cacheKey);

      if (cachedPreferences) {
        res.json({
          success: true,
          preferences: JSON.parse(cachedPreferences)
        });
        return;
      }

      // Récupérer les préférences de notification depuis la base de données
      const { data, error } = await supabase
        .from('users')
        .select('notification_preferences')
        .eq('id', userId)
        .single();

      if (error) {
        logger.error('Erreur lors de la récupération des préférences de notification:', error);
        res.status(500).json({
          success: false,
          message: 'Erreur lors de la récupération des préférences'
        });
        return;
      }

      // Construire l'objet de préférences
      let preferences = data.notification_preferences || {};

      // Mise à niveau: Si le nouveau format n'est pas encore présent, créer une structure par défaut
      if (!preferences.email_enabled && !preferences.email_notifications) {
        preferences = {
          email_enabled: true,
          email_notifications: {
            nouvelles_offres: true,
            connexion: true,
            messages: true,
            evaluations: true,
            missions: true,
            paiements: true,
            systeme: true,
            badges: true
          },
          sms_enabled: false,
          app_enabled: true
        };
      }

      // Mettre en cache pour 5 minutes
      await redis.set(cacheKey, JSON.stringify(preferences), 'EX', 300);

      res.json({
        success: true,
        preferences
      });
    }
    catch (error) {
      logger.error('Erreur serveur lors de la récupération des préférences:', error);
      res.status(500).json({
        success: false,
        message: 'Erreur serveur'
      });
    }
  }
);

// Route d'administration pour suspendre/bannir un utilisateur (définitif ou temporaire)
userRoutes.post('/admin/suspend-user', 
  authMiddleware.authenticateToken, 
  adminActionsLimiter,
  asyncHandler(async (req: Request, res: Response): Promise<void> => {
  try {
    // Vérification du rôle admin ou modo
    const adminUser = req.user;
    if (!adminUser || !['jobpadm', 'jobmodo'].includes(adminUser.role)) {
      res.status(403).json({ success: false, message: 'Accès refusé' });
      return;
    }
    const { userId, profil_actif, suspension_reason, suspended_until } = req.body;
    if (!userId || typeof profil_actif !== 'boolean') {
      res.status(400).json({ success: false, message: 'Paramètres manquants' });
      return;
    }
    // Préparer les données de mise à jour
    const updateData: any = { profil_actif };
    if (profil_actif === true) {
      updateData.suspended_until = null;
      updateData.suspension_reason = null;
    } else {
      if (typeof suspension_reason === 'string' && suspension_reason.trim() !== '') {
        updateData.suspension_reason = suspension_reason.trim();
      } else {
        updateData.suspension_reason = null;
      }
      if (suspended_until) {
        updateData.suspended_until = suspended_until;
      } else {
        updateData.suspended_until = null;
      }
    }
    // Mettre à jour l'utilisateur
    await dbService.updateUser(userId, updateData);

    // Si l'utilisateur est banni, invalider tous ses tokens pour forcer la déconnexion
    if (profil_actif === false) {
      const { tokenService } = require('../services/tokenService');
      await tokenService.invalidateAllUserTokens(userId, 'suspension');

      // Vérifier si l'utilisateur a un abonnement premium actif
      const { data: userAbo, error: aboError } = await supabase
        .from('user_abo')
        .select('*')
        .eq('user_id', userId)
        .eq('statut', 'actif')
        .eq('type_abonnement', 'premium')
        .maybeSingle();

      if (userAbo && !aboError) {
        logger.info(`Annulation de l'abonnement premium pour l'utilisateur banni: ${userId}`);

        // Annuler l'abonnement dans la base de données
        await supabase
          .from('user_abo')
          .update({
            renouvellement_auto: false,
            raison_annulation_abonnement: 'Compte banni'
          })
          .eq('id', userAbo.id);

        // Annuler l'abonnement via l'API Stripe si un ID d'abonnement Stripe existe
        if (userAbo.stripe_subscription_id) {
          const { stripe } = require('../config/stripe');
          try {
            await stripe.subscriptions.update(userAbo.stripe_subscription_id, {
              cancel_at_period_end: true,
              metadata: {
                canceled_reason: 'user_banned'
              }
            });
            logger.info(`Abonnement Stripe annulé pour l'utilisateur banni: ${userId}, subscription: ${userAbo.stripe_subscription_id}`);

            // Créer une notification pour l'annulation de l'abonnement Premium
            try {
              await supabase
                .from('user_notifications')
                .insert({
                  user_id: userId,
                  type: 'subscription',
                  title: 'Votre abonnement Premium a été annulé',
                  content: `Suite à la suspension de votre compte, votre abonnement Premium a été automatiquement annulé. Il restera actif jusqu'au ${new Date(userAbo.date_fin).toLocaleDateString('fr-FR')}.`,
                  link: '/dashboard/premium',
                  is_read: false,
                  is_archived: false
                });
              logger.info(`Notification d'annulation d'abonnement créée pour l'utilisateur banni: ${userId}`);
            } catch (notifError) {
              logger.error(`Erreur lors de la création de la notification d'annulation d'abonnement pour l'utilisateur ${userId}`, { error: notifError });
            }
          } catch (stripeError) {
            logger.error(`Erreur lors de l'annulation de l'abonnement Stripe: ${userId}`, { error: stripeError });
          }
        }
      }
    }

    // Envoyer un email selon l'action
    const userRaw = await dbService.getUserById(userId);
    if (userRaw) {
      // Décrypter les données utilisateur
      const user = await decryptUserDataAsync(userRaw);
      if (user && user.email) {
      const { sendSuspensionEmail } = require('../services/emailService');
      if (profil_actif === false) {
        // Vérifier si l'utilisateur avait un abonnement premium
        const { data: userAbo, error: aboError } = await supabase
          .from('user_abo')
          .select('*')
          .eq('user_id', userId)
          .eq('type_abonnement', 'premium')
          .maybeSingle();

        // Envoyer l'email avec mention de l'annulation de l'abonnement si nécessaire
        let emailMessage = updateData.suspension_reason || 'Contactez le support pour plus d\'informations.';
        if (userAbo && !aboError) {
          emailMessage += " Votre abonnement premium a également été annulé suite à cette suspension.";

          // Envoyer un email spécifique pour l'annulation de l'abonnement
          const { sendSubscriptionStatusChangeEmail } = require('../services/emailService');

          // Créer le contenu HTML pour l'email
          const baseUrl = process.env.FRONTEND_URL || 'http://localhost:5173';
          const htmlContent = `
            <div style="font-family: Arial, sans-serif; width: 100%; max-width: 600px; margin: 0 auto; background: linear-gradient(135deg, #FFF8F3 0%, #FFE4BA 100%); padding: 40px 0;">
              <div style="background-color: white; padding: 40px; border-radius: 16px; box-shadow: 0 4px 6px rgba(0,0,0,0.1);">
                <div style="text-align: center; margin-bottom: 30px;">
                  <img src="${baseUrl}/logo.png" alt="JobPartiel Logo" style="max-width: 150px; height: auto;">
                </div>
                <h2 style="color: #FF6B2C; text-align: center; margin-bottom: 20px;">Annulation de votre abonnement Premium</h2>
                <p style="color: #333; font-size: 16px; line-height: 1.5; margin-bottom: 20px;">
                  Suite à la suspension de votre compte, votre abonnement Premium a été automatiquement annulé.
                </p>
                <p style="color: #333; font-size: 16px; line-height: 1.5; margin-bottom: 20px;">
                  Votre abonnement restera actif jusqu'à la fin de la période en cours, soit jusqu'au ${new Date(userAbo.date_fin).toLocaleDateString('fr-FR')}.
                </p>
                <p style="color: #333; font-size: 16px; line-height: 1.5; margin-bottom: 20px;">
                  Pour toute question concernant cette annulation, veuillez contacter notre service client.
                </p>
                <div style="text-align: center; margin-top: 30px;">
                  <p style="color: #777; font-size: 14px;">© ${new Date().getFullYear()} JobPartiel - Tous droits réservés</p>
                </div>
              </div>
            </div>
          `;

          await sendSubscriptionStatusChangeEmail(user.email, {
            changeType: 'cancellation',
            newPlan: 'premium',
            html: htmlContent
          });
        }

        await sendSuspensionEmail(user.email, emailMessage, updateData.suspended_until);
      } else if (profil_actif === true) {
        // Vérifier si l'utilisateur avait un abonnement premium annulé à cause d'un bannissement
        const { data: userAbo, error: aboError } = await supabase
          .from('user_abo')
          .select('*')
          .eq('user_id', userId)
          .eq('type_abonnement', 'premium')
          .eq('raison_annulation_abonnement', 'Compte banni')
          .order('date_fin', { ascending: false })
          .maybeSingle();

        // Si l'utilisateur avait un abonnement premium annulé à cause d'un bannissement et que l'abonnement est encore actif
        if (userAbo && !aboError && userAbo.stripe_subscription_id && new Date(userAbo.date_fin) > new Date()) {
          logger.info(`Réactivation de l'abonnement premium pour l'utilisateur débanni: ${userId}`);

          // Réactiver le renouvellement automatique dans la base de données
          await supabase
            .from('user_abo')
            .update({
              renouvellement_auto: true,
              raison_annulation_abonnement: null
            })
            .eq('id', userAbo.id);

          // Réactiver l'abonnement via l'API Stripe
          const { stripe } = require('../config/stripe');
          try {
            await stripe.subscriptions.update(userAbo.stripe_subscription_id, {
              cancel_at_period_end: false,
              metadata: {
                reactivated_reason: 'user_unbanned',
                is_unbanned_reactivation: 'true'  // Ajout d'un flag spécifique pour indiquer que c'est une réactivation après débannissement
              }
            });
            logger.info(`Abonnement Stripe réactivé pour l'utilisateur débanni: ${userId}, subscription: ${userAbo.stripe_subscription_id}`);

            // Réactiver les fonctionnalités premium désactivées lors du bannissement
            try {
              const { reactivatePremiumFeatures } = require('../routes/configSubscriptions');
              const reactivationResult = await reactivatePremiumFeatures(userId, userAbo.options || {});

              if (reactivationResult.success && reactivationResult.summary !== 'Aucune réactivation nécessaire') {
                logger.info(`Fonctionnalités premium réactivées lors du débannissement pour l'utilisateur ${userId}: ${reactivationResult.summary}`);
              }
            } catch (reactivationError) {
              logger.error(`Erreur lors de la réactivation des fonctionnalités premium lors du débannissement pour l'utilisateur ${userId}:`, reactivationError);
            }

            // Envoyer un seul email combiné pour la réactivation du compte et de l'abonnement
            const baseUrl = process.env.FRONTEND_URL || 'http://localhost:5173';
            const htmlContent = `
              <div style="font-family: Arial, sans-serif; width: 100%; max-width: 600px; margin: 0 auto; background: linear-gradient(135deg, #FFF8F3 0%, #FFE4BA 100%); padding: 40px 0;">
                <div style="background-color: white; padding: 40px; border-radius: 16px; box-shadow: 0 4px 6px rgba(0,0,0,0.1);">
                  <div style="text-align: center; margin-bottom: 30px;">
                    <img src="${baseUrl}/logo.png" alt="JobPartiel Logo" style="max-width: 150px; height: auto;">
                  </div>
                  <h2 style="color: #FF6B2C; text-align: center; margin-bottom: 20px;">Votre compte et abonnement ont été réactivés</h2>

                  <div style="background-color: #FFF8F3; padding: 20px; border-radius: 12px; margin-bottom: 20px;">
                    <h3 style="color: #FF6B2C; margin-top: 0;">Réactivation de votre compte</h3>
                    <p style="color: #333; font-size: 16px; line-height: 1.5; margin-bottom: 10px;">
                      Votre compte JobPartiel a été réactivé avec succès. Vous pouvez à nouveau accéder à toutes les fonctionnalités de la plateforme.
                    </p>
                    ${updateData.suspension_reason ? `<p style="color: #333; font-size: 16px; line-height: 1.5; margin-bottom: 0;">
                      Raison de la réactivation : ${updateData.suspension_reason}
                    </p>` : ''}
                  </div>

                  <div style="background-color: #FFF8F3; padding: 20px; border-radius: 12px; margin-bottom: 20px;">
                    <h3 style="color: #FF6B2C; margin-top: 0;">Réactivation de votre abonnement Premium</h3>
                    <p style="color: #333; font-size: 16px; line-height: 1.5; margin-bottom: 10px;">
                      Votre abonnement Premium a également été réactivé automatiquement.
                    </p>
                    <p style="color: #333; font-size: 16px; line-height: 1.5; margin-bottom: 10px;">
                      Votre abonnement continuera sans interruption et sera automatiquement renouvelé à la fin de la période en cours, le ${new Date(userAbo.date_fin).toLocaleDateString('fr-FR')}.
                    </p>
                    <p style="color: #333; font-size: 16px; line-height: 1.5; margin-bottom: 10px;">
                      <strong>Important :</strong> Cette réactivation suite à un débannissement ne génère pas de bonus Jobi. Les bonus de 20 Jobi sont uniquement attribués lors des renouvellements réguliers d'abonnement.
                    </p>
                    <p style="color: #333; font-size: 16px; line-height: 1.5; margin-bottom: 0;">
                      Vous pouvez gérer votre abonnement à tout moment depuis votre espace personnel.
                    </p>
                  </div>

                  <div style="text-align: center; margin-top: 30px;">
                    <a href="${baseUrl}/dashboard"
                       style="display: inline-block; background-color: #FF6B2C; color: white; text-decoration: none; padding: 12px 25px; border-radius: 8px; font-weight: bold;">
                      Accéder à mon compte
                    </a>
                  </div>

                  <div style="text-align: center; margin-top: 30px;">
                    <p style="color: #777; font-size: 14px;">© ${new Date().getFullYear()} JobPartiel - Tous droits réservés</p>
                  </div>
                </div>
              </div>
            `;

            // Utiliser directement le service d'email pour envoyer un seul email
            const { queueEmail } = require('../services/emailQueueService');
            await queueEmail(
              user.email,
              "Votre compte et abonnement Premium ont été réactivés ✅",
              htmlContent
            );

            logger.info(`Email combiné de réactivation envoyé à l'utilisateur: ${userId}`);

            // Créer une notification spécifique pour la réactivation de l'abonnement Premium
            try {
              await supabase
                .from('user_notifications')
                .insert({
                  user_id: userId,
                  type: 'subscription',
                  title: 'Votre abonnement Premium a été réactivé',
                  content: `Suite à la réactivation de votre compte, votre abonnement Premium a été automatiquement réactivé. Il sera renouvelé automatiquement le ${new Date(userAbo.date_fin).toLocaleDateString('fr-FR')}. Notez que cette réactivation ne génère pas de bonus Jobi, contrairement aux renouvellements réguliers.`,
                  link: '/dashboard/abonnement',
                  is_read: false,
                  is_archived: false
                });
              logger.info(`Notification de réactivation d'abonnement créée pour l'utilisateur ${userId}`);
            } catch (notifError) {
              logger.error(`Erreur lors de la création de la notification de réactivation d'abonnement pour l'utilisateur ${userId}`, { error: notifError });
            }
          } catch (stripeError) {
            logger.error(`Erreur lors de la réactivation de l'abonnement Stripe: ${userId}`, { error: stripeError });
            // Envoyer l'email standard de réactivation du compte
            await sendSuspensionEmail(user.email, updateData.suspension_reason || "Votre compte a été réactivé. Vous pouvez à nouveau accéder à la plateforme. Si vous avez des questions, contactez le support.");
          }
        } else {
          // Envoyer l'email standard de réactivation du compte
          await sendSuspensionEmail(user.email, updateData.suspension_reason || "Votre compte a été réactivé. Vous pouvez à nouveau accéder à la plateforme. Si vous avez des questions, contactez le support.");
        }
      }
      }
    }

    // Log l'action
    logger.info('Utilisateur suspendu/banni', { userId, adminId: adminUser.id, profil_actif, suspension_reason, suspended_until });

    // Journaliser dans l'historique des activités du compte
    const { logUserActivity, getIpFromRequest } = require('../utils/activityLogger');
    if (profil_actif === false) {
      await logUserActivity(
        userId,
        'ban_user',
        undefined,
        undefined,
        {
          message: "L'utilisateur a été banni.",
          reason: updateData.suspension_reason,
          "Débannissement prévu le": updateData.suspended_until
        },
        req.ip
      );

      // Créer une notification pour l'utilisateur banni
      try {
        await supabase
          .from('user_notifications')
          .insert({
            user_id: userId,
            type: 'system',
            title: 'Votre compte a été suspendu',
            content: updateData.suspension_reason
              ? `Votre compte a été suspendu pour la raison suivante : ${updateData.suspension_reason}${updateData.suspended_until ? `. Votre compte sera réactivé le ${new Date(updateData.suspended_until).toLocaleDateString('fr-FR')}.` : ''}`
              : 'Votre compte a été suspendu. Veuillez contacter le support pour plus d\'informations.',
            link: '/dashboard/support/new',
            is_read: false,
            is_archived: false
          });
        logger.info(`Notification de suspension créée pour l'utilisateur ${userId}`);
      } catch (notifError) {
        logger.error(`Erreur lors de la création de la notification de suspension pour l'utilisateur ${userId}`, { error: notifError });
      }
    } else if (profil_actif === true) {
      await logUserActivity(
        userId,
        'unban_user',
        undefined,
        undefined,
        {
          message: "L'utilisateur a été débanni."
        },
        req.ip
      );

      // Créer une notification pour l'utilisateur débanni
      try {
        await supabase
          .from('user_notifications')
          .insert({
            user_id: userId,
            type: 'system',
            title: 'Votre compte a été réactivé',
            content: 'Votre compte a été réactivé. Vous pouvez à nouveau accéder à toutes les fonctionnalités de la plateforme.',
            link: '/dashboard',
            is_read: false,
            is_archived: false
          });
        logger.info(`Notification de réactivation créée pour l'utilisateur ${userId}`);
      } catch (notifError) {
        logger.error(`Erreur lors de la création de la notification de réactivation pour l'utilisateur ${userId}`, { error: notifError });
      }
    }
    res.json({ success: true, message: 'Utilisateur mis à jour' });
  } catch (error) {
    logger.error('Erreur lors de la suspension/bannissement utilisateur', { error });
    res.status(500).json({ success: false, message: 'Erreur serveur' });
  }
}));

// Route pour upload de documents de vérification entreprise
userRoutes.post(
  '/verification/entreprise/upload',
  authMiddleware.authenticateToken,
  verificationDocsLimiter,
  asyncHandler(uploadEntrepriseVerificationDocs)
);

// Route pour la liste des documents de vérification entreprise (admin/modo)
userRoutes.get(
  '/verification/entreprise/list',
  authMiddleware.authenticateToken,
  authMiddleware.checkRole(['jobpadm', 'jobmodo']),
  verificationDocsLimiter,
  listEntrepriseVerificationDocs
);

// Route pour que l'utilisateur récupère ses propres documents de vérification entreprise
userRoutes.get(
  '/verification/entreprise/my-docs',
  authMiddleware.authenticateToken,
  verificationDocsLimiter,
  getMyEntrepriseVerificationDocs
);

// Route pour valider/refuser un document de vérification entreprise (admin/modo)
userRoutes.post(
  '/verification/entreprise/validate',
  authMiddleware.authenticateToken,
  authMiddleware.checkRole(['jobpadm', 'jobmodo']),
  adminActionsLimiter,
  validateEntrepriseVerificationDoc
);

// Route pour supprimer tous les documents de vérification entreprise (admin/modo)
userRoutes.post(
  '/verification/entreprise/delete-all',
  authMiddleware.authenticateToken,
  authMiddleware.checkRole(['jobpadm', 'jobmodo']),
  adminActionsLimiter,
  require('../controllers/user').deleteAllEntrepriseVerificationDocs
);

// Route pour supprimer un document de vérification entreprise (admin/modo)
userRoutes.post(
  '/verification/entreprise/delete',
  authMiddleware.authenticateToken,
  authMiddleware.checkRole(['jobpadm', 'jobmodo']),
  adminActionsLimiter,
  require('../controllers/user').deleteEntrepriseVerificationDoc
);

// Route pour forcer un statut de vérification (admin/modo)
userRoutes.post(
  '/verification/force-status',
  authMiddleware.authenticateToken,
  authMiddleware.checkRole(['jobpadm', 'jobmodo']),
  adminActionsLimiter,
  forceUserVerificationStatus
);

// Route pour demander à un utilisateur de remplir ses infos entreprise (envoi email)
userRoutes.post('/entreprise/remind-infos', 
  authMiddleware.authenticateToken, 
  adminActionsLimiter,
  async (req: Request, res: Response): Promise<void> => {
  try {
    const { userId } = req.body;
    if (!userId) res.status(400).json({ success: false, error: 'userId manquant' });
    await sendEntrepriseRemindInfosEmail(userId);
    res.json({ success: true });
  } catch (e) {
    res.status(500).json({ success: false, error: 'Erreur lors de l\'envoi de l\'email.' });
  }
});

export default userRoutes;