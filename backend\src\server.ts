import express from 'express';
import { createServer } from 'http';
import { Server } from 'socket.io';
import fileUpload from 'express-fileupload';
import { limiter, securityMiddleware, errorHandler } from './middleware/security';
import { requestLogger } from './middleware/requestLogger';
import { logSecurity } from './services/logger';
import { LogEventType } from './types/logger';
import { authRoutes } from './routes/auth';
import { userRoutes } from './routes/user';
import { jobiRoutes } from './routes/jobi';
import configSubscriptionsRouter from './routes/configSubscriptions';
import emailRoutes from './routes/email';
import servicesRoutes from './routes/services';
import sireneRoutes from './routes/sirene';
import badgesRoutes from './routes/badges';
import missionsRoutes from './routes/missions';
import missionResponsesRoutes from './routes/missionResponses';
import bugReportsRoutes from './routes/bugReports';
import supportRoutes from './routes/SupportTickets';
import vpsRoutes from './routes/vpsRoutes';
import messagingRoutes from './routes/messagingRoutes';
import reviewsRouter from './routes/reviews';
import paymentsRoutes from './routes/payments';
import invoicesRoutes from './routes/invoices';
import config from './config';
// import { checkPasswordExpiration } from './middleware/checkPasswordExpiration';
import { cleanupService } from './services/cleanupService';
import session from 'express-session';

import { SuspiciousActivityDetector } from './services/suspiciousActivityDetector';
import { RedisStore } from 'connect-redis';
import cors from 'cors';
import logger from './utils/logger';
import { redis } from './config/redis';  // Import de l'instance Redis singleton
import { initializeEncryption } from './utils/encryption'; // Import de l'initialisation du cryptage
import favoritesRoutes from './routes/favorites';
import { notificationRoutes } from './routes/notifications';
import { setupCronJobs } from './config/cronJobs';
import { execSync } from 'child_process';
import path from 'path';
import fs from 'fs';
import jwt, { JwtPayload } from 'jsonwebtoken';
import { planningRoutes } from './routes/planning';
import advancedStatsRoutes from './routes/advancedStats';
import qualityMetricsRoutes from './routes/qualityMetrics';
import clientRoutes from './routes/clientRoutes';
import passport from './config/passport';
import { stripe } from './config/stripe';
import { supabase } from './config/supabase';
import { logUserActivity } from './utils/activityLogger';
import promocodesRouter from './routes/promocodes';
import reportedContentRoutes from './routes/reportedContent';
import redisAdminRouter from './routes/redisAdmin';
import supabaseProxyRouter from './routes/supabaseProxy';
import emailQueueAdminRouter from './routes/emailQueueAdmin';
import newsletterRouter from './routes/newsletter';
import contentModerationRouter from './routes/contentModeration';
import openRouterRouter from './routes/openRouter';
import aiCreditsRouter from './routes/aiCredits';
import sitemapRouter from './routes/sitemap';
import aiGenerationRouter from './routes/aiGeneration';
import aiImageGenerationRouter from './routes/aiImageGeneration';
import aiImageStatsRouter from './routes/aiImageStats';
import missionAssistantRouter from './routes/missionAssistant';
import storageRouter from './routes/storage';
import aiConsentRoutes from './routes/aiConsent';
import cardEditorRouter from './routes/cardEditor';
import seoPagesRouter from './routes/seoPages';
import securityMonitoringRouter from './routes/securityMonitoring';
import userManagementRoutes from './routes/userManagement';

// Interface pour notre token JWT
interface JWTPayload extends JwtPayload {
  userId: string;
}

const app = express();
const port = config.port;
const httpServer = createServer(app);
const io = new Server(httpServer, {
  cors: {
    origin: config.frontendUrl,
    methods: ['GET', 'POST'],
    credentials: true
  }
});

// Route webhook Stripe spéciale qui doit être définie AVANT tous les middlewares
app.post('/api/stripe/webhook', express.raw({type: 'application/json'}), async (req, res) => {
  console.log('1 Stripe webhook reçu');

  const sig = req.headers['stripe-signature'] as string;
  const rawBody = req.body;

  if (!sig) {
    console.log('Signature Stripe manquante');
    res.status(400).json({ success: false, message: 'Signature Stripe manquante' });
    return;
  }

  try {
    const webhookSecret = process.env.STRIPE_WEBHOOK_SECRET;

    if (!webhookSecret) {
      console.error('ERREUR CRITIQUE: STRIPE_WEBHOOK_SECRET non défini dans les variables d\'environnement');
      res.status(500).json({
        success: false,
        message: 'Configuration serveur manquante'
      });
      return;
    }

    console.log('Vérification de la signature Stripe');

    const event = stripe.webhooks.constructEvent(rawBody, sig, webhookSecret);

    console.log('Événement Stripe valide reçu:', event.type, event.id);

    // Importer les fonctions nécessaires de configSubscriptions pour traiter l'événement
    const { handleSubscriptionChange, handleSubscriptionCanceled, notifyUpcomingPayment } = require('./routes/configSubscriptions');

    // Traiter l'événement selon son type
    switch (event.type) {
      case 'customer.subscription.created':
      case 'customer.subscription.updated':
        await handleSubscriptionChange(event.data.object);
        break;

      case 'customer.subscription.deleted':
        await handleSubscriptionCanceled(event.data.object);
        break;

      case 'invoice.upcoming':
        await notifyUpcomingPayment(event.data.object);
        break;

      case 'invoice.finalized':
        logger.info(`Facture finalisée reçue: ${event.data.object.id}`);
        break;

      case 'checkout.session.completed':
        logger.info(`Session de paiement complétée: ${event.data.object.id}`);

        // Vérifier si c'est un paiement pour des crédits IA
        const session = event.data.object;
        if (session.metadata && session.metadata.type === 'ai_credits') {
          const userId = session.metadata.userId;
          const creditsAmount = parseInt(session.metadata.creditsAmount) || 10;

          logger.info(`Ajout de ${creditsAmount} crédits IA pour l'utilisateur ${userId}`);

          try {
            // Récupérer les crédits actuels
            const { data, error } = await supabase
              .from('user_ai_credits')
              .select('credits')
              .eq('user_id', userId)
              .single();

            if (error && error.code !== 'PGRST116') {
              logger.error('Erreur lors de la récupération des crédits IA:', error);
              break;
            }

            const currentCredits = data?.credits || 0;
            const newCredits = currentCredits + creditsAmount;

            if (data) {
              // Mettre à jour l'entrée existante
              await supabase
                .from('user_ai_credits')
                .update({
                  credits: newCredits,
                  updated_at: new Date().toISOString()
                })
                .eq('user_id', userId);
            } else {
              // Créer une nouvelle entrée
              await supabase
                .from('user_ai_credits')
                .insert({
                  user_id: userId,
                  credits: newCredits,
                  created_at: new Date().toISOString(),
                  updated_at: new Date().toISOString()
                });
            }

            // Mettre à jour le cache
            const cacheKey = `ai_credits:${userId}`;
            await redis.setex(cacheKey, 60 * 5, newCredits.toString());

            // Enregistrer l'activité
            await logUserActivity(
              userId,
              'ai_credits_purchased_stripe',
              undefined,
              'ai_credits',
              JSON.stringify({
                credits_purchased: creditsAmount,
                new_credits_balance: newCredits,
                payment_id: session.id
              }),
              'stripe_webhook'
            );

            // Envoyer un email de confirmation
            try {
              const { data: userData, error: userError } = await supabase
                .from('users')
                .select('email')
                .eq('id', userId)
                .single();

              if (!userError && userData) {
                // Déchiffrer les données utilisateur
                const { decryptUserDataAsync } = require('./utils/encryption');
                const decryptedUser = await decryptUserDataAsync(userData);

                // Récupérer le solde actuel des crédits IA
                const { data: aiCreditsData } = await supabase
                  .from('user_ai_credits')
                  .select('credits')
                  .eq('user_id', userId)
                  .single();

                const currentCredits = aiCreditsData?.credits || creditsAmount;
                const oldCredits = currentCredits - creditsAmount;

                // Récupérer les valeurs de configuration pour les crédits IA
                const { default: subscriptions } = require('./config/ConfigSubscriptions');
                const creditsPerPack = subscriptions.gratuit.aiCredits.packs;
                const packQuantity = Math.ceil(creditsAmount / creditsPerPack);

                // Récupérer le prix total depuis les métadonnées de la session
                let totalPrice = parseInt(session.metadata.totalPrice) || 0;

                // Si le prix total n'est pas défini dans les métadonnées, le calculer
                if (totalPrice === 0) {
                  const pricePerPack = subscriptions.gratuit.aiCredits.additionalCost;
                  totalPrice = pricePerPack * packQuantity;
                }

                const { sendAiCreditsEmail } = require('./services/emailAiCredits');
                await sendAiCreditsEmail(decryptedUser.email, creditsAmount, totalPrice, oldCredits, currentCredits, true);

                // Enregistrer dans l'historique des crédits IA
                try {
                  const { logAiCreditsOperation } = require('./controllers/aiCreditsController');
                  await logAiCreditsOperation(
                    userId,
                    'achat_stripe',
                    creditsAmount,
                    oldCredits,
                    currentCredits,
                    `Achat de ${packQuantity} pack${packQuantity > 1 ? 's' : ''} de ${creditsPerPack} crédits IA pour ${totalPrice}€`,
                    session.id,
                    'stripe_webhook'
                  );
                } catch (historyError) {
                  logger.error('Erreur lors de l\'enregistrement de l\'historique des crédits IA:', historyError);
                  // Ne pas bloquer le processus si l'enregistrement de l'historique échoue
                }
              }
            } catch (emailError) {
              logger.error('Erreur lors de l\'envoi de l\'email de confirmation:', emailError);
            }

            logger.info(`${creditsAmount} crédits IA ajoutés avec succès pour l'utilisateur ${userId}`);
          } catch (error) {
            logger.error('Erreur lors de l\'ajout de crédits IA via Stripe:', error);
          }
        }
        break;

      case 'payment_intent.succeeded':
        logger.info(`Paiement réussi: ${event.data.object.id}`);
        break;

      case 'charge.succeeded':
        logger.info(`Charge réussie: ${event.data.object.id}`);
        break;

      case 'billing_portal.session.created':
        logger.info(`Portail client Stripe créé avec succès pour la session: ${event.data.object.id}`);
        break;

      default:
        logger.info(`Événement Stripe reçu mais non traité: ${event.type}`);
    }

    res.json({ received: true });
    return;
  } catch (error: any) {
    console.error('Erreur lors du traitement du webhook Stripe:', error);

    if (error.type === 'StripeSignatureVerificationError') {
      console.error('Détails de l\'erreur de signature:', {
        message: error.message,
        signature: sig ? sig.substring(0, 20) + '...' : 'manquante'
      });
    }

    res.status(400).json({
      success: false,
      message: `Erreur lors du traitement du webhook: ${error.message || 'Erreur inconnue'}`
    });
    return;
  }
});

// Configuration du maxListeners pour éviter les avertissements
// Utilisation d'une valeur dynamique basée sur le nombre d'écouteurs attendus
const EXPECTED_LISTENERS = 50; // Augmentation du nombre d'écouteurs attendus pour Redis
redis.setMaxListeners(EXPECTED_LISTENERS);

// La surveillance des écouteurs Redis est maintenant gérée par node-cron dans cronJobs.ts

// Configuration Redis pour une meilleure gestion des sessions
redis.config('SET', 'maxmemory-policy', 'volatile-ttl')
    .catch((err) => logger.error('Erreur lors de la configuration Redis maxmemory-policy:', err));

redis.config('SET', 'notify-keyspace-events', 'Ex')
    .catch((err) => logger.error('Erreur lors de la configuration Redis notify-keyspace-events:', err));

// Initialisation du détecteur d'activités suspectes avec l'instance singleton
SuspiciousActivityDetector.initialize(redis).catch(error => {
    logSecurity.error(LogEventType.SERVER_ERROR, 'Erreur lors de l\'initialisation du détecteur d\'activités suspectes', {
        error: error instanceof Error ? error.message : 'Unknown error'
    });
});

// Route de santé détaillée - Doit être AVANT les middlewares session et CSRF
app.get('/api/health', async (_req, res) => {
    try {
        // Vérifier la connexion Redis
        const redisStatus = await new Promise((resolve) => {
            redis.ping((err) => {
                resolve(err ? 'unhealthy' : 'healthy');
            });
        });

        const healthStatus = {
            status: redisStatus === 'healthy' ? 'healthy' : 'degraded',
            timestamp: new Date().toISOString(),
            environment: config.env,
            port: config.port,
            redis: {
                status: redisStatus,
                connected: redis.status === 'ready'
            },
            supabase: {
                url: config.supabase.url ? 'configured' : 'not configured',
                serviceRole: config.supabase.serviceRoleKey ? 'configured' : 'not configured'
            },
            requiredEnvVars: {
                PORT: !!config.port,
                JWT_SECRET: !!config.jwt.secret,
                SUPABASE_URL: !!config.supabase.url,
                SUPABASE_KEY: !!config.supabase.serviceRoleKey
            }
        };

        logger.info('Health Check Response', healthStatus);
        res.status(200).json(healthStatus);
        return;
    } catch (error) {
        logger.info('Health Check Failed', {
            error: error instanceof Error ? error.message : 'Unknown error',
            stack: error instanceof Error ? error.stack : undefined
        });
        res.status(500).json({
            status: 'unhealthy',
            error: error instanceof Error ? error.message : 'Unknown error'
        });
        return;
    }
});

// Configuration globale du RedisStore pour les sessions
const sessionStore = new RedisStore({
    client: redis,
    prefix: 'jobpartiel:sess:',
    disableTouch: false,
    ttl: 7 * 24 * 60 * 60, // 7 jours en secondes
    disableTTL: false,
    scanCount: 100
});

// Augmenter la limite de listeners sur RedisStore pour éviter les warnings
sessionStore.setMaxListeners(EXPECTED_LISTENERS);

// Gestion des événements Redis avec des références aux fonctions pour faciliter le nettoyage
const redisEventHandlers = {
    disconnect: () => {
        logger.warn('Déconnecté de Redis - Tentative de reconnexion automatique');
    },
    error: (error: Error) => {
        logger.error('Erreur Redis:', error);
        // Ne pas tenter de reconnecter en cas d'erreur d'authentification
        if (error.message.includes('NOAUTH') || error.message.includes('AUTH')) {
            logger.error('Erreur d\'authentification Redis - Vérifiez vos identifiants');
        }
    }
};

// Attacher les gestionnaires d'événements avec des références nommées
redis.on('disconnect', redisEventHandlers.disconnect);
redis.on('error', redisEventHandlers.error);

// Gestion des erreurs non capturées pour éviter les crashes du serveur
process.on('uncaughtException', (error) => {
    logger.error('Exception non capturée:', error);
    // Ne pas arrêter le serveur, juste logger l'erreur
    // En production, on pourrait vouloir redémarrer le serveur de manière contrôlée
});

process.on('unhandledRejection', (reason, promise) => {
    logger.error('Promesse rejetée non gérée:', { reason, promise });
    // Ne pas arrêter le serveur, juste logger l'erreur
});

// Nettoyer les listeners lors de l'arrêt de l'application
process.on('SIGTERM', () => {
    redis.removeListener('disconnect', redisEventHandlers.disconnect);
    redis.removeListener('error', redisEventHandlers.error);
});

process.on('SIGINT', () => {
    redis.removeListener('disconnect', redisEventHandlers.disconnect);
    redis.removeListener('error', redisEventHandlers.error);
});

// Le nettoyage périodique des sessions Redis expirées est maintenant géré par node-cron dans cronJobs.ts

// Création d'une seule instance du middleware de session
const sessionMiddleware = session({
    store: sessionStore,
    secret: config.session.secret,
    name: 'jobpartiel.sid',
    resave: false,
    saveUninitialized: false,
    rolling: true,
    proxy: config.isProduction,
    unset: 'destroy',
    cookie: {
        secure: config.isProduction,
        httpOnly: true,
        sameSite: config.isProduction ? 'none' : 'lax',
        domain: config.isProduction ? '.jobpartiel.fr' : undefined,
        path: '/',
        maxAge: 7 * 24 * 60 * 60 * 1000  // 7 jours
    }
});

// Application du middleware de session à toutes les routes sauf /api/health
app.use((req, res, next) => {
    // Exclure la route health des sessions
    if (req.path === '/api/health') {
        return next();
    }

    sessionMiddleware(req, res, next);
});

// Log de debug pour la session (désactivé maintenant que le problème CSRF est résolu)
app.use((_req, _res, next) => {
    // Logs désactivés pour éviter de polluer les logs
    // if (config.isDevelopment && req.path.includes('/api/auth/')) {
    //     logger.info('🔍 Session Debug', {
    //         path: req.path,
    //         method: req.method,
    //         sessionID: req.sessionID,
    //         hasSession: !!req.session,
    //         hasCsrfToken: !!req.session?.csrfToken
    //     });
    // }
    next();
});

// Configuration CORS optimisée - Doit être après la session
app.use(cors({
    origin: ['http://localhost:4173', 'http://localhost:5173', 'https://jobpartiel.fr', 'https://www.jobpartiel.fr', 'https://dev.jobpartiel.fr', 'https://dev-api.jobpartiel.fr'], // Ajout de l'origine dev-api
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
    credentials: true,
    allowedHeaders: [
        'Content-Type',
        'Authorization',
        'X-CSRF-Token',
        'X-Requested-With',
        'Accept',
        'Content-Length',
        'Cookie',
        'X-Forwarded-For',
        'X-Forwarded-Proto',
        'X-Forwarded-Host',
        'X-Real-IP'
    ],
    exposedHeaders: ['X-CSRF-Token', 'Set-Cookie'],
    preflightContinue: false,
    optionsSuccessStatus: 204,
    maxAge: 86400 // 24 heures pour réduire les requêtes preflight
}));

// Enable pre-flight requests pour toutes les routes
app.options(/.*/, cors());

// Parsers - Après CORS et session avec optimisations
app.use(express.json({
  limit: '10mb',
  type: ['application/json', 'text/plain']
}));
app.use(express.urlencoded({
  extended: true,
  limit: '10mb',
  parameterLimit: 1000
}));

// Configurer trust proxy et optimisations Express pour HTTP/2
// Configuration trust proxy sécurisée selon l'environnement
if (config.isProduction) {
  app.set('trust proxy', 1); // Premier proxy uniquement en production
} else {
  // En développement, pas de proxy
  app.set('trust proxy', false);
}
app.set('x-powered-by', false); // Désactiver l'en-tête X-Powered-By
app.set('etag', 'strong'); // ETags forts pour un meilleur cache
app.set('view cache', config.isProduction); // Cache des vues en production

// Optimisations pour HTTP/2
app.set('case sensitive routing', false);
app.set('strict routing', false);


// Autres middlewares
app.use(requestLogger);

// Middlewares de sécurité (incluent déjà la protection CSRF)
securityMiddleware.forEach(middleware => app.use(middleware));
app.use(limiter);

// Configuration de express-fileupload
app.use(fileUpload({
    limits: { fileSize: 5 * 1024 * 1024 }, // 5MB max file size
    abortOnLimit: true,
    createParentPath: true,
    useTempFiles: true,
    tempFileDir: '/tmp/'
}));

// Middleware pour vérifier l'expiration du mot de passe sur toutes les routes authentifiées
// app.use('/api/*', checkPasswordExpiration);

// Routes
app.use('/api/auth', authRoutes);
app.use('/api/users', userRoutes);
app.use('/api/email', emailRoutes);
app.use('/api/jobi', jobiRoutes);
app.use('/api/services', servicesRoutes);
app.use('/api/missions', missionsRoutes);
app.use('/api/mission-responses', missionResponsesRoutes);
app.use('/api/subscriptions', configSubscriptionsRouter);
app.use('/api/sirene', sireneRoutes);
app.use('/api/user-badges', badgesRoutes);
app.use('/api/favorites', favoritesRoutes);
app.use('/api/notifications', notificationRoutes);
app.use('/api/bug-reports', bugReportsRoutes);
app.use('/api/support', supportRoutes);
app.use('/api/vps', vpsRoutes);
app.use('/api/messages', messagingRoutes);
app.use('/api/reviews', reviewsRouter);
app.use('/api/payments', paymentsRoutes);
app.use('/api/planning', planningRoutes);
app.use('/api/advanced-stats', advancedStatsRoutes);
app.use('/api/quality-metrics', qualityMetricsRoutes);
app.use('/api/invoices', invoicesRoutes);
app.use('/api/clients', clientRoutes);
app.use('/api/promocodes', promocodesRouter);
app.use('/api/reported-content', reportedContentRoutes);
app.use('/api/admin/redis', redisAdminRouter);
app.use('/api/storage-proxy', supabaseProxyRouter);
app.use('/api/admin/email-queue', emailQueueAdminRouter);
app.use('/api/newsletter', newsletterRouter);
app.use('/api/content-moderation', contentModerationRouter);
app.use('/api/storage', storageRouter);
app.use('/api/ai-generation', aiGenerationRouter);
app.use('/api/ai-image-generation', aiImageGenerationRouter);
app.use('/api/ai-image-stats', aiImageStatsRouter);
app.use('/api/mission-assistant', missionAssistantRouter);
app.use('/api/openrouter', openRouterRouter);
app.use('/api/ai-credits', aiCreditsRouter);
app.use('/api/user/ai-consent', aiConsentRoutes);
app.use('/api/card-editor', cardEditorRouter);
app.use('/api/seo-pages', seoPagesRouter);
app.use('/api/security-monitoring', securityMonitoringRouter);
app.use('/api/admin/user-management', userManagementRoutes);
app.use('/', sitemapRouter);

// Middleware de gestion des erreurs
app.use(errorHandler);

// Gestion des routes non trouvées
app.use((req, res) => {
    logSecurity.warn(LogEventType.API_ERROR, 'Route non trouvée', {
        path: req.path,
        method: req.method
    });
    res.status(404).json({ message: 'Route non trouvée' });
    return;
});

// Middleware d'authentification Socket.IO
io.use((socket, next) => {
  const token = socket.handshake.auth.token;
  if (!token) {
    // logger.warn('Tentative de connexion socket sans token');
    return next(new Error('Authentication error'));
  }
  try {
    const decoded = jwt.verify(token, config.jwt.secret) as JWTPayload;
    socket.data.userId = decoded.userId;
    // logger.info(`Socket authentifié pour l'utilisateur ${decoded.userId}`);
    next();
  } catch (err) {
    // Implémentation du logging debounce
    const now = Date.now();
    const debounceInterval = 10000; // 10 secondes
    if (!lastSocketAuthErrorTime || (now - lastSocketAuthErrorTime > debounceInterval)) {
      logger.error('Erreur d\'authentification socket:', err);
      lastSocketAuthErrorTime = now; // Mise à jour du timestamp
    } else {
      logger.error('Erreur d\'authentification socket (débounced):', (err as Error).message); // Log en debug pour suivi si nécessaire
    }
    next(new Error('Authentication error'));
  }
});

// Variable pour le debounce du logging d'erreur d'authentification socket
let lastSocketAuthErrorTime: number | null = null;

// Définir la limite d'écouteurs pour Socket.IO
io.setMaxListeners(15); // Limite raisonnable pour les connexions socket

// Gestion des connexions Socket.IO avec nettoyage approprié
io.on('connection', (socket) => {
  const userId = socket.data.userId;

//   logger.info(`Nouvelle connexion socket pour l'utilisateur ${userId}, ID socket: ${socket.id}`);

  // Joindre une room spécifique à l'utilisateur
  socket.join(`user_${userId}`);

  // Informer l'utilisateur de sa connexion réussie
  socket.emit('connected', {
    status: 'connected',
    socketId: socket.id,
    userId: userId,
    timestamp: new Date().toISOString()
  });

  // Gérer les erreurs de socket
  socket.on('error', (error) => {
    logger.error(`Erreur socket pour l'utilisateur ${userId}:`, error);
  });

  socket.on('disconnect', (_reason) => {
    // logger.info(`Déconnexion socket pour l'utilisateur ${userId}, raison: ${_reason}`);
    socket.leave(`user_${userId}`);
  });
});

// Exporter io pour l'utiliser dans d'autres fichiers
export { io };

// Exporter également le sessionStore pour permettre le nettoyage des listeners dans les tâches cron
export { sessionStore };

// Initialisation de Passport après la configuration de session
app.use(passport.initialize());
// On ne va pas utiliser passport.session() car on utilise notre propre gestion de session par JWT et cookies

// Démarrage du serveur
httpServer.listen(port, () => {
    // Vider Redis au démarrage du serveur tout en préservant les données importantes
    try {
        // Essayer d'abord dans le répertoire racine (/app en production)
        let flushRedisPath = './flush-redis.js';

        // Si le fichier n'existe pas à cet emplacement, essayer le chemin relatif depuis __dirname
        if (!fs.existsSync(flushRedisPath)) {
            flushRedisPath = path.join(__dirname, '..', 'flush-redis.js');
        }

        logger.info(`Vidage sélectif du cache Redis au démarrage via le script ${flushRedisPath}...`);
        logger.info('Les données importantes (files d\'emails, sessions, etc.) seront préservées');
        execSync(`node ${flushRedisPath} --auto-flush`, { stdio: 'inherit' });
        logger.info('Cache Redis vidé avec succès tout en préservant les données critiques');
    } catch (error) {
        logger.error('Erreur lors du vidage sélectif du cache Redis au démarrage:', error);
    }

    // Initialisation du système de cryptage optimisé (100% asynchrone)
    initializeEncryption().catch((error) => {
        logger.error('Erreur lors de l\'initialisation du système de cryptage:', error);
        process.exit(1); // Arrêter l'application si le cryptage ne peut pas être initialisé
    });

    // Démarrage du service de nettoyage
    cleanupService.performCleanup();

    // Configuration des tâches planifiées (cron jobs)
    // Toutes les tâches planifiées sont maintenant gérées par node-cron
    // y compris les vérifications des offres expirées et le traitement de la file d'attente d'emails
    setupCronJobs();

    logSecurity.info(
        LogEventType.SERVER_START,
        `Serveur démarré en mode ${config.env} sur le port ${config.port}`,
        {
            environment: config.env,
            port: config.port,
            frontendUrl: config.frontendUrl
        }
    );

    if (config.isDevelopment) {
        // Test de connexion SMTP en développement
        const { testSmtpConnection } = require('./services/emailService');
        testSmtpConnection().catch((error: Error) => {
            logSecurity.info(LogEventType.EMAIL_ERROR, 'Erreur lors du test SMTP', {
                error: error.message,
                stack: error.stack
            });
        });

        // Log des interfaces réseau en développement
        const networkInterfaces = require('os').networkInterfaces();
        Object.keys(networkInterfaces).forEach((interfaceName) => {
            networkInterfaces[interfaceName].forEach((details: import('os').NetworkInterfaceInfo) => {
                if (details.family === 'IPv4' && !details.internal) {
                    logSecurity.info(LogEventType.SERVER_START, 'Interface réseau détectée', {
                        interface: interfaceName,
                        address: details.address
                    });
                }
            });
        });
    }
});
export default app;
