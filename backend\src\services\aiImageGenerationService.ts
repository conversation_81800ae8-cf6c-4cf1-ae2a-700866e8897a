import axios from 'axios';
import logger from '../utils/logger';
import { supabase } from '../config/supabase';
import { uploadTemporaryImageForModeration } from './storage';
import crypto from 'crypto';
import { selectAIModel } from '../controllers/openRouterController';
import { logOpenRouterUsage } from './contentModerationService';
import { decryptProfilDataAsync } from '../utils/encryption';

// Fonction pour générer un UUID v4 valide
const generateUniqueId = () => {
  return crypto.randomUUID();
};

// Configuration de l'API de génération d'images
const IMAGE_API_URL = 'https://api.runware.ai/v1';
const IMAGE_API_KEY = process.env.RUNWARE_API_KEY || '';

// Coût en crédits pour générer une image
export const IMAGE_GENERATION_COST = 5;

// Types d'utilisation pour la génération d'images
export type ImageGenerationPurpose = 'profile_picture' | 'banner_picture' | 'mission_image' | 'gallery_photo' | 'featured_photo' | 'card_editor';

// Interface pour les options de génération d'images
export interface ImageGenerationOptions {
  prompt: string;
  width?: number;
  height?: number;
  userId: string;
  purpose: ImageGenerationPurpose;
  storeTemporarily?: boolean; // Si true, l'image sera stockée dans le bucket temporaire
}

/**
 * Génère une image à l'aide de l'API Runware AI
 * Si le prompt n'est pas fourni, il est généré automatiquement via l'IA (OpenRouter) selon le profil utilisateur.
 * @param options Options de génération d'image
 * @returns URL de l'image générée
 */
export const generateImage = async (options: ImageGenerationOptions): Promise<{ imageUrl: string; imageBase64: string }> => {
  try {
    const { prompt, width = 1024, height = 1024, userId, purpose, storeTemporarily = false } = options;

    // Générer un ID unique pour la tâche
    const taskUUID = generateUniqueId();

    // Construire le corps de la requête pour Runware
    const requestBody = [
      {
        taskType: "authentication",
        apiKey: IMAGE_API_KEY
      },
      {
        taskType: "imageInference",
        taskUUID: taskUUID,
        width: width,
        height: height,
        numberResults: 1,
        outputFormat: "JPEG",
        steps: 4,
        CFGScale: 1,
        scheduler: "FlowMatchEulerDiscreteScheduler",
        outputType: [
          "dataURI",
          "URL",
          "base64Data"
        ],
        includeCost: true,
        positivePrompt: prompt,
        model: "runware:100@1"
      }
    ];

    // Log de debug : afficher la requête envoyée
    logger.info('Requête envoyée à Runware:', JSON.stringify(requestBody));

    let response;
    try {
      response = await axios.post(
        IMAGE_API_URL,
        requestBody,
        {
          headers: {
            'Content-Type': 'application/json'
          },
          timeout: 60000 // 60 secondes de timeout
        }
      );
    } catch (axiosError: any) {
      if (axiosError.response) {
        logger.error('Erreur Axios - Réponse reçue:', {
          status: axiosError.response.status,
          headers: axiosError.response.headers,
          data: axiosError.response.data
        });
      } else if (axiosError.request) {
        logger.error('Erreur Axios - Pas de réponse reçue:', axiosError.request);
      } else {
        logger.error('Erreur Axios - Message:', axiosError.message);
      }
      throw new Error('Erreur lors de la requête à l\'API Runware');
    }

    // Fonction utilitaire pour tronquer récursivement les champs volumineux dans les logs
    function truncateLargeFields(obj: any, fields: string[], maxLength: number): any {
      if (Array.isArray(obj)) {
        return obj.map(item => truncateLargeFields(item, fields, maxLength));
      } else if (typeof obj === 'object' && obj !== null) {
        const newObj: any = {};
        for (const key in obj) {
          if (fields.includes(key) && typeof obj[key] === 'string' && obj[key].length > maxLength) {
            newObj[key] = obj[key].substring(0, maxLength) + '... (tronqué)';
          } else {
            newObj[key] = truncateLargeFields(obj[key], fields, maxLength);
          }
        }
        return newObj;
      }
      return obj;
    }

    logger.info('Réponse HTTP Runware limitée à 50 caractères:', {
      status: response.status,
      headers: response.headers,
      data: truncateLargeFields(response.data, ['imageBase64Data', 'base64Data', 'imageDataURI'], 50)
    });

    // Vérifier si la réponse contient une image (robuste)
    if (
      response.data &&
      response.data.data &&
      Array.isArray(response.data.data) &&
      response.data.data.length > 0 &&
      (response.data.data[0].imageURL || response.data.data[0].image_url || response.data.data[0].url)
    ) {
      const imageData = response.data.data[0];
      const imageUrl = imageData.imageURL || imageData.image_url || imageData.url;
      const imageBase64 = imageData.imageBase64Data || imageData.base64Data || '';

      // Enregistrer les statistiques d'utilisation avec l'URL directe de l'image
      await logImageGenerationUsage(userId, purpose, prompt, imageData.cost || 0, imageData.imageUUID || '', imageUrl);

      // Télécharger l'image et la stocker dans le bucket Supabase approprié
      let storedImageUrl = '';

      try {
        // Convertir le base64 en buffer
        const base64Data = imageBase64.replace(/^data:image\/\w+;base64,/, '');
        const buffer = Buffer.from(base64Data, 'base64');

        // TOUJOURS stocker dans le bucket temporaire, peu importe le purpose
        // Ne jamais ajouter le tag IA pour les images de type card_editor
        const addIaTag = purpose !== 'card_editor';

        // Gestion d'erreur robuste pour éviter les crashes du serveur
        let result;
        try {
          result = await uploadTemporaryImageForModeration(userId, buffer, 'image/jpeg', addIaTag);
          storedImageUrl = result.publicUrl;
        } catch (uploadError: any) {
          logger.error('Erreur lors de l\'upload temporaire, tentative sans traitement d\'image:', uploadError);
          // Fallback : essayer sans traitement d'image si Sharp échoue
          result = await uploadTemporaryImageForModeration(userId, buffer, 'image/jpeg', false);
          storedImageUrl = result.publicUrl;
        }

        logger.info('Image IA stockée temporairement pour modération:', {
          userId,
          purpose,
          filePath: result.filePath
        });

        logger.info('Image IA stockée avec succès dans Supabase:', {
          userId,
          purpose,
          originalUrl: imageUrl,
          storedUrl: storedImageUrl
        });

        // Retourner l'URL stockée et le base64 original
        return {
          imageUrl: storedImageUrl,
          imageBase64
        };
      } catch (storageError) {
        logger.error('Erreur lors du stockage de l\'image IA dans Supabase:', storageError);
        // En cas d'erreur de stockage, on retourne quand même l'URL et le base64 originaux
        return {
          imageUrl,
          imageBase64
        };
      }
    } else {
      // Journaliser la réponse pour le débogage
      logger.error('Réponse de l\'API Runware sans image (détail):', JSON.stringify(response.data));
      // Si la réponse contient un message d'erreur, l'afficher
      if (response.data && response.data.data && response.data.data[0] && response.data.data[0].error) {
        logger.error('Erreur Runware:', response.data.data[0].error);
      }
      throw new Error('Aucune image n\'a été générée par l\'API');
    }
  } catch (error: any) {
    logger.error('Erreur lors de la génération d\'image IA:', error);
    throw new Error(`Erreur lors de la génération d'image: ${error.message}`);
  }
};

/**
 * Enregistre les statistiques d'utilisation de la génération d'images
 * @param userId ID de l'utilisateur
 * @param purpose Objectif de la génération d'image
 * @param prompt Prompt utilisé pour la génération
 * @param cost Coût de la génération
 * @param imageId ID de l'image générée
 * @param directImageUrl URL directe de l'image fournie par l'API Runware
 */
export const logImageGenerationUsage = async (
  userId: string,
  purpose: ImageGenerationPurpose,
  prompt: string,
  cost: number,
  imageId: string,
  directImageUrl?: string
): Promise<void> => {
  try {
    // Vérifier si la table existe
    const { error: checkError } = await supabase
      .from('ai_image_generation_stats')
      .select('*', { count: 'exact', head: true });

    // Si la table n'existe pas, on log l'erreur mais on ne bloque pas le processus
    if (checkError && checkError.code === 'PGRST116') {
      logger.error('La table ai_image_generation_stats n\'existe pas encore. Veuillez exécuter la migration.');
      return;
    }

    // Insérer les données d'utilisation dans la base de données
    const { error } = await supabase
      .from('ai_image_generation_stats')
      .insert({
        user_id: userId,
        purpose: purpose,
        prompt: prompt,
        cost: cost,
        image_id: imageId,
        // Utiliser l'URL directe de l'API si disponible, sinon construire l'URL du proxy
        image_url: directImageUrl || (imageId ? `${process.env.API_URL || 'http://localhost:3001'}/api/storage-proxy/temp_moderation/${imageId}` : null),
        created_at: new Date().toISOString()
      });

    if (error) {
      logger.error('Erreur lors de l\'enregistrement des statistiques de génération d\'image:', error);

      // Log détaillé pour le débogage
      logger.error('Détails de l\'erreur:', {
        code: error.code,
        message: error.message,
        details: error.details,
        hint: error.hint,
        timestamp: new Date().toISOString()
      });
    }
  } catch (error) {
    logger.error('Erreur lors de l\'enregistrement des statistiques de génération d\'image:', error);

    // Log détaillé pour le débogage
    if (error instanceof Error) {
      logger.error('Détails de l\'exception:', {
        name: error.name,
        message: error.message,
        stack: error.stack,
        timestamp: new Date().toISOString()
      });
    } else {
      logger.error('Erreur inconnue:', {
        error,
        timestamp: new Date().toISOString()
      });
    }
  }
};

/**
 * Génère un prompt contextuel simplifié basé sur les données du profil utilisateur.
 * Le prompt ne fait que rassembler les informations clés (métier, prénom, ville, description, style visuel, environnement, outils, etc.)
 * Chaque info est sur une ligne, sans rédaction longue, pour laisser OpenRouter faire la rédaction créative.
 * @param userId ID de l'utilisateur
 * @param purpose Objectif de la génération d'image
 * @param galleryId ID de la galerie (facultatif)
 * @param galleryName Nom de la galerie (facultatif)
 * @param galleryDescription Description de la galerie (facultatif)
 * @param missionTitle Titre de la mission (facultatif)
 * @param missionDescription Description de la mission (facultatif)
 * @returns Prompt contextuel simplifié
 */
export const generateContextualPrompt = async (
  userId: string,
  purpose: ImageGenerationPurpose,
  galleryId?: string,
  galleryName?: string,
  galleryDescription?: string,
  missionTitle?: string,
  missionDescription?: string
): Promise<string> => {
  logger.info('Génération d\'un prompt contextuel SIMPLIFIÉ avec informations du profil utilisateur');
  try {
    // Récupérer les données du profil utilisateur
    const { data: userProfile, error: profileError } = await supabase
      .from('user_profil')
      .select('bio, ville, pays, code_postal, type_de_profil, mode_vacance, prenom')
      .eq('user_id', userId)
      .single();

    if (profileError) {
      logger.error('Erreur lors de la récupération du profil utilisateur:', profileError);
      return getDefaultPrompt(purpose);
    }

    // Déchiffrer les données sensibles du profil
    const decryptedProfile = await decryptProfilDataAsync(userProfile);

    // Récupérer les services de l'utilisateur
    const { data: userServices, error: servicesError } = await supabase
      .from('user_services')
      .select('titre, description, categorie')
      .eq('user_id', userId)
      .eq('statut', 'actif');

    if (servicesError) {
      logger.error('Erreur lors de la récupération des services utilisateur:', servicesError);
    }

    // Préparation des éléments clés (tout sur une ligne, pas de rédaction longue)
    let lines = [];
    if (decryptedProfile.prenom) lines.push(`Prénom : ${decryptedProfile.prenom}`);
    if (decryptedProfile.ville) lines.push(`Ville : ${decryptedProfile.ville}`);
    if (decryptedProfile.pays) lines.push(`Pays : ${decryptedProfile.pays}`);
    if (decryptedProfile.code_postal) lines.push(`Code postal : ${decryptedProfile.code_postal}`);
    if (decryptedProfile.type_de_profil) lines.push(`Type de profil : ${decryptedProfile.type_de_profil}`);
    if (decryptedProfile.mode_vacance) lines.push(`Mode vacances : activé`);
    if (decryptedProfile.bio) lines.push(`Description : ${decryptedProfile.bio.replace(/<[^>]+>/g, '').trim()}`);

    if (userServices && userServices.length > 0) {
      lines.push(`Services proposés : ${userServices.map(s => s.titre).join(', ')}`);
      const descriptions = userServices.map(s => s.description).filter(Boolean).join(' ');
      if (descriptions) lines.push(`Détail des services : ${descriptions}`);
      const categories = userServices.map(s => s.categorie).filter(Boolean).join(', ');
      if (categories) lines.push(`Catégories de services : ${categories}`);
    }

    if (purpose === 'gallery_photo') {
      // On vide d'abord le tableau
      lines.length = 0;
      if (galleryName) lines.push(`Nom de la galerie : ${galleryName}`);
      if (galleryDescription) lines.push(`Description de la galerie : ${galleryDescription.replace(/<[^>]+>/g, '').trim()}`);
      lines.push(`Consigne : L'image doit absolument respecter le nom et la description de la galerie ci-dessus, sans interprétation libre.`);
      lines.push(`Objectif de la galerie : Mettre en avant la créativité, la qualité et l'originalité des réalisations présentées dans cette galerie.`);
      lines.push(`Contexte : Cette galerie est destinée à inspirer les clients potentiels et à valoriser le savoir-faire unique de l'utilisateur dans la création de services personnalisés.`);
    }

    // --- Ajout pour les missions ---
    if (purpose === 'mission_image') {
      lines.length = 0;
      // Utiliser missionTitle/missionDescription si fournis, sinon fallback sur galleryName/galleryDescription
      const title = missionTitle || galleryName;
      const desc = missionDescription || galleryDescription;
      if (title) lines.push(`Titre de la mission : ${title}`);
      if (desc) lines.push(`Description de la mission : ${desc.replace(/<[^>]+>/g, '').trim()}`);
      lines.push(`Consigne : L'image doit absolument illustrer la mission décrite ci-dessus, sans interprétation libre.`);
      lines.push(`Objectif : Mettre en avant le contexte, l'objectif et l'ambiance de la mission pour inspirer confiance et donner envie de postuler.`);
    }

    if (purpose === 'card_editor') {
      // Prompt universel, résultat aléatoire à chaque génération
      let secteur = '';
      if (userServices && userServices.length > 0) {
        secteur = userServices.map(s => s.categorie).filter(Boolean).join(', ');
      }
      let services = '';
      if (userServices && userServices.length > 0) {
        services = userServices.map(s => s.titre).filter(Boolean).join(', ');
      }
      const support = userProfile.type_de_profil === 'pro' ? 'carte de visite professionnelle' : 'flyer grand public';

      // Génération de paramètres aléatoires pour rendre chaque prompt unique
      const stylesArtistiques = ['vectoriel minimaliste', 'aquarelle stylisée', '3D isométrique', 'flat design', 'illustration ligne claire', 'gravure vintage', 'néon lumineux', 'paper cut art', 'pixel art', 'doodle art'];
      const compositions = ['centrée avec symétrie radiale', 'asymétrique dynamique', 'motif répétitif géométrique', 'disposition en diagonale', 'composition en cercle', 'arrangement en grille'];
      const eclairages = ['dramatique avec ombres marquées', 'diffus et enveloppant', 'éclat latéral définissant les contours', 'ambiant doux', 'high-key brillant', 'low-key mystérieux'];
      const textures = ['minimaliste pur', 'papier texturé', 'motif géométrique léger', 'gradient doux', 'texture technique discrète'];

      // Sélection aléatoire d'éléments
      const styleChoisi = stylesArtistiques[Math.floor(Math.random() * stylesArtistiques.length)];
      const compositionChoisie = compositions[Math.floor(Math.random() * compositions.length)];
      const eclairageChoisi = eclairages[Math.floor(Math.random() * eclairages.length)];
      const textureChoisie = textures[Math.floor(Math.random() * textures.length)];
      const iconeStyle = Math.random() > 0.5 ? 'multiples icônes formant une composition' : 'icône unique dominante au centre';
      const detailLevel = Math.random() > 0.5 ? 'ultra-détaillé' : 'élégamment minimaliste';

      let prompt = `Contexte : Générer une illustration ou composition visuelle pour une ${support} destinée à un professionnel du secteur ${secteur ? secteur : 'service à domicile'}${services ? ' : ' + services : ''}.`;
      prompt += `\nConsigne : L'image doit représenter de façon évidente l'activité du client, uniquement à travers des pictogrammes ou icônes en rapport direct avec le métier ou le service.`;
      prompt += `\nStyle artistique : ${styleChoisi}`;
      prompt += `\nComposition : ${compositionChoisie}`;
      prompt += `\nÉclairage : ${eclairageChoisi}`;
      prompt += `\nArrière-plan : ${textureChoisie}`;
      prompt += `\nIcônes : ${iconeStyle}`;
      prompt += `\nNiveau de détail : ${detailLevel}`;
      prompt += `\nPalette : Intégrer subtilement les couleurs de la marque (#FF6B2C, #FF7A35, #FF965E, #FFF8F3, #FFE4BA) et les combiner avec une palette complémentaire aléatoire pour un résultat visuel dynamique et harmonieux. L'accent principal reste sur l'identification claire du métier via les icônes.`;
      prompt += `\nRestrictive : Strictement aucun texte, personnage, photographie, zone de texte, ou maquette de support. Uniquement des éléments graphiques permettant d'identifier immédiatement le métier.`;
      prompt += `\nInterdiction absolue : Il est strictement interdit d'afficher ou d'intégrer tout texte tel que 'image générée par IA', 'jobpartiel.fr', nom de site, watermark, signature, ou toute mention textuelle, explicite ou cachée, sur l'image. L'image doit être 100% sans texte, sans logo, sans watermark, sans url.`;

      lines = [prompt];
    }

    if (purpose === 'banner_picture') {
      // On vide d'abord le tableau
      lines.length = 0;

      const profileType = userProfile.type_de_profil || 'professionnel';
      const servicesList = userServices && userServices.length > 0
        ? userServices.map(s => s.titre).join(', ')
        : 'services à domicile';
      const location = userProfile.ville && userProfile.pays ? `${userProfile.ville}, ${userProfile.pays}` : userProfile.ville || userProfile.pays || '';
      const bioShort = userProfile.bio ? userProfile.bio.replace(/<[^>]+>/g, '').substring(0, 350).trim() + '...' : ''; // Limiter la bio pour le prompt

      let prompt = `Créer une image de bannière professionnelle (ratio 16:9) ultra-réaliste 8K pour le profil d'un ${profileType} expert en ${servicesList}.`;

      if (location) {
        prompt += ` La scène se déroule dans un environnement typique de ${servicesList} à ${location}.`;
      } else {
        prompt += ` Montrer ce professionnel en pleine action, dans un cadre qui évoque ses compétences et services.`;
      }

      prompt += ` Mettre en avant l'expertise, le professionnalisme et la qualité du travail.`;

      if (bioShort) {
         prompt += ` L'image doit capturer l'essence de cette description : "${bioShort}".`;
      }

      prompt += ` Vue cinématique grand angle, éclairage professionnel, composition dynamique.`;

      lines = [prompt];
    }

    // Prompt final simplifié
    const contextualPrompt = lines.join('\n');
    return contextualPrompt || getDefaultPrompt(purpose);
  } catch (error) {
    logger.error('Erreur lors de la génération du prompt contextuel simplifié:', error);
    return getDefaultPrompt(purpose);
  }
};

/**
 * Retourne un prompt par défaut selon le type d'image à générer
 * @param purpose Objectif de la génération d'image
 * @returns Prompt par défaut
 */
const getDefaultPrompt = (purpose: ImageGenerationPurpose): string => {
  // Obtenir la saison actuelle pour contextualiser les images
  const getSeasonContext = (): string => {
    const month = new Date().getMonth();
    if (month >= 5 && month <= 8) return 'Lumière estivale chaude et naturelle, tons dorés, ambiance dynamique et énergique';
    if (month >= 9 && month <= 11) return 'Lumière automnale dorée et tamisée, tons chauds orangés et bruns, ambiance chaleureuse';
    if (month >= 0 && month <= 2) return 'Lumière hivernale douce, tons bleus-gris subtils, ambiance professionnelle et confortable';
    return 'Lumière printanière fraîche, tons verts et pastels, ambiance de renouveau et de dynamisme';
  };

  // Palette de couleurs de la marque pour harmonisation
  const colorPalette = 'Palette de couleurs harmonieuse intégrant subtilement les tons #FF6B2C (primaire), #FF7A35 (secondaire), #FF965E (tertiaire), fond #FFF8F3 avec accents #FFE4BA';

  // Contexte saisonnier
  const seasonContext = getSeasonContext();

  logger.info('Aucun prompt trouvé pour la génération d\'image, on utilise un par defaut');

  switch (purpose) {
    case 'profile_picture':
      return `Portrait professionnel hyper-réaliste 8K ultra-détaillé d'une personne experte en services à domicile. ${seasonContext}.
      Éclairage studio Rembrandt professionnel avec lumière latérale douce créant des ombres modelantes sur le visage, arrière-plan bokeh subtil aux tons chauds et accueillants, profondeur de champ f/2.8,
      proportions visage parfaites avec regard direct et engageant établissant une connexion immédiate, tenue professionnelle soignée reflétant le secteur d'activité,
      composition suivant précisément la règle des tiers avec point focal sur les yeux, netteté exceptionnelle 8K sur le visage révélant des micro-détails naturels,
      rendu de peau naturel évitant tout effet artificiel, expression faciale confiante, avenante et professionnelle inspirant confiance.
      ${colorPalette}. Style photographique contemporain haut de gamme de type portrait corporate LinkedIn premium.`;

    case 'banner_picture':
      return `Bannière de profil professionnelle ultra-réaliste 8K détaillant un professionnel de services à domicile en pleine action. ${seasonContext}.
      Vue cinématique grand angle captant l'environnement de travail dans son ensemble, ratio 16:9 pour un rendu moderne,
      profondeur de champ moyenne (f/5.6) équilibrant netteté du sujet et contexte environnant, éclairage naturel provenant des fenêtres combiné à un remplissage doux éliminant les ombres trop marquées,
      ${colorPalette}, premier plan mettant en valeur des outils professionnels spécifiques au métier utilisés avec expertise,
      environnement de travail impeccablement rangé et organisé reflétant un professionnalisme sans faille,
      mouvement naturel capturé au moment décisif montrant le savoir-faire, perspective légèrement surélevée offrant une vue englobante et valorisante de la scène,
      composition dynamique suivant la règle des tiers avec lignes directrices guidant le regard vers l'action principale,
      flou cinétique subtil sur les éléments en mouvement renforçant l'impression d'action. Style photojournalisme documentaire moderne haute définition.`;

    case 'mission_image':
      return `Scène immersive ultra-réaliste 8K détaillant un professionnel de services à domicile en pleine action. ${seasonContext}.
      Vue cinématique grand angle captant l'environnement de travail dans son ensemble, ratio 16:9 pour un rendu moderne,
      profondeur de champ moyenne (f/5.6) équilibrant netteté du sujet et contexte environnant, éclairage naturel provenant des fenêtres combiné à un remplissage doux éliminant les ombres trop marquées,
      ${colorPalette}, premier plan mettant en valeur des outils professionnels spécifiques au métier utilisés avec expertise,
      environnement de travail impeccablement rangé et organisé reflétant un professionnalisme sans faille,
      mouvement naturel capturé au moment décisif montrant le savoir-faire, perspective légèrement surélevée offrant une vue englobante et valorisante de la scène,
      composition dynamique suivant la règle des tiers avec lignes directrices guidant le regard vers l'action principale,
      flou cinétique subtil sur les éléments en mouvement renforçant l'impression d'action. Style photojournalisme documentaire moderne haute définition.`;

    case 'gallery_photo':
      return `Photographie commerciale ultra-détaillée 8K d'une réalisation exceptionnelle dans le domaine du service à domicile. ${seasonContext}.
      Plan large permettant d'apprécier l'ampleur et la qualité du travail accompli, technique d'éclairage à trois points (key, fill, rim) avec accent particulier sur les détails techniques du travail réalisé,
      profondeur de champ f/8 optimisée pour maximiser la netteté sur toute la zone d'intérêt sans perdre l'effet de profondeur,
      couleurs vives légèrement saturées (110%) mettant en valeur la qualité et la finition du travail, contraste marqué mais naturel (ratio 7:1) pour définition optimale,
      composition centrée sur le résultat final avec lignes directrices menant aux points d'intérêt technique, micro-textures parfaitement visibles révélant la qualité d'exécution,
      reflets et brillances parfaitement maîtrisés pour mettre en valeur les surfaces et finitions, ${colorPalette}.
      Rendu final type magazine spécialisé haut de gamme, évoquant instantanément l'expertise absolue et l'excellence technique. Qualité photographique professionnelle digne des meilleurs portfolios.`;

    case 'featured_photo':
      return `Image signature ultra-premium 8K destinée à représenter un expert reconnu en services à domicile. ${seasonContext}.
      Plan américain ou mi-corps valorisant permettant d'inclure la gestuelle professionnelle tout en capturant l'expression du visage,
      éclairage Paramount (papillon) créant un éclairage flatteur avec rim light subtil pour définir la silhouette et la détacher du fond,
      arrière-plan contextualisé légèrement flouté (f/4) montrant l'environnement de travail professionnel sans distraire du sujet principal,
      décor élégant, organisé et stratégiquement arrangé reflétant l'excellence professionnelle, ${colorPalette},
      tenue professionnelle impeccable adaptée au secteur d'activité spécifique, accessoires et outils du métier stratégiquement placés pour une composition équilibrée sans paraître artificielle,
      posture corporelle assurée, ouverte et dynamique évoquant l'expertise et l'accessibilité, regard direct et confiant créant une connexion immédiate,
      sourire sincère et professionnel inspirant confiance. Rendu final type couverture de magazine professionnel ou profil business premium,
      qualité publicitaire haut de gamme employant les techniques photographiques des meilleures campagnes corporate.`;

    case 'card_editor':
      return `Image professionnelle ultra-détaillée 8K pour carte de visite ou flyer. ${seasonContext}.
      Style artistique variable (choisir parmi : vectoriel minimaliste, aquarelle stylisée, 3D isométrique, flat design moderne, illustration ligne claire, gravure vintage, néon lumineux, paper cut art, pixel art haute qualité, doodle art professionnel).
      Variation aléatoire de composition (choisir parmi : centrée avec symétrie radiale, asymétrique dynamique, motif répétitif géométrique, disposition en diagonale, composition en cercle, arrangement en grille moderne).
      Palette de couleurs (mélanger : tons #FF6B2C primaire, #FF7A35 secondaire, #FF965E tertiaire, fond #FFF8F3, accents #FFE4BA et choisir une palette complémentaire aléatoire harmonieuse).
      Modulation aléatoire de l'éclairage (choisir parmi : dramatique avec ombres marquées, lumière diffuse enveloppante, éclat latéral définissant les contours, illumination ambiante douce, contraste high-key, style low-key mystérieux).
      Échelle d'icônes variable (choisir entre un motif d'icônes multiples ou une seule icône dominante centrale).
      Texture d'arrière-plan variable (choisir parmi : minimaliste pur, subtile papier texturé, motif géométrique léger, gradient doux, texture technique discrète).
      Obligatoirement inclure des pictogrammes ou icônes symboliques représentant clairement et spécifiquement le secteur d'activité sans texte.
      Éléments visuels thématiques obligatoires en rapport direct avec le métier (outils spécifiques, objets représentatifs, symboles universels du secteur).
      Sans aucun texte, personnage ou élément photographique - uniquement des éléments graphiques.
      Composition adaptée à l'impression professionnelle avec marge de sécurité de 3mm.
      Niveau de détail variable (choisir entre ultra-détaillé ou épuré minimaliste).
      Rendu final dynamique générant à chaque fois un résultat visuellement différent tout en conservant le thème professionnel et l'identification immédiate du secteur d'activité.
      La variation des paramètres ci-dessus DOIT créer des résultats très différents à chaque génération tout en restant cohérents avec l'activité professionnelle représentée.`;

    default:
      return `Image professionnelle ultra-réaliste 8K pour un service à domicile de qualité supérieure. ${seasonContext}.
      Composition parfaitement équilibrée suivant la règle des tiers avec point focal clair et lignes directrices subtiles,
      éclairage professionnel trois points (principale, remplissage, contre-jour) créant volume et profondeur sans ombres distractives,
      couleurs vibrantes mais naturelles avec saturation maîtrisée (105%), contraste dynamique mais équilibré (ratio 5:1),
      détails nets jusqu'aux micro-textures révélant la qualité du travail, profondeur visuelle créée par stratification des plans,
      atmosphère accueillante et professionnelle inspirant confiance immédiate, ${colorPalette},
      style photographique contemporain évitant les filtres tendance pour une image intemporelle, qualité magazine haut de gamme,
      mise au point précise sur les éléments techniques importants. Rendu final de qualité professionnelle irréprochable à forte valeur perçue.`;
  }
};


/**
 * Génère un prompt Midjourney en anglais via OpenRouter à partir du contexte utilisateur (profil, métier, etc.) ou d'un prompt source fourni.
 * Utilise generateContextualPrompt pour obtenir le contexte riche, ou un prompt source si fourni, puis demande à OpenRouter de formater le prompt Midjourney.
 * Gère le fallback 429 avec la clé payante et le modèle payant si défini. Log l'usage si possible.
 * @param userId ID de l'utilisateur
 * @param purpose Objectif de la génération d'image (profile_picture, mission_image, etc.)
 * @param model (optionnel) Modèle à utiliser (par défaut : modèle gratuit)
 * @param sourcePrompt (optionnel) Prompt source à convertir/optimiser (si fourni, utilisé à la place du contexte généré)
 * @returns Prompt Midjourney en anglais prêt à l'emploi
 */
export async function generateMidjourneyPromptViaOpenRouter(
  userId: string,
  purpose: ImageGenerationPurpose,
  model?: string,
  sourcePrompt?: string
): Promise<string> {
  logger.info('Génération du prompt Midjourney via OpenRouter');

  // 1. Utiliser le prompt source si fourni, sinon générer le contexte riche en français
  const context = sourcePrompt && sourcePrompt.trim().length > 0
    ? sourcePrompt
    : await generateContextualPrompt(userId, purpose);

  // 2. Construire la consigne pour OpenRouter
  const instruction = `Voici un contexte utilisateur pour une image professionnelle :\n${context}\n\nGénère un prompt Midjourney en anglais (~100 mots), détaillé, structuré, pour représenter visuellement ce profil métier. Inclue toujours '--v 6 --ar 16:9' à la fin.`;

  // 3. Préparer l'appel API harmonisé
  const MODERATION_API_URL = process.env.MODERATION_API_URL || 'https://api.openrouter.ai/api/v1/chat/completions';
  const MODERATION_API_KEY = process.env.MODERATION_API_KEY || '';
  const TIMEOUT_API = 15000;

  // Sélection dynamique du modèle si non fourni
  let AI_API_MODEL = model;
  if (!AI_API_MODEL) {
    AI_API_MODEL = await selectAIModel(false); // false = texte
  }
  let usedFallback = false;
  let response;

  const getAIPayload = (model: string) => ({
    model,
    messages: [
      {
        role: "system",
        content: "Tu es un expert en prompts Midjourney. Tu ne réponds qu'avec le prompt Midjourney final, sans explication."
      },
      {
        role: "user",
        content: instruction
      }
    ],
    temperature: 0.7,
    top_p: 0.9,
    frequency_penalty: 0.0,
    presence_penalty: 0.0,
    max_tokens: 400
  });

  const axiosConfig = {
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${MODERATION_API_KEY}`,
      'HTTP-Referer': 'https://jobpartiel.fr',
      'X-Title': 'JobPartiel Midjourney Prompt Generator'
    },
    timeout: TIMEOUT_API
  };

  try {
    response = await axios.post(
      MODERATION_API_URL,
      getAIPayload(AI_API_MODEL),
      axiosConfig
    );

    const promptGen = response.data.choices[0].message.content;
    logger.info('Prompt Midjourney généré avec succès via OpenRouter :\n' + promptGen);

    // Log l'usage dans openrouter_api_usage pour stats admin
    if (response.data.usage) {
      await logOpenRouterUsage(
        userId,
        'midjourney_prompt',
        AI_API_MODEL,
        response.data.usage.prompt_tokens || 0,
        response.data.usage.completion_tokens || 0,
        response.data.usage.total_tokens || 0,
        response.data.id || null
      );
    }

  } catch (err: any) {
    // Si erreur 429 ou 400, on tente une fois avec le modèle payant
    if (err.response && (err.response.status === 429 || err.response.status === 400) && !usedFallback) {
      logger.warn(`Erreur ${err.response.status} détectée, tentative avec le modèle payant.`);
      AI_API_MODEL = await selectAIModel(false); // On rappelle pour obtenir le modèle payant si la logique interne le permet
      usedFallback = true;
      try {
        response = await axios.post(
          MODERATION_API_URL,
          getAIPayload(AI_API_MODEL),
          axiosConfig
        );
      } catch (err2) {
        logger.error('Erreur lors du fallback payant OpenRouter:', err2);
        return '';
      }
    } else {
      logger.error('Erreur lors de la génération du prompt Midjourney via OpenRouter:', err);
      return '';
    }
  }

  // Extraire le prompt généré
  const prompt = response?.data?.choices && response.data.choices[0] && response.data.choices[0].message && response.data.choices[0].message.content
    ? response.data.choices[0].message.content.trim()
    : '';
  return prompt;
}
