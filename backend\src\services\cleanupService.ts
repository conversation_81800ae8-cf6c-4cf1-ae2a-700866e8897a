import { SuspiciousActivityDetector } from './suspiciousActivityDetector';
import { TokenBlacklist } from './tokenBlacklist';
import { SessionManager } from './sessionManager';
import { logSecurityEvent } from '../utils/logger';
import { SecurityEvent } from '../middleware/security';
import { redis } from '../config/redis';
import logger from '../utils/logger';
import { cleanupOldAttachments } from '../controllers/supportTicketAttachments';
import { cleanupOldMessageAttachments } from './storage';
import { supabase } from '../config/supabase';
import { checkAndApplyScheduledDowngrades } from '../routes/configSubscriptions';
import { sendProfilExpirationWarningEmail, sendProfilInvalidatedEmail } from './emailServiceModeration';
import { decryptProfilDataAsync } from '../utils/encryption';

class CleanupService {
    private static instance: CleanupService;

    private constructor() { }

    static getInstance(): CleanupService {
        if (!CleanupService.instance) {
            CleanupService.instance = new CleanupService();
        }
        return CleanupService.instance;
    }

    /**
     * Nettoyage au démarrage du serveur
     */
    async performCleanup() {
        const securityEvent: SecurityEvent = {
            type: 'cleanup_service',
            message: 'Cleanup service initiated.',
            action: 'cleanup',
            status: 'success',
            ip: '127.0.0.1'
        };
        logSecurityEvent(securityEvent);

        // Nettoyage des tokens révoqués
        await TokenBlacklist.cleanup();

        // Nettoyage des sessions expirées
        await SessionManager.cleanup();

        // Nettoyage de l'activité suspecte
        await SuspiciousActivityDetector.cleanup();
        
        // Nettoyage des anciennes pièces jointes des tickets de support
        try {
            logger.info('Début du nettoyage des pièces jointes anciennes des tickets de support');
            await cleanupOldAttachments();
            logger.info('Nettoyage des pièces jointes anciennes des tickets de support terminé');
        } catch (error) {
            logger.error('Erreur lors du nettoyage des pièces jointes anciennes des tickets de support:', error);
        }

        // Nettoyage des pièces jointes de messages expirées
        try {
            logger.info('Début du nettoyage des pièces jointes de messages expirées');
            await cleanupOldMessageAttachments();
            logger.info('Nettoyage des pièces jointes de messages expirées terminé');
        } catch (error) {
            logger.error('Erreur lors du nettoyage des pièces jointes de messages expirées:', error);
        }
        
        // Nettoyage des conversations avec trop de messages
        try {
            logger.info('Début du nettoyage des conversations dépassant la limite de messages de 150');
            await this.cleanupConversationsExceedingLimit();
            logger.info('Nettoyage des conversations dépassant la limite de messages terminé');
        } catch (error) {
            logger.error('Erreur lors du nettoyage des conversations dépassant la limite de messages:', error);
        }
        
        // Nettoyage des anciens timestamps de bug reports
        await this.cleanupOldBugReportTimestamps();
        
        // Nettoyage des anciens rappels d'emails
        await this.cleanupOldEmailReminders();
       
        // Vérifier et appliquer les downgrades d'abonnements planifiés
        await this.cleanupScheduledSubscriptionDowngrades();
        
        // --- Rappel 7 jours avant le renouvellement automatique ---
        const { send7DaysBeforeRenewalReminder } = require('../routes/configSubscriptions');
        await send7DaysBeforeRenewalReminder();        

        // Invalidation automatique des profils vérifiés expirés
        await this.invalidateExpiredVerifiedProfiles();

        // Planification du nettoyage périodique (1 fois par jour)
        this.schedulePeriodicCleanup();
    }
    
    /**
     * Nettoie les conversations qui dépassent la limite de 150 messages
     */
    async cleanupConversationsExceedingLimit() {
        const MAX_MESSAGES = 150;
        const WARNING_MESSAGE = "⚠️ Attention : Cette conversation est limitée à 150 messages. Les messages les plus anciens seront automatiquement supprimés.";
        const BATCH_SIZE = 20; // Nombre de conversations à traiter par lot

        try {
            logger.info('Début du nettoyage des conversations dépassant la limite de messages');
            
            // 1. Récupérer toutes les conversations
            let from = 0;
            let hasMore = true;
            let cleanedConversations = 0;
            let totalMessagesDeleted = 0;
            
            while (hasMore) {
                // Récupérer un lot de conversations
                const { data: conversations, error } = await supabase
                    .from('user_messages_conversations')
                    .select('id, total_messages')
                    .range(from, from + BATCH_SIZE - 1);
                
                if (error) {
                    logger.error(`Erreur lors de la récupération des conversations: ${error.message}`);
                    break;
                }
                
                if (!conversations || conversations.length === 0) {
                    hasMore = false;
                    break;
                }
                
                // 2. Pour chaque conversation, vérifier si elle dépasse la limite
                for (const conversation of conversations) {
                    if (!conversation.total_messages || conversation.total_messages <= MAX_MESSAGES) {
                        continue; // Passer à la conversation suivante si elle ne dépasse pas la limite
                    }
                    
                    try {
                        // Compter le nombre réel de messages (au cas où total_messages est incorrect)
                        const { count, error: countError } = await supabase
                            .from('user_messages')
                            .select('*', { count: 'exact', head: true })
                            .eq('conversation_id', conversation.id);
                        
                        if (countError) {
                            logger.error(`Erreur lors du comptage des messages pour la conversation ${conversation.id}: ${countError.message}`);
                            continue;
                        }
                        
                        if (!count || count <= MAX_MESSAGES) {
                            // Mettre à jour le total_messages si nécessaire
                            if (conversation.total_messages !== count) {
                                await supabase
                                    .from('user_messages_conversations')
                                    .update({ total_messages: count })
                                    .eq('id', conversation.id);
                                
                                logger.info(`Correction du compteur total_messages pour la conversation ${conversation.id}: ${conversation.total_messages} → ${count}`);
                            }
                            continue;
                        }
                        
                        // Calculer combien de messages doivent être supprimés
                        const messagesToDelete = count - MAX_MESSAGES;
                        
                        // Récupérer les messages à supprimer (les plus anciens d'abord, en excluant le message d'avertissement)
                        const { data: oldMessages, error: messagesError } = await supabase
                            .from('user_messages')
                            .select('id, content')
                            .eq('conversation_id', conversation.id)
                            .neq('content', WARNING_MESSAGE) // Exclure le message d'avertissement
                            .order('created_at', { ascending: true })
                            .limit(messagesToDelete);
                        
                        if (messagesError || !oldMessages || oldMessages.length === 0) {
                            logger.error(`Erreur ou aucun message à supprimer pour la conversation ${conversation.id}: ${messagesError?.message || 'Aucun message éligible'}`);
                            continue;
                        }
                        
                        const messageIds = oldMessages.map(message => message.id);
                        
                        // Suppression des pièces jointes associées
                        const { data: attachments } = await supabase
                            .from('user_message_attachments')
                            .select('*')
                            .in('message_id', messageIds);
                        
                        if (attachments && attachments.length > 0) {
                            // Supprimer les fichiers du bucket
                            const filePaths = attachments
                                .filter(att => att.file_path)
                                .map(att => att.file_path);
                            
                            if (filePaths.length > 0) {
                                await supabase.storage
                                    .from('message_attachments')
                                    .remove(filePaths);
                            }
                            
                            // Supprimer les enregistrements des pièces jointes
                            await supabase
                                .from('user_message_attachments')
                                .delete()
                                .in('message_id', messageIds);
                        }
                        
                        // Supprimer les messages
                        const { error: deleteError } = await supabase
                            .from('user_messages')
                            .delete()
                            .in('id', messageIds);
                        
                        if (deleteError) {
                            logger.error(`Erreur lors de la suppression des messages pour la conversation ${conversation.id}: ${deleteError.message}`);
                            continue;
                        }
                        
                        // Mettre à jour le total_messages
                        await supabase
                            .from('user_messages_conversations')
                            .update({ total_messages: MAX_MESSAGES })
                            .eq('id', conversation.id);
                        
                        cleanedConversations++;
                        totalMessagesDeleted += messageIds.length;
                        
                        logger.info(`Nettoyage réussi pour la conversation ${conversation.id}: ${messageIds.length} messages supprimés`);
                    } catch (convError) {
                        logger.error(`Erreur lors du traitement de la conversation ${conversation.id}: ${convError instanceof Error ? convError.message : String(convError)}`);
                    }
                }
                
                // Passer au lot suivant
                from += BATCH_SIZE;
                
                // Si on a récupéré moins de conversations que la taille du lot, on a probablement tout traité
                if (conversations.length < BATCH_SIZE) {
                    hasMore = false;
                }
            }
            
            logger.info(`Nettoyage des conversations terminé: ${cleanedConversations} conversations nettoyées, ${totalMessagesDeleted} messages supprimés`);
        } catch (error) {
            logger.error(`Erreur globale lors du nettoyage des conversations: ${error instanceof Error ? error.message : String(error)}`);
            throw error;
        }
    }
    
    /**
     * Nettoie les anciens timestamps de bug reports
     */
    private async cleanupOldBugReportTimestamps() {
        try {
            logger.info('Début du nettoyage des anciens timestamps de bug reports');
            
            // Récupérer toutes les clés de bug reports
            const bugReportKeys = await redis.keys('bug_report:*:last_email');
            
            if (bugReportKeys.length === 0) {
                logger.info('Aucun timestamp de bug report à nettoyer');
                return;
            }
            
            logger.info(`${bugReportKeys.length} clés de bug reports trouvées, vérification de l'âge...`);
            
            const now = Date.now();
            let expiredCount = 0;
            
            // Pour chaque clé, vérifier si elle est trop ancienne (plus de 30 jours)
            for (const key of bugReportKeys) {
                try {
                    const timestamp = await redis.get(key);
                    
                    if (timestamp && !isNaN(parseInt(timestamp))) {
                        // Si le timestamp est plus ancien que 30 jours
                        if (now - parseInt(timestamp) > 30 * 24 * 60 * 60 * 1000) {
                            await redis.del(key);
                            expiredCount++;
                        }
                    } else {
                        // Si le timestamp est invalide, on le supprime
                        await redis.del(key);
                        expiredCount++;
                    }
                } catch (error) {
                    logger.error(`Erreur lors du traitement de la clé de bug report ${key}:`, error);
                }
            }
            
            logger.info(`Nettoyage terminé: ${expiredCount} clés de bug reports obsolètes supprimées`);
        } catch (error) {
            logger.error('Erreur lors du nettoyage des anciens timestamps de bug reports:', error);
        }
    }
    
    /**
     * Nettoie les anciens rappels d'emails pour éviter l'accumulation
     */
    private async cleanupOldEmailReminders() {
        try {
            logger.info('Début du nettoyage des anciens rappels d\'emails');
            
            // Récupérer toutes les clés de rappels d'emails
            const reminderKeys = await redis.keys('reminder:email:*');
            
            if (reminderKeys.length === 0) {
                logger.info('Aucun rappel d\'email à nettoyer');
                return;
            }
            
            logger.info(`${reminderKeys.length} clés de rappels d'emails trouvées, vérification des TTL...`);
            
            let expiredCount = 0;
            let keptCount = 0;
            
            // Pour chaque clé, vérifier si elle a déjà une TTL
            for (const key of reminderKeys) {
                try {
                    const ttl = await redis.ttl(key);
                    
                    // Si la clé a une TTL négative (pas de TTL) ou très longue (plus de 30 jours)
                    if (ttl < 0 || ttl > 30 * 24 * 60 * 60) {
                        // Extraire l'ID de l'offre et les heures restantes depuis la clé
                        // Format attendu: reminder:email:offer:ID:HOURS
                        const parts = key.split(':');
                        
                        if (parts.length >= 5) {
                            // Vérifier si la clé est pour une offre terminée
                            // On pourrait faire une requête à Supabase ici, mais c'est coûteux
                            // Pour simplifier, on nettoie toutes les clés de plus de 2 semaines
                            await redis.del(key);
                            expiredCount++;
                        } else {
                            // Fixer une TTL de 2 semaines pour les clés sans TTL
                            if (ttl < 0) {
                                await redis.expire(key, 14 * 24 * 60 * 60);
                                keptCount++;
                            }
                        }
                    } else {
                        // La clé a déjà une TTL raisonnable, on la garde
                        keptCount++;
                    }
                } catch (error) {
                    logger.error(`Erreur lors du traitement de la clé de rappel d'email ${key}:`, error);
                }
            }
            
            logger.info(`Nettoyage terminé: ${expiredCount} clés de rappels d'emails nettoyées, ${keptCount} conservées`);
        } catch (error) {
            logger.error('Erreur lors du nettoyage des anciens rappels d\'emails:', error);
        }
    }
    
    /**
     * Planification du nettoyage périodique
     */
    private schedulePeriodicCleanup() {
        // Nettoyage quotidien à 3h du matin
        const now = new Date();
        const night = new Date(
            now.getFullYear(),
            now.getMonth(),
            now.getDate() + 1, // demain
            3, // 3h du matin
            0, 
            0
        );
        
        // Calculer le délai jusqu'à 3h du matin demain
        const delay = night.getTime() - now.getTime();
        
        // Programmer le premier nettoyage
        setTimeout(() => {
            // Exécuter le nettoyage
            this.performPeriodicCleanup();
            
            // Programmer les nettoyages suivants (toutes les 12h)
            setInterval(() => {
                this.performPeriodicCleanup();
            }, 12 * 60 * 60 * 1000);
        }, delay);
        
        logger.info(`Nettoyage périodique programmé pour ${night.toLocaleString()}`);
    }
    
    /**
     * Exécute le nettoyage périodique
     */
    private async performPeriodicCleanup() {
        try {
            logger.info('Début du nettoyage périodique des données Redis');
           
            // Nettoyage des anciens timestamps de bug reports
            await this.cleanupOldBugReportTimestamps();
            
            // Nettoyage des anciens rappels d'emails
            await this.cleanupOldEmailReminders();
            
            // Nettoyage des conversations avec trop de messages
            try {
                logger.info('Début du nettoyage périodique des conversations dépassant la limite de messages');
                await this.cleanupConversationsExceedingLimit();
                logger.info('Nettoyage périodique des conversations dépassant la limite de messages terminé');
            } catch (error) {
                logger.error('Erreur lors du nettoyage périodique des conversations dépassant la limite de messages:', error);
            }
            
            // Nettoyage des pièces jointes de messages expirées
            await cleanupOldMessageAttachments();
            
            // Nettoyage des anciennes pièces jointes des tickets de support
            await cleanupOldAttachments();
            
            // Vérifier et appliquer les downgrades d'abonnements planifiés
            await this.cleanupScheduledSubscriptionDowngrades();
            
            // --- Rappel 7 jours avant le renouvellement automatique ---
            const { send7DaysBeforeRenewalReminder } = require('../routes/configSubscriptions');
            await send7DaysBeforeRenewalReminder();
            
            // Invalidation automatique des profils vérifiés expirés
            await this.invalidateExpiredVerifiedProfiles();
            
            logger.info('Nettoyage périodique terminé avec succès');
        } catch (error) {
            logger.error('Erreur lors du nettoyage périodique:', error);
        }
    }

    /**
     * Vérifier et appliquer les downgrades d'abonnements planifiés
     * Vérifie si des abonnements ont une date d'expiration dépassée et un downgrade planifié
     */
    private async cleanupScheduledSubscriptionDowngrades() {
        try {
            logger.info('Début de la vérification des downgrades d\'abonnements planifiés');
            await checkAndApplyScheduledDowngrades();
            logger.info('Vérification des downgrades d\'abonnements planifiés terminée');
        } catch (error) {
            logger.error('Erreur lors de la vérification des downgrades d\'abonnements planifiés:', error);
        }
    }

    /**
     * Invalide les profils vérifiés dont la date de validation de document a plus d'un an
     */
    private async invalidateExpiredVerifiedProfiles() {
        try {
            logger.info('Début de l\'invalidation automatique des profils vérifiés expirés');
            const now = new Date();
            const oneYearAgo = new Date(now.getTime() - 365 * 24 * 60 * 60 * 1000);

            // Récupérer tous les profils où au moins une date de validation est renseignée
            const { data: profils, error } = await supabase
                .from('user_profil')
                .select('user_id, type_de_profil, date_validation_document_identite, date_validation_document_entreprise, date_validation_document_assurance')
                .or('date_validation_document_identite.not.is.null,date_validation_document_entreprise.not.is.null,date_validation_document_assurance.not.is.null');

            if (error) {
                logger.error('Erreur lors de la récupération des profils pour invalidation:', error);
                return;
            }
            if (!profils || profils.length === 0) {
                logger.info('Aucun profil à vérifier pour invalidation automatique');
                return;
            }

            // Déchiffrer les données de profil (bien que les dates ne soient pas chiffrées,
            // on applique le déchiffrement pour la cohérence)
            const decryptedProfils = [];
            for (const profil of profils) {
                const decryptedProfil = await decryptProfilDataAsync(profil);
                decryptedProfils.push(decryptedProfil);
            }

            // Délais de rappel en jours
            const reminders = [60, 30, 15, 7, 1];

            for (const profil of decryptedProfils) {
                let toInvalidate = false;
                let updatesProfil: any = {};
                let updatesUser: any = {};

                // Pour tous : si la date d'identité est trop ancienne
                const identiteExpiree = profil.date_validation_document_identite && new Date(profil.date_validation_document_identite) < oneYearAgo;
                const entrepriseExpiree = profil.date_validation_document_entreprise && new Date(profil.date_validation_document_entreprise) < oneYearAgo;
                const assuranceExpiree = profil.date_validation_document_assurance && new Date(profil.date_validation_document_assurance) < oneYearAgo;

                // --- Rappels d'expiration pour chaque document ---
                const docs: { key: keyof typeof profil; type: 'identite' | 'entreprise' | 'assurance' }[] = [
                    { key: 'date_validation_document_identite', type: 'identite' },
                    { key: 'date_validation_document_entreprise', type: 'entreprise' },
                    { key: 'date_validation_document_assurance', type: 'assurance' },
                ];
                for (const doc of docs) {
                    const dateStr = profil[doc.key];
                    if (dateStr) {
                        const validationDate = new Date(dateStr);
                        const expirationDate = new Date(validationDate);
                        expirationDate.setFullYear(expirationDate.getFullYear() + 1);
                        const diffDays = Math.ceil((expirationDate.getTime() - now.getTime()) / (1000 * 60 * 60 * 24));
                        if (reminders.includes(diffDays)) {
                            // Anti-doublon Redis
                            const redisKey = `profil_expiry_reminder:${profil.user_id}:${doc.type}:${diffDays}`;
                            const ttlSeconds = Math.ceil((expirationDate.getTime() - now.getTime()) / 1000) + 86400; // +1 jour
                            const alreadySent = await redis.get(redisKey);
                            if (!alreadySent) {
                                await sendProfilExpirationWarningEmail(profil.user_id, doc.type, expirationDate.toISOString(), diffDays);
                                await redis.set(redisKey, '1', 'EX', ttlSeconds);
                            }
                        }
                    }
                }
                // --- Fin rappels ---

                if (profil.type_de_profil === 'entreprise') {
                    // Pro : si un des 3 n'est plus valide, on invalide tout
                    if (identiteExpiree || entrepriseExpiree || assuranceExpiree) {
                        toInvalidate = true;
                        updatesUser.profil_verifier = false;
                        if (identiteExpiree) {
                            updatesProfil.date_validation_document_identite = null;
                            updatesUser.identite_verifier = false;
                        }
                        if (entrepriseExpiree) {
                            updatesProfil.date_validation_document_entreprise = null;
                            updatesUser.entreprise_verifier = false;
                        }
                        if (assuranceExpiree) {
                            updatesProfil.date_validation_document_assurance = null;
                            updatesUser.assurance_verifier = false;
                        }
                    }
                } else {
                    // Particulier : si identité n'est plus valide, on invalide tout
                    if (identiteExpiree) {
                        toInvalidate = true;
                        updatesUser.profil_verifier = false;
                        updatesProfil.date_validation_document_identite = null;
                        updatesUser.identite_verifier = false;
                    }
                }

                if (toInvalidate) {
                    // Vérifier si le profil était vérifié juste avant
                    const { data: userBefore } = await supabase
                        .from('users')
                        .select('profil_verifier')
                        .eq('id', profil.user_id)
                        .single();
                    // Mettre à jour user_profil
                    await supabase
                        .from('user_profil')
                        .update(updatesProfil)
                        .eq('user_id', profil.user_id);
                    // Mettre à jour users
                    await supabase
                        .from('users')
                        .update(updatesUser)
                        .eq('id', profil.user_id);
                    logger.info(`Profil vérifié invalidé pour l'utilisateur ${profil.user_id}`);
                    // Envoi email d'invalidation uniquement si le profil était vérifié avant
                    if (userBefore && userBefore.profil_verifier === true) {
                        await sendProfilInvalidatedEmail(profil.user_id);
                    }
                }
            }
            logger.info('Invalidation automatique des profils vérifiés expirés terminée');
        } catch (error) {
            logger.error('Erreur lors de l\'invalidation automatique des profils vérifiés expirés:', error);
        }
    }
}

export const cleanupService = CleanupService.getInstance();
