import React, { useState, useCallback, useEffect } from 'react';
import { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, AlertTriangle, Check, ChevronRight, ChevronLeft, Edit, Info, Upload, Trash2, CheckCircle } from 'lucide-react';
import { CircularProgress, LinearProgress, Box, Typography, IconButton, Tooltip } from '@mui/material';
import ModalPortal from '../ModalPortal';
import { motion } from 'framer-motion';
import { useMissionAssistant } from '../../hooks/useMissionAssistant';
import { notify } from '../Notification';
import { SERVICE_CATEGORIES, SERVICE_SUBCATEGORIES } from '../../pages/dashboard/services/types';
import { useImageCompression } from '../../utils/imageCompressor';
import useContentModeration from '../../hooks/useContentModeration';
import { useImageModeration } from '../../hooks/useImageModeration';
import ImageModerationStatus from '../ImageModerationStatus';
import RejectedImageMessage from '../RejectedImageMessage';
import useAiConsent from '../../hooks/useAiConsent';
import axios from 'axios';
import InterventionZoneMap from '../../pages/dashboard/missions/InterventionZoneMap';
import logger from '../../utils/logger';
import { setCookie, getCookie } from '../../utils/cookieUtils';
import AiMissionImageGenerator from './AiMissionImageGenerator';

// Types pour les données de mission
interface MissionData {
  titre: string;
  description: string;
  category_id?: string;
  subcategory_id?: string;
  has_budget: boolean;
  budget: number;
  budget_defini: boolean;
  date_mission: string;
  has_time_preference: boolean;
  time_slots: Array<{
    date: string;
    start: string;
    end: string;
  }>;
  adresse: string;
  code_postal: string;
  ville: string;
  pays: string;
  coordinates?: [number, number]; // Latitude, longitude
  intervention_zone?: {
    center: [number, number];
    radius: number;
  };
  payment_method: 'jobi' | 'jobi_only' | 'euros' | 'direct_only' | 'both';
  photos?: File[];
  is_urgent: boolean;
  extracted_data?: {
    budget?: number;
    payment_method?: 'jobi' | 'jobi_only' | 'euros' | 'direct_only' | 'both';
  };
}

interface AiMissionAssistantProps {
  open: boolean;
  onClose: () => void;
  onComplete: (missionData: MissionData) => void;
  userProfile: any;
}

interface CreditWarningProps { credits: number }
const CreditWarning: React.FC<CreditWarningProps> = ({ credits }) => (
  <div className="mt-3 flex items-center space-x-2 text-xs text-gray-500">
    <AlertTriangle className="h-3.5 w-3.5 flex-shrink-0 text-[#FF6B2C]" />
    <p>
      Chaque régénération utilise 1 crédit IA.
      Vous avez actuellement {credits} crédit{credits > 1 ? 's' : ''}.
    </p>
  </div>
);

const AiMissionAssistant: React.FC<AiMissionAssistantProps> = ({
  open,
  onClose,
  onComplete,
  // userProfile est passé mais non utilisé directement dans ce composant
  // il est utilisé dans le hook useMissionAssistant
  userProfile: _userProfile
}) => {
  const [userInput, setUserInput] = useState('');
  const [originalUserInput, setOriginalUserInput] = useState('');
  const [inputModified, setInputModified] = useState(false);
  const [currentStep, setCurrentStep] = useState(0);
  const [generatedData, setGeneratedData] = useState<Partial<MissionData> & Record<string, any>>({});
  const [isCompleted, setIsCompleted] = useState(false);
  const [showGeneratedResult, setShowGeneratedResult] = useState(false);
  // Nous utilisons setCurrentStepData mais pas currentStepData directement
  const [, setCurrentStepData] = useState<any>(null);
  const [isEditing, setIsEditing] = useState(false);
  const [editedData, setEditedData] = useState<Partial<MissionData> & Record<string, any>>({});
  const [editType, setEditType] = useState<'modify' | 'improve' | 'shorten' | 'lengthen' | null>(null);
  const [showConfirmation, setShowConfirmation] = useState(false);
  const [showGenerationChoice, setShowGenerationChoice] = useState(false);
  const [pendingAction, setPendingAction] = useState<{
    type: 'improve' | 'shorten' | 'lengthen' | 'generate_all' | null;
    callback: () => Promise<void>;
  } | null>(null);
  const [selectedFiles, setSelectedFiles] = useState<File[]>([]);
  const [previewUrls, setPreviewUrls] = useState<string[]>([]);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isModerating, setIsModerating] = useState(false);
  const [isValidatingAddress, setIsValidatingAddress] = useState(false);
  const [addressValidated, setAddressValidated] = useState(false);
  const [showAddressConfirmation, setShowAddressConfirmation] = useState(false);
  const [addressConfirmationData, setAddressConfirmationData] = useState<{
    message: string;
    address: string;
    callback: (confirm: boolean) => Promise<void>;
  } | null>(null);
  // États pour la modération d'images
  const [isModerationModalOpen, setIsModerationModalOpen] = useState(false);
  const [moderationImages, setModerationImages] = useState<File[]>([]);
  const [currentModerationIndex, setCurrentModerationIndex] = useState(0);
  const [moderationPreviewUrl, setModerationPreviewUrl] = useState<string | null>(null);
  const [moderatedFiles, setModeratedFiles] = useState<File[]>([]);
  const [isImageRejected, setIsImageRejected] = useState(false);
  const [rejectionDescription, setRejectionDescription] = useState<string | undefined>();
  const [rejectionImprovementSuggestions, setRejectionImprovementSuggestions] = useState<string | undefined>();
  // Utiliser les coordonnées du profil utilisateur ou Paris par défaut
  const [, setMapCenter] = useState<[number, number]>(
    _userProfile?.intervention_zone?.center || [48.8566, 2.3522]
  );
  const [mapRadius, setMapRadius] = useState<number>(15000); // 15km par défaut
  const [, setShowMap] = useState<boolean>(false);
  const { compressGalleryPhoto } = useImageCompression();
  const { validateContentSafety } = useContentModeration();
  const { moderateImage, isLoading: isModerationLoading } = useImageModeration();
  const [dontAskAgain, setDontAskAgain] = useState(false);

  // Étapes de l'assistant
  const steps = [
    {
      title: 'Description du besoin',
      field: 'description',
      resultFields: ['titre', 'description'],
      message: 'Décrivez votre besoin en quelques phrases, et notre IA vous aidera à créer une mission complète.',
      resultMessage: 'Voici le titre et la description générés par notre IA en fonction de votre besoin. Vous pouvez les modifier ou demander à l\'IA de les améliorer.'
    },
    {
      title: 'Catégorie et service',
      fields: ['category_id', 'subcategory_id'],
      message: 'Notre IA va analyser votre besoin pour suggérer la catégorie et le service les plus adaptés à votre mission.',
      resultMessage: 'Voici la catégorie et le service suggérés par notre IA. Ces choix sont basés sur l\'analyse de votre description.'
    },
    {
      title: 'Budget',
      fields: ['has_budget', 'budget', 'budget_defini', 'payment_method'],
      message: 'Notre IA va maintenant estimer un budget approprié pour votre mission en fonction du type de service et de sa complexité.',
      resultMessage: 'Voici le budget suggéré par notre IA pour votre mission.'
    },
    {
      title: 'Localisation',
      fields: ['adresse', 'code_postal', 'ville'],
      message: 'Notre IA va déterminer la localisation idéale pour votre mission en fonction des informations fournies.',
      resultMessage: 'Voici la localisation suggérée pour votre mission. Vous pouvez la modifier si nécessaire.'
    },
    {
      title: 'Horaires',
      fields: ['date_mission', 'has_time_preference', 'time_slots'],
      message: 'Notre IA va proposer des horaires adaptés à votre mission en fonction de sa nature et de sa durée estimée.',
      resultMessage: 'Voici les horaires suggérés pour votre mission. Ces créneaux sont basés sur la disponibilité habituelle pour ce type de service.'
    },
    {
      title: 'Photos',
      fields: ['photos'],
      message: 'Vous pouvez ajouter des photos pour illustrer votre mission (facultatif).',
      resultMessage: 'Ajoutez des photos pour illustrer votre mission et augmenter vos chances de trouver un jobbeur.'
    },
    {
      title: 'Résumé et publication',
      field: 'summary',
      message: "Vous pouvez générer un résumé automatique de votre mission avec l'IA (facultatif). Vous pouvez publier votre mission même sans résumé.",
      resultMessage: 'Voici le résumé complet de votre mission. Vérifiez que toutes les informations sont correctes avant de publier.'
    }
  ];

  // Utilisation du hook de génération IA pour l'assistant de mission
  const { hasConsent } = useAiConsent();
  const {
    isProcessing,
    progress,
    currentStep: aiStep,
    credits,
    isRateLimited,
    generateStep,
    generateAllSteps
  } = useMissionAssistant({
    onStepComplete: (stepData) => {
      // Si nous avons régénéré à partir d'un texte modifié à l'étape initiale,
      // nous devons remplacer complètement les données précédentes
      if (inputModified && currentStep === 0) {
        // Remplacer complètement les données générées
        setGeneratedData(stepData);

        // Réinitialiser l'état de modification
        setOriginalUserInput('');
        setInputModified(false);
      } else {
        // Sinon, mettre à jour les données générées en conservant les précédentes
        setGeneratedData(prev => ({
          ...prev,
          ...stepData
        }));
      }

      // Si les données contiennent des coordonnées, marquer l'adresse comme validée
      if (stepData.coordinates && Array.isArray(stepData.coordinates) && stepData.coordinates.length === 2) {
        setAddressValidated(true);
      }

      // Stocker les données de l'étape actuelle pour affichage
      setCurrentStepData(stepData);

      // Afficher le résultat généré
      setShowGeneratedResult(true);
    },
    onError: () => {
      notify('Une erreur est survenue lors de la génération', 'error');
    }
  });

  // Cette fonction n'est plus nécessaire car le hook useMissionAssistant
  // gère déjà le parsing de la réponse JSON

  // Vérifier si nous avons déjà des données pour une étape
  const checkIfStepHasData = (stepIndex: number) => {
    const step = steps[stepIndex];

    // Rendre l'étape Résumé (summary) optionnelle
    if (step.field === 'summary') {
      return true;
    }

    if (step.field) {
      return !!generatedData[step.field as keyof typeof generatedData];
    } else if (step.fields) {
      // Pour l'étape du budget (index 2), vérifier si has_budget est défini
      if (stepIndex === 2) {
        // Si budget est 0 et budget_defini est false, on considère que has_budget est false
        if (generatedData.budget === 0 && generatedData.budget_defini === false) {
          return true;
        }
        // Si has_budget est explicitement défini
        if (typeof generatedData.has_budget === 'boolean') {
          return true;
        }
        // Vérifier si on a toutes les données nécessaires pour le budget
        return typeof generatedData.budget === 'number' &&
               typeof generatedData.budget_defini === 'boolean' &&
               !!generatedData.payment_method;
      }

      // Pour l'étape des horaires (index 4), vérifier spécifiquement les données d'horaires
      if (stepIndex === 4) {
        // Vérifier si on a le champ has_time_preference défini (peu importe sa valeur)
        const hasTimePreference = typeof generatedData.has_time_preference === 'boolean';

        if (hasTimePreference) {
          // Si pas de préférence horaire, c'est suffisant (l'IA a déterminé qu'il n'y a pas d'horaires spécifiques)
          if (!generatedData.has_time_preference) {
            return true;
          }
          // Si préférence horaire, vérifier qu'on a des créneaux ET une date de mission
          return Array.isArray(generatedData.time_slots) &&
                 generatedData.time_slots.length > 0 &&
                 !!generatedData.date_mission;
        }
        return false;
      }

      // Pour l'étape des photos (index 5), c'est toujours considéré comme ayant des données
      // car les photos sont facultatives
      if (stepIndex === 5) {
        return true;
      }

      return step.fields.every(field => !!generatedData[field as keyof typeof generatedData]);
    } else if (step.resultFields) {
      return step.resultFields.every(field => !!generatedData[field as keyof typeof generatedData]);
    }

    return false;
  };

  // Fonction pour modérer les images
  const moderateImages = async () => {
    if (selectedFiles.length === 0) {
      return true; // Pas d'images à modérer
    }

    try {
      setIsModerationModalOpen(true);
      setModerationImages([...selectedFiles]);
      setCurrentModerationIndex(0);

      // Modérer chaque image
      const moderatedResults = [];
      for (let i = 0; i < selectedFiles.length; i++) {
        const file = selectedFiles[i];
        setCurrentModerationIndex(i);

        // Créer une URL pour la prévisualisation
        const previewUrl = URL.createObjectURL(file);
        setModerationPreviewUrl(previewUrl);

        // Modérer l'image
        try {
          const result = await moderateImage(file, 'mission', `mission-photo-ai-${i}`);

          if (!result.isSafe) {
            // L'image est inappropriée
            notify(`L'image ${i + 1} ne respecte pas nos règles de modération.`, 'error');

            // Afficher un message plus détaillé dans la console pour le débogage
            logger.info('Image refusée par la modération', {
              description: result.description,
              contentType: 'mission',
              imageIndex: i
            });

            // Mettre à jour les états pour afficher le message de rejet
            setIsImageRejected(true);
            setRejectionDescription(result.description);
            setRejectionImprovementSuggestions(result.improvementSuggestions);

            // Ne pas libérer l'URL de prévisualisation pour que l'image reste visible dans la modale de rejet
            // L'URL sera libérée lorsque l'utilisateur fermera la modale

            // Garder la modale ouverte pour afficher le message détaillé
            return false;
          }

          // Libérer l'URL de prévisualisation seulement si l'image est acceptée
          URL.revokeObjectURL(previewUrl);

          moderatedResults.push(file);
        } catch (error) {
          // Libérer l'URL de prévisualisation en cas d'erreur
          URL.revokeObjectURL(previewUrl);

          logger.error('Erreur lors de la modération de l\'image:', error);
          notify(`Erreur lors de la vérification de l'image ${i + 1}. Veuillez réessayer.`, 'error');

          // Réinitialiser les états de rejet
          setIsImageRejected(false);
          setRejectionDescription(undefined);
          setRejectionImprovementSuggestions(undefined);

          // Fermer la modale
          setIsModerationModalOpen(false);
          return false;
        }
      }

      // Toutes les images ont été modérées avec succès
      setIsModerationModalOpen(false);

      // Réinitialiser les états de rejet
      setIsImageRejected(false);
      setRejectionDescription(undefined);
      setRejectionImprovementSuggestions(undefined);

      // Libérer l'URL de prévisualisation si elle existe encore
      if (moderationPreviewUrl) {
        URL.revokeObjectURL(moderationPreviewUrl);
        setModerationPreviewUrl(null);
      }

      // Si nous avons des images modérées, les utiliser pour la mission
      if (moderatedResults.length > 0) {
        setModeratedFiles(moderatedResults);
      }

      return true;
    } catch (error) {
      logger.error('Erreur lors de la modération des images:', error);
      notify('Une erreur est survenue lors de la vérification des images', 'error');

      // Réinitialiser les états de rejet
      setIsImageRejected(false);
      setRejectionDescription(undefined);

      // Fermer la modale
      setIsModerationModalOpen(false);
      return false;
    }
  };

  // Lancer la génération pour l'étape actuelle
  const processCurrentStep = async () => {
    // Vérifier si l'utilisateur a donné son consentement
    if (!hasConsent) {
      notify('Vous devez accepter les conditions d\'utilisation de l\'IA avant de pouvoir générer du contenu', 'error');
      window.dispatchEvent(new CustomEvent('open-ai-consent-modal'));
      return;
    }

    if (userInput.trim().length < 10 && currentStep === 0) {
      notify('Veuillez décrire votre besoin en au moins 10 caractères', 'warning');
      return;
    }

    // Si nous sommes en train de montrer un résultat, passer à l'étape suivante
    if (showGeneratedResult) {
      // Si nous sommes à l'étape 0 (titre et description), lancer la modération avant de passer à l'étape suivante
      if (currentStep === 0) {
        try {
          setIsModerating(true);

          // Vérifier le titre
          const titleIsSafe = await validateContentSafety(
            generatedData.titre || '',
            'mission_title'
          );

          if (!titleIsSafe) {
            setIsModerating(false);
            return;
          }

          // Nettoyer le HTML pour obtenir le texte brut pour la modération
          const descriptionText = (generatedData.description || '').replace(/<[^>]*>/g, '');
          const descriptionIsSafe = await validateContentSafety(
            descriptionText,
            'mission_description'
          );

          if (!descriptionIsSafe) {
            setIsModerating(false);
            return;
          }

          setIsModerating(false);
        } catch (error) {
          console.error('Erreur lors de la modération:', error);
          notify('Une erreur est survenue lors de la vérification du contenu', 'error');
          setIsModerating(false);
          return;
        }
      }

      // Si nous sommes à l'étape des photos (5), modérer les images avant de passer à l'étape suivante
      if (currentStep === 5 && selectedFiles.length > 0) {
        const imagesAreValid = await moderateImages();
        if (!imagesAreValid) {
          return;
        }
      }

      // Si nous sommes à l'étape 3 (localisation) et que l'adresse n'est pas validée, exiger la validation
      // Mais seulement si l'adresse n'a pas déjà des coordonnées (ce qui indiquerait qu'elle a été validée lors de la génération automatique)
      if (currentStep === 3 && !addressValidated && generatedData.adresse && generatedData.ville && !generatedData.coordinates) {
        // Construire une adresse complète pour l'affichage
        const fullAddress = `${generatedData.adresse} ${generatedData.code_postal} ${generatedData.ville}`;

        // Créer une fonction de callback pour la validation d'adresse
        const validateAddressCallback = async (confirm: boolean) => {
          if (confirm) {
            try {
              setIsValidatingAddress(true);
              const validatedAddress = await validateAddress(fullAddress);

              if (validatedAddress.isValid) {
                setAddressValidated(true);
                // Mettre à jour les données avec l'adresse validée
                setGeneratedData(prev => ({
                  ...prev,
                  adresse: validatedAddress.adresse,
                  code_postal: validatedAddress.code_postal,
                  ville: validatedAddress.ville,
                  pays: validatedAddress.pays,
                  coordinates: validatedAddress.coordinates,
                  intervention_zone: {
                    center: validatedAddress.coordinates || [48.8566, 2.3522],
                    radius: mapRadius
                  }
                }));
                notify('Adresse validée avec succès', 'success');

                // Maintenant que l'adresse est validée, on peut continuer
                setShowAddressConfirmation(false);

                // Attendre un peu pour que l'utilisateur puisse voir le message de succès
                setTimeout(() => {
                  if (currentStep < steps.length - 1) {
                    setCurrentStep(currentStep + 1);
                    setShowGeneratedResult(false);
                  }
                }, 1500);
              } else {
                setAddressValidated(false);
                notify('Impossible de valider cette adresse. Veuillez modifier l\'adresse et réessayer.', 'warning');
                setShowAddressConfirmation(false);
              }
            } catch (error) {
              console.error('Erreur lors de la validation d\'adresse:', error);
              notify('Erreur lors de la validation d\'adresse. Veuillez réessayer.', 'error');
              setShowAddressConfirmation(false);
            } finally {
              setIsValidatingAddress(false);
            }
          } else {
            // L'utilisateur a choisi de ne pas valider l'adresse
            setShowAddressConfirmation(false);
            notify('La validation d\'adresse est obligatoire pour continuer.', 'warning');
          }
        };

        // Afficher la modale de confirmation
        setAddressConfirmationData({
          message: "L'adresse doit être validée avant de continuer. Voulez-vous la valider maintenant ?",
          address: fullAddress,
          callback: validateAddressCallback
        });
        setShowAddressConfirmation(true);
        return;
      }

      if (currentStep < steps.length - 1) {
        // Debug: Afficher les données de l'étape actuelle
        logger.info(`=== Données de l'étape ${currentStep} : ${steps[currentStep].title} ===`);
        logger.info('Données actuelles:', generatedData);
        if (currentStep === 2) { // Étape du budget
          logger.info('Budget défini:', generatedData.has_budget);
          logger.info('Montant du budget:', generatedData.budget);
          logger.info('Type de budget:', generatedData.budget_defini ? 'Fixe' : 'Flexible');
          logger.info('Méthode de paiement:', generatedData.payment_method);
        }
        logger.info('Prochaine étape:', steps[currentStep + 1].title);
        logger.info('============================================');

        // Ne pas réinitialiser l'état de validation d'adresse pour éviter les problèmes
        // lors de la génération automatique complète

        setCurrentStep(currentStep + 1);
        setShowGeneratedResult(false);
      } else {
        // Finaliser le processus
        setIsCompleted(true);
      }
      return;
    }

    // Vérifier si nous avons déjà des données pour cette étape
    const hasDataForCurrentStep = checkIfStepHasData(currentStep);

    if (hasDataForCurrentStep) {
      // Si nous avons déjà des données pour cette étape, les afficher sans régénérer
      setShowGeneratedResult(true);
      return;
    }

    // Vérifier si l'utilisateur a assez de crédits
    if (credits <= 0) {
      notify('Vous n\'avez pas assez de crédits IA. Veuillez en acheter dans le menu "Intelligence Artificielle"', 'error');
      return;
    }

    // Si nous sommes à l'étape initiale, afficher la boîte de dialogue de choix
    if (currentStep === 0 && !showGeneratedResult) {
      setShowGenerationChoice(true);
    } else {
      // Pour les autres étapes, afficher directement la confirmation
      // Créer la fonction de callback qui sera exécutée si l'utilisateur confirme
      const executeGeneration = async () => {
        // Fermer la confirmation immédiatement
        setShowConfirmation(false);
        setPendingAction(null);

        const step = steps[currentStep].title;

        // Appeler l'API via notre hook
        await generateStep(step, userInput, generatedData);
      };

      // Stocker l'action en attente et afficher la confirmation
      setPendingAction({
        type: null,
        callback: executeGeneration
      });
      setShowConfirmation(true);
    }
  };

  // Finaliser et envoyer les données complètes
  const handleFinalize = async () => {
    if (isSubmitting) return;
    setIsSubmitting(true);
    try {
      // Vérifier que toutes les données requises sont présentes
      const requiredFields = ['titre', 'description', 'category_id', 'subcategory_id'];
      const missingFields = requiredFields.filter(field => !generatedData[field]);

      if (missingFields.length > 0) {
        notify(`Certaines informations sont manquantes: ${missingFields.join(', ')}`, 'error');
        setIsSubmitting(false);
        return;
      }

      // Le résumé (summary) n'est plus obligatoire
      // Préparer les données finales
      const finalData: MissionData = {
        ...generatedData as MissionData,
        // Ajouter les photos modérées si présentes, sinon utiliser les photos sélectionnées
        photos: moderatedFiles.length > 0 ? moderatedFiles : selectedFiles
      };

      // Réinitialiser la sauvegarde du texte modifié
      setOriginalUserInput('');
      setInputModified(false);

      // Appeler le callback de complétion
      onComplete(finalData);
      onClose();
    } catch (error) {
      notify('Une erreur est survenue lors de la finalisation de la mission', 'error');
      console.error('Erreur lors de la finalisation:', error);
      setIsSubmitting(false);
    }
  };

  // Démarrer l'édition du contenu
  const startEditing = (type: 'modify' | 'improve' | 'shorten' | 'lengthen') => {
    setIsEditing(true);
    setEditType(type);

    // Initialiser les données d'édition avec les données actuelles
    if (type === 'modify') {
      // Copier uniquement les champs pertinents selon l'étape actuelle
      if (currentStep === 0) {
        setEditedData({
          titre: generatedData.titre || '',
          description: generatedData.description || ''
        });
      } else if (currentStep === 1) {
        setEditedData({
          category_id: generatedData.category_id || '',
          subcategory_id: generatedData.subcategory_id || ''
        });
      } else if (currentStep === 2) {
        // Initialisation du budget
        const hasBudget = typeof generatedData.budget === 'number' || generatedData.has_budget;
        setEditedData({
          has_budget: hasBudget,
          budget: typeof generatedData.budget === 'number' ? generatedData.budget : 0,
          budget_defini: hasBudget ? (generatedData.budget_defini || false) : false,
          payment_method: hasBudget ? (generatedData.payment_method || 'euros') : undefined
        });
      } else if (currentStep === 3) {
        setEditedData({
          adresse: generatedData.adresse || '',
          code_postal: generatedData.code_postal || '',
          ville: generatedData.ville || ''
        });
      } else if (currentStep === 4) {
        // Obtenir la date du jour au format YYYY-MM-DD
        const today = new Date();
        const formattedDate = today.toISOString().split('T')[0];

        // Définir une date de mission par défaut (aujourd'hui si non définie)
        const missionDate = generatedData.date_mission || formattedDate;

        // Pour les horaires, s'assurer que time_slots est un tableau avec des valeurs valides
        let timeSlots: Array<{date: string, start: string, end: string}> = [];

        if (Array.isArray(generatedData.time_slots) && generatedData.time_slots.length > 0) {
          // Filtrer les créneaux vides et ajouter des valeurs par défaut si nécessaire
          timeSlots = generatedData.time_slots
            .filter(slot => slot && (slot.date || slot.start || slot.end)) // Filtrer les créneaux complètement vides
            .map(slot => ({
              date: slot.date || missionDate,
              start: slot.start || '08:00',
              end: slot.end || '18:00'
            }));
        }

        // Si aucun créneau valide n'a été trouvé, créer un créneau par défaut
        if (timeSlots.length === 0) {
          timeSlots = [{
            date: missionDate,
            start: '08:00',
            end: '18:00'
          }];
        }

        setEditedData({
          date_mission: missionDate,
          has_time_preference: generatedData.has_time_preference || false,
          time_slots: timeSlots
        });
      } else {
        setEditedData({...generatedData});
      }
    }
  };

  // Annuler l'édition
  const cancelEditing = () => {
    setIsEditing(false);
    setEditType(null);
    setEditedData({});
  };

  // Fonction pour valider une adresse avec api-adresse.data.gouv.fr
  const validateAddress = async (address: string): Promise<{
    isValid: boolean;
    adresse: string;
    code_postal: string;
    ville: string;
    pays: string;
    coordinates?: [number, number];
  }> => {
    try {
      if (!address || address.length < 3) {
        return {
          isValid: false,
          adresse: '',
          code_postal: '',
          ville: '',
          pays: ''
        };
      }

      try {
        const response = await axios.get(
          `https://api-adresse.data.gouv.fr/search/?q=${encodeURIComponent(address)}&limit=1`,
          { timeout: 5000 } // Timeout de 5 secondes
        );

        if (response.data.features && response.data.features.length > 0) {
          const feature = response.data.features[0];
          const addressData = feature.properties;
          const coordinates: [number, number] = [
            feature.geometry.coordinates[1], // Latitude
            feature.geometry.coordinates[0]  // Longitude
          ];

          // Mettre à jour le centre de la carte
          setMapCenter(coordinates);

          // Extraire le numéro et la rue
          let extractedNumero = '';
          let extractedRue = addressData.name;

          // Extraire le numéro de la rue
          const numeroMatch = addressData.name.match(/^(\d+[a-z]?)/i);
          if (numeroMatch) {
            extractedNumero = numeroMatch[1];
            extractedRue = addressData.name.substring(numeroMatch[0].length).trim();
          }

          // Afficher la carte
          setShowMap(true);

          return {
            isValid: true,
            adresse: `${extractedNumero} ${extractedRue}`.trim(),
            code_postal: addressData.postcode || '',
            ville: addressData.city || '',
            pays: 'France',
            coordinates: coordinates
          };
        }
      } catch (apiError) {
        console.error('Erreur API lors de la validation d\'adresse:', apiError);
        // En cas d'erreur API, on continue avec le fallback
      }

      // Fallback: Extraire les informations de l'adresse manuellement
      // Format attendu: "numéro rue, code postal ville"
      const addressParts = address.split(',').map(part => part.trim());

      if (addressParts.length >= 2) {
        // Première partie: numéro et rue
        const streetPart = addressParts[0];

        // Deuxième partie: code postal et ville
        const locationPart = addressParts[1];
        const locationMatch = locationPart.match(/^(\d{5})\s+(.+)$/);

        let codePostal = '';
        let ville = '';

        if (locationMatch) {
          codePostal = locationMatch[1];
          ville = locationMatch[2];
        } else {
          // Si le format n'est pas exact, on essaie de trouver un code postal
          const postalCodeMatch = locationPart.match(/\d{5}/);
          if (postalCodeMatch) {
            codePostal = postalCodeMatch[0];
            ville = locationPart.replace(codePostal, '').trim();
          } else {
            ville = locationPart;
          }
        }

        // Utiliser les coordonnées du profil utilisateur ou Paris par défaut
        const defaultCoordinates: [number, number] = _userProfile?.intervention_zone?.center || [48.8566, 2.3522];

        // Afficher la carte avec les coordonnées par défaut
        setMapCenter(defaultCoordinates);
        setShowMap(true);

        notify('Validation automatique de l\'adresse (mode dégradé)', 'info');

        return {
          isValid: true,
          adresse: streetPart,
          code_postal: codePostal,
          ville: ville,
          pays: 'France',
          coordinates: defaultCoordinates
        };
      }

      // Si on ne peut pas extraire les informations, on utilise les données brutes
      if (address.length > 0) {
        const defaultCoordinates: [number, number] = _userProfile?.intervention_zone?.center || [48.8566, 2.3522];
        setMapCenter(defaultCoordinates);
        setShowMap(true);

        notify('Validation automatique de l\'adresse (mode dégradé)', 'info');

        // Essayer d'extraire un code postal
        const postalCodeMatch = address.match(/\d{5}/);
        const codePostal = postalCodeMatch ? postalCodeMatch[0] : '';

        return {
          isValid: true,
          adresse: address,
          code_postal: codePostal,
          ville: address.replace(codePostal, '').trim(),
          pays: 'France',
          coordinates: defaultCoordinates
        };
      }

      notify('Adresse insuffisante pour la validation', 'warning');
      return {
        isValid: false,
        adresse: '',
        code_postal: '',
        ville: '',
        pays: ''
      };
    } catch (error) {
      console.error('Erreur lors de la validation d\'adresse:', error);
      notify('Erreur lors de la validation d\'adresse, validation automatique activée', 'warning');

      // En cas d'erreur, on accepte l'adresse telle quelle
      // Utiliser les coordonnées du profil utilisateur ou Paris par défaut
      const defaultCoordinates: [number, number] = _userProfile?.intervention_zone?.center || [48.8566, 2.3522];

      setMapCenter(defaultCoordinates);
      setShowMap(true);

      return {
        isValid: true,
        adresse: address,
        code_postal: '',
        ville: '',
        pays: 'France',
        coordinates: defaultCoordinates
      };
    }
  };

  // Fonctions utilitaires pour la gestion des créneaux horaires
  const sortTimeSlots = (slots: Array<{date: string, start: string, end: string}>) => {
    return slots.sort((a, b) => {
      const dateCompare = a.date.localeCompare(b.date);
      if (dateCompare !== 0) return dateCompare;
      return a.start.localeCompare(b.start);
    });
  };

  const mergeOverlappingSlots = (slots: Array<{date: string, start: string, end: string}>) => {
    if (!slots || slots.length === 0) return [];

    // Trier les créneaux par date et heure de début
    const sortedSlots = sortTimeSlots([...slots]);
    const mergedSlots: Array<{date: string, start: string, end: string}> = [];

    // Regrouper les créneaux par date
    const slotsByDate: Record<string, Array<{start: string, end: string}>> = {};
    sortedSlots.forEach(slot => {
      if (!slotsByDate[slot.date]) {
        slotsByDate[slot.date] = [];
      }
      slotsByDate[slot.date].push({ start: slot.start, end: slot.end });
    });

    // Pour chaque date, fusionner les créneaux qui se chevauchent
    Object.entries(slotsByDate).forEach(([date, timeSlots]) => {
      const mergedTimeSlotsForDate: Array<{start: string, end: string}> = [];

      timeSlots.sort((a, b) => a.start.localeCompare(b.start));

      let currentSlot = timeSlots[0];

      for (let i = 1; i < timeSlots.length; i++) {
        const nextSlot = timeSlots[i];

        // Si les créneaux se chevauchent
        if (currentSlot.end >= nextSlot.start) {
          // Fusionner les créneaux en prenant l'heure de fin la plus tardive
          currentSlot.end = currentSlot.end > nextSlot.end ? currentSlot.end : nextSlot.end;
        } else {
          // Pas de chevauchement, ajouter le créneau courant et passer au suivant
          mergedTimeSlotsForDate.push({ ...currentSlot });
          currentSlot = nextSlot;
        }
      }

      // Ajouter le dernier créneau
      mergedTimeSlotsForDate.push(currentSlot);

      // Ajouter les créneaux fusionnés au résultat final
      mergedTimeSlotsForDate.forEach(timeSlot => {
        mergedSlots.push({
          date,
          start: timeSlot.start,
          end: timeSlot.end
        });
      });
    });

    return mergedSlots;
  };

  // Sauvegarder les modifications
  const saveEdits = async () => {
    // Vérifier si les données ont été modifiées
    if (JSON.stringify(editedData) === JSON.stringify(generatedData)) {
      notify('Aucune modification n\'a été effectuée', 'info');
      setIsEditing(false);
      setEditType(null);
      return;
    }

    // Créer une copie des données éditées pour validation
    let validatedData = { ...editedData };

    // Validation spécifique pour le budget
    if (currentStep === 2) {
      if (validatedData.budget === 0 && validatedData.budget_defini === false) {
        validatedData.has_budget = false;
      } else {
        validatedData.has_budget = true;
      }
    }

    // Validation spécifique pour la localisation
    if (currentStep === 3) {
      // Si l'adresse a été modifiée et n'est pas validée, proposer de la valider
      if (
        editedData.adresse !== generatedData.adresse ||
        editedData.code_postal !== generatedData.code_postal ||
        editedData.ville !== generatedData.ville
      ) {
        if (!addressValidated) {
          // Construire une adresse complète pour l'affichage
          const fullAddress = `${editedData.adresse} ${editedData.code_postal} ${editedData.ville}`;

          // Créer une fonction de callback pour la validation d'adresse
          const validateAddressCallback = async (confirm: boolean) => {
            if (confirm) {
              try {
                setIsValidatingAddress(true);
                const validatedAddress = await validateAddress(fullAddress);

                if (validatedAddress.isValid) {
                  setAddressValidated(true);
                  // Mettre à jour les données avec l'adresse validée
                  validatedData = {
                    ...validatedData,
                    adresse: validatedAddress.adresse,
                    code_postal: validatedAddress.code_postal,
                    ville: validatedAddress.ville,
                    pays: validatedAddress.pays,
                    coordinates: validatedAddress.coordinates,
                    intervention_zone: {
                      center: validatedAddress.coordinates || [48.8566, 2.3522],
                      radius: mapRadius
                    }
                  };

                  // Mettre à jour les données générées avec les données validées
                  setGeneratedData(prev => ({
                    ...prev,
                    ...validatedData
                  }));

                  // Quitter le mode édition
                  setIsEditing(false);
                  setEditType(null);

                  notify('Adresse validée avec succès', 'success');
                } else {
                  setAddressValidated(false);
                  notify('Impossible de valider cette adresse. Veuillez modifier l\'adresse et réessayer.', 'warning');
                  setShowAddressConfirmation(false);
                }
              } catch (error) {
                console.error('Erreur lors de la validation d\'adresse:', error);
                notify('Erreur lors de la validation d\'adresse. Veuillez réessayer.', 'error');
                setShowAddressConfirmation(false);
              } finally {
                setIsValidatingAddress(false);
              }
            } else {
              // L'utilisateur a choisi de ne pas valider l'adresse
              setShowAddressConfirmation(false);
              notify('La validation d\'adresse est obligatoire pour enregistrer les modifications.', 'warning');
            }
          };

          // Afficher la modale de confirmation
          setAddressConfirmationData({
            message: "L'adresse a été modifiée mais n'est pas validée. Voulez-vous la valider avant de continuer ?",
            address: fullAddress,
            callback: validateAddressCallback
          });
          setShowAddressConfirmation(true);
          return; // Arrêter l'exécution de saveEdits ici
        }
      }
    }

    // Validation spécifique pour les horaires
    if (currentStep === 4 && validatedData.has_time_preference && Array.isArray(validatedData.time_slots)) {
      // Fusionner les créneaux qui se chevauchent et supprimer les doublons
      validatedData.time_slots = mergeOverlappingSlots(validatedData.time_slots);
    }

    // Mettre à jour les données générées avec les données validées
    setGeneratedData(prev => ({
      ...prev,
      ...validatedData
    }));

    // Quitter le mode édition
    setIsEditing(false);
    setEditType(null);
  };

  // Fonction pour demander confirmation avant de régénérer
  const confirmAction = (type: 'improve' | 'shorten' | 'lengthen') => {
    // Vérifier si l'utilisateur a donné son consentement
    if (!hasConsent) {
      notify('Vous devez accepter les conditions d\'utilisation de l\'IA avant de pouvoir générer du contenu', 'error');
      window.dispatchEvent(new CustomEvent('open-ai-consent-modal'));
      return;
    }

    // Vérifier si l'utilisateur a assez de crédits
    if (credits <= 0) {
      notify('Vous n\'avez pas assez de crédits IA. Veuillez en acheter dans le menu "Intelligence Artificielle"', 'error');
      return;
    }

    // Créer la fonction de callback qui sera exécutée si l'utilisateur confirme
    const executeAction = async () => {
      // Fermer la confirmation immédiatement
      setShowConfirmation(false);
      setPendingAction(null);

      // Pas besoin de setIsProcessing car generateStep le fera
      setEditType(type);

      const step = steps[currentStep].title;
      let instruction = '';

      switch (type) {
        case 'improve':
          instruction = 'Améliore ce texte en le rendant plus professionnel et détaillé.';
          break;
        case 'shorten':
          instruction = 'Raccourcis ce texte tout en gardant les informations essentielles.';
          break;
        case 'lengthen':
          instruction = 'Développe ce texte avec plus de détails et d\'informations.';
          break;
      }

      // Ajouter l'instruction au userInput pour la régénération
      const enhancedInput = `${userInput}\n\nInstruction: ${instruction}`;

      // Appeler l'API via notre hook
      await generateStep(step, enhancedInput, generatedData);

      // Réinitialiser le type d'édition après la génération
      setEditType(null);
    };

    // Stocker l'action en attente et afficher la confirmation
    setPendingAction({
      type,
      callback: executeAction
    });
    setShowConfirmation(true);
  };

  // Confirmer l'action
  const handleConfirm = async () => {
    // Stocker la callback temporairement
    const callback = pendingAction?.callback;

    // Fermer la modal immédiatement
    setShowConfirmation(false);
    setPendingAction(null);

    // Exécuter la callback si elle existe
    if (callback) {
      await callback();
    }
  };

  // Annuler l'action
  const handleCancel = () => {
    setShowConfirmation(false);
    setPendingAction(null);
  };

  // Régénérer le contenu avec des instructions spécifiques
  const regenerateContent = async (type: 'improve' | 'shorten' | 'lengthen') => {
    // Demander confirmation avant de régénérer
    confirmAction(type);
  };

  // Gérer l'upload des photos
  const handleFileChange = async (event: React.ChangeEvent<HTMLInputElement>) => {
    if (!event.target.files || event.target.files.length === 0) return;

    const files = Array.from(event.target.files);

    // Limiter le nombre de photos à 8
    if (selectedFiles.length + files.length > 8) {
      notify('Vous ne pouvez pas ajouter plus de 8 photos au total', 'warning');
      return;
    }

    // Vérifier les types de fichiers (uniquement images)
    const validFiles = files.filter(file => file.type.startsWith('image/'));
    if (validFiles.length !== files.length) {
      notify('Certains fichiers ne sont pas des images et ont été ignorés', 'warning');
    }

    // Compresser et ajouter les fichiers
    const newFiles: File[] = [];
    const newUrls: string[] = [];

    for (const file of validFiles) {
      try {
        // Compresser l'image
        const compressedFile = await compressGalleryPhoto(file);

        // Créer une URL pour la prévisualisation
        const url = URL.createObjectURL(compressedFile);

        newFiles.push(compressedFile);
        newUrls.push(url);
      } catch (error) {
        console.error('Erreur lors de la compression de l\'image:', error);
        notify('Une erreur est survenue lors du traitement de l\'image', 'error');
      }
    }

    // Mettre à jour les états
    setSelectedFiles(prev => [...prev, ...newFiles]);
    setPreviewUrls(prev => [...prev, ...newUrls]);

    // Mettre à jour les données générées
    setGeneratedData(prev => ({
      ...prev,
      photos: [...(prev.photos || []), ...newFiles]
    }));

    // Réinitialiser l'input file
    event.target.value = '';
  };

  // Gérer les images générées par IA
  const handleAiImageGenerated = async (imageUrl: string, imageBase64: string) => {
    try {
      // Limiter le nombre de photos à 8
      if (selectedFiles.length >= 8) {
        notify('Vous ne pouvez pas ajouter plus de 8 photos au total', 'warning');
        return;
      }

      // Convertir l'URL de l'image en fichier
      const response = await fetch(imageUrl);
      const blob = await response.blob();
      const file = new File([blob], `mission-image-ia-${Date.now()}.jpg`, { type: 'image/jpeg' });

      // --- Correction : ne pas lancer de modération immédiate ici ! ---
      // Ajouter simplement l'image à la liste des fichiers sélectionnés et à l'état preview.
      // La modération sera faite plus tard lors de la finalisation de la mission (onComplete).
      const previewUrl = URL.createObjectURL(file);
      setSelectedFiles(prev => [...prev, file]);
      setPreviewUrls(prev => [...prev, previewUrl]);
      setGeneratedData(prev => ({
        ...prev,
        photos: [...(prev.photos || []), file]
      }));
      notify('Image générée par IA ajoutée avec succès !', 'success');
    } catch (error) {
      console.error('Erreur lors de l\'ajout de l\'image générée par IA:', error);
      notify('Une erreur est survenue lors de l\'ajout de l\'image générée par IA', 'error');
    }
  };

  // Supprimer une photo
  const handleRemovePhoto = (index: number) => {
    // Libérer l'URL de prévisualisation
    URL.revokeObjectURL(previewUrls[index]);

    // Mettre à jour les états
    setSelectedFiles(prev => prev.filter((_: File, i: number) => i !== index));
    setPreviewUrls(prev => prev.filter((_: string, i: number) => i !== index));

    // Mettre à jour les données générées
    setGeneratedData(prev => ({
      ...prev,
      photos: (prev.photos || []).filter((_: File, i: number) => i !== index)
    }));
  };

  // Styles pour l'affichage des photos
  const StyledImageList = useCallback(() => (
    <Box sx={{
      width: '100%',
      height: 'auto',
      gap: '8px',
      padding: '0',
      display: 'grid',
      gridTemplateColumns: 'repeat(auto-fill, minmax(150px, 1fr))',
      '& .add-photo-label': {
        gridColumn: '1 / -1',
        margin: '0 auto',
        width: '100%'
      },
      '@media (max-width: 600px)': {
        gap: '6px',
        gridTemplateColumns: 'repeat(2, 1fr)',
      }
    }}>
      {previewUrls.map((url, index) => (
        <Box key={index} sx={{
          overflow: 'hidden',
          borderRadius: '8px',
          boxShadow: '0 2px 4px rgba(0, 0, 0, 0.05)',
          position: 'relative',
          aspectRatio: '1/1',
          '&:hover': {
            '& .delete-button': {
              opacity: 1,
            },
            '& .overlay': {
              opacity: 1,
            }
          }
        }}>
          <img
            src={url}
            alt={`Photo ${index + 1}`}
            style={{
              width: '100%',
              height: '100%',
              objectFit: 'cover',
            }}
          />
          <Box className="overlay" sx={{
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            background: 'linear-gradient(to top, rgba(0, 0, 0, 0.7), transparent)',
            opacity: 0,
            transition: 'opacity 0.2s ease',
          }} />
          <IconButton
            className="delete-button"
            onClick={() => handleRemovePhoto(index)}
            size="small"
            sx={{
              position: 'absolute',
              top: '4px',
              right: '4px',
              opacity: 0,
              transition: 'opacity 0.2s ease',
              backgroundColor: 'white',
              padding: '2px',
              minWidth: 'unset',
              borderRadius: '4px',
              '&:hover': {
                backgroundColor: '#f5f5f5',
              },
              '& svg': {
                fontSize: '16px',
                color: '#FF6B2C',
              }
            }}
          >
            <Trash2 size={16} />
          </IconButton>
        </Box>
      ))}
    </Box>
  ), [previewUrls, handleRemovePhoto]);

  // Réinitialiser l'assistant
  const resetAssistant = () => {
    setUserInput('');
    setOriginalUserInput('');
    setInputModified(false);
    setCurrentStep(0);
    setGeneratedData({});

    // Libérer les URLs de prévisualisation
    previewUrls.forEach(url => URL.revokeObjectURL(url));
    setSelectedFiles([]);
    setPreviewUrls([]);
    setIsCompleted(false);
    setShowGeneratedResult(false);
    setCurrentStepData(null);
    setIsEditing(false);
    setEditType(null);
    setEditedData({});
    setShowConfirmation(false);
    setPendingAction(null);
  };

  // Fonction pour formater l'affichage de la méthode de paiement
  const formatPaymentMethod = (method: string | undefined) => {
    switch (method) {
      case 'jobi':
      case 'jobi_only':
        return 'Jobi uniquement';
      case 'euros':
      case 'direct_only':
        return 'Euros uniquement';
      case 'both':
        return 'Euros ou Jobi (au choix)';
      default:
        return 'Non spécifié';
    }
  };

  // Fonction pour afficher le résultat de l'étape Budget
  const renderBudgetResult = () => {
    if (!generatedData.has_budget && !generatedData.budget) {
      return (
        <div className="bg-white rounded-lg p-4 shadow-sm">
          <div className="flex items-center justify-between">
            <span className="text-gray-600">Budget:</span>
            <span className="font-semibold">Non défini</span>
          </div>
        </div>
      );
    }

    const paymentMethod = generatedData.payment_method || 'euros';

    return (
      <div className="bg-white rounded-lg p-4 shadow-sm">
        <div className="flex flex-col gap-2">
          <div className="flex items-center justify-between">
            <span className="text-gray-600">Montant:</span>
            <span className="font-semibold">
              {generatedData.budget} {paymentMethod === 'jobi' || paymentMethod === 'jobi_only' ? 'Jobi' : 'Euros'}
            </span>
          </div>
          <div className="flex items-center justify-between">
            <span className="text-gray-600">Type de budget:</span>
            <span className="font-semibold">
              {generatedData.budget_defini ? 'Fixe' : 'Estimatif'}
            </span>
          </div>
          <div className="flex items-center justify-between">
            <span className="text-gray-600">Mode de paiement:</span>
            <span className="font-semibold">
              {formatPaymentMethod(paymentMethod)}
            </span>
          </div>
        </div>
      </div>
    );
  };

  if (!open) return null;

  // Fonction pour gérer la confirmation d'adresse
  const handleAddressConfirm = async () => {
    if (addressConfirmationData && addressConfirmationData.callback) {
      await addressConfirmationData.callback(true);
      // Fermer la modale après la validation
      setShowAddressConfirmation(false);
    }
  };

  // Fonction pour gérer l'annulation de la confirmation d'adresse
  const handleAddressCancel = async () => {
    if (addressConfirmationData && addressConfirmationData.callback) {
      await addressConfirmationData.callback(false);
      // Fermer la modale après l'annulation
      setShowAddressConfirmation(false);
    }
  };

  // Vérifier au montage si le cookie existe pour ne plus demander la confirmation IA
  useEffect(() => {
    if (showConfirmation && getCookie('skipIaCreditConfirmation') === '1') {
      // Exécuter directement l'action sans afficher la confirmation
      setShowConfirmation(false);
      if (pendingAction?.callback) {
        pendingAction.callback();
      }
    }
  }, [showConfirmation, pendingAction]);

  // Vérifier si l'adresse a déjà des coordonnées pour la marquer comme validée
  useEffect(() => {
    if (generatedData.coordinates && Array.isArray(generatedData.coordinates) && generatedData.coordinates.length === 2) {
      setAddressValidated(true);
    }
  }, [generatedData.coordinates]);

  return (
    <ModalPortal>
      <div className="fixed inset-0 bg-black/50 flex items-center justify-center p-4 z-[60]">
        {/* Boîte de dialogue de choix du mode de génération */}
        {showGenerationChoice && (
          <div className="fixed inset-0 bg-black/70 flex items-center justify-center z-[70] p-4">
            <motion.div
              initial={{ opacity: 0, scale: 0.97 }}
              animate={{ opacity: 1, scale: 1 }}
              className="w-full max-w-[600px] bg-[#FFF8F3] rounded-2xl shadow-2xl border border-[#FFE4BA] overflow-hidden flex flex-col max-h-[90vh] overflow-y-auto"
            >
              {/* Header */}
              <div className="flex items-center justify-center gap-2 bg-[#FFF8F3] border-b border-[#FFE4BA] px-2 sm:px-6 py-4">
                <Sparkles className="h-6 w-6 text-[#FF6B2C]" />
                <h3 className="text-xl font-bold text-gray-900 text-center flex-1">Comment générer votre mission ?</h3>
                <button
                  onClick={() => setShowGenerationChoice(false)}
                  className="text-gray-400 hover:text-gray-700 transition-colors p-1 rounded-full"
                  aria-label="Fermer"
                >
                  <X className="h-5 w-5" />
                </button>
              </div>

              {/* Bloc d'intro modernisé */}
              <div className="bg-white px-2 sm:px-6 py-4 flex flex-col items-center text-center gap-2 border-b border-[#FFE4BA]">
                <p className="text-gray-700 text-sm max-w-[90%] mx-auto">
                  Choisissez comment vous souhaitez créer votre mission avec l'IA.
                </p>
              </div>

              {/* Options IA */}
              <div className="flex flex-col gap-3 px-2 sm:px-6 py-4 sm:py-6 bg-[#FFF8F3]">
                {/* Option 1 : Génération complète */}
                <div
                  onClick={() => {
                    if (!hasConsent) {
                      notify('Vous devez accepter les conditions d\'utilisation de l\'IA avant de pouvoir générer du contenu', 'error');
                      window.dispatchEvent(new CustomEvent('open-ai-consent-modal'));
                      return;
                    }
                    if (credits < 5) {
                      notify('Vous n\'avez pas assez de crédits IA. Veuillez en acheter dans le menu "Intelligence Artificielle"', 'error');
                      return;
                    }
                    setShowGenerationChoice(false);
                    const executeAllGeneration = async () => {
                      setShowConfirmation(false);
                      setPendingAction(null);
                      const initialData = { is_urgent: generatedData.is_urgent || false };
                      const result = await generateAllSteps(userInput, initialData);
                      if (result) {
                        // Modération automatique du titre et de la description après génération complète
                        const titre = result.titre || '';
                        const description = (result.description || '').replace(/<[^>]*>/g, '');
                        setIsModerating(true);
                        const titleIsSafe = await validateContentSafety(titre, 'mission_title');
                        if (!titleIsSafe) {
                          setIsModerating(false);
                          notify('Le titre généré ne respecte pas nos règles de modération.', 'error');
                          return;
                        }
                        const descriptionIsSafe = await validateContentSafety(description, 'mission_description');
                        setIsModerating(false);
                        if (!descriptionIsSafe) {
                          notify('La description générée ne respecte pas nos règles de modération.', 'error');
                          return;
                        }
                        setGeneratedData(result);
                        setCurrentStep(0); // Revenir à la première étape
                        setShowGeneratedResult(true); // Afficher le résultat généré de la première étape
                        notify('Génération complète réussie ! Vérifiez chaque étape avant de publier.', 'success');
                      }
                    };
                    setPendingAction({ type: 'generate_all', callback: executeAllGeneration });
                    setShowConfirmation(true);
                  }}
                  className={`group relative transition-all duration-150 rounded-xl border-2 px-5 py-5 bg-white flex flex-col sm:flex-row gap-4 items-center shadow-sm ${
                    credits >= 5
                      ? 'cursor-pointer hover:shadow-lg hover:border-[#FF6B2C]'
                      : 'opacity-60 cursor-not-allowed border-gray-200'
                  }`}
                >
                  <div className="flex flex-col items-center sm:items-start gap-2 w-full sm:w-auto">
                    <div className="flex flex-col items-center sm:flex-row sm:items-center sm:justify-center w-full gap-1 sm:gap-3 mb-1">
                      <div className="flex items-center gap-2 justify-center">
                        <Sparkles className="h-5 w-5 text-[#FF6B2C] flex-shrink-0" />
                        <span className="text-lg font-bold text-gray-900 leading-tight text-center">
                          Génération automatique complète
                        </span>
                      </div>
                      <span className="mt-1 sm:mt-0 sm:ml-2 text-xs bg-green-100 text-green-700 px-2 py-0.5 rounded-full font-bold shadow-sm text-center w-max">Recommandé</span>
                    </div>
                    <p className="text-gray-700 text-sm mt-1 max-w-[420px]">
                      L'IA crée toute votre mission en une seule fois, de la description au résumé final. Idéal pour aller vite, vous pourrez tout modifier ensuite.
                    </p>
                    <div className="flex flex-col sm:flex-row sm:items-center gap-2 mt-2">
                      <span className={`text-sm font-semibold px-3 py-1.5 rounded-full border text-center mx-auto sm:mx-0 ${
                        credits >= 5
                          ? 'bg-[#FFF8F3] text-[#FF6B2C] border-[#FFE4BA]'
                          : 'bg-red-50 text-red-600 border-red-200'
                      }`}>
                        5 crédits IA au total
                      </span>
                      {credits >= 5 ? (
                        <span className="text-sm text-[#FF6B2C] italic flex items-center gap-1">
                          <Info className="h-4 w-4" /> Modifications gratuites après génération
                        </span>
                      ) : (
                        <span className="text-sm text-red-600 italic flex items-center gap-1">
                          <AlertTriangle className="h-4 w-4" /> Crédits insuffisants ({credits}/5)
                        </span>
                      )}
                    </div>
                  </div>
                  {/* Feedback de sélection possible ici (ex: check ou highlight) */}
                </div>

                {/* Option 2 : Étape par étape */}
                <div
                  onClick={() => {
                    if (!hasConsent) {
                      notify('Vous devez accepter les conditions d\'utilisation de l\'IA avant de pouvoir générer du contenu', 'error');
                      window.dispatchEvent(new CustomEvent('open-ai-consent-modal'));
                      return;
                    }
                    if (credits < 1) {
                      notify('Vous n\'avez pas assez de crédits IA. Veuillez en acheter dans le menu "Intelligence Artificielle"', 'error');
                      return;
                    }
                    setShowGenerationChoice(false);
                    const executeGeneration = async () => {
                      setShowConfirmation(false);
                      setPendingAction(null);
                      const step = steps[currentStep].title;
                      await generateStep(step, userInput, generatedData);
                    };
                    setPendingAction({ type: null, callback: executeGeneration });
                    setShowConfirmation(true);
                  }}
                  className={`group relative transition-all duration-150 rounded-xl border-2 px-5 py-5 bg-white flex flex-col sm:flex-row gap-4 items-center shadow-sm ${
                    credits >= 1
                      ? 'cursor-pointer hover:shadow-lg hover:border-[#FF6B2C]'
                      : 'opacity-60 cursor-not-allowed border-gray-200'
                  }`}
                >
                  <div className="flex flex-col items-center sm:items-start gap-2 w-full sm:w-auto">
                    <div className="flex items-center gap-2">
                      <ChevronRight className="h-7 w-7 text-[#FF6B2C]" />
                      <span className="text-lg font-bold text-gray-900">
                        Génération guidée étape par étape
                      </span>
                    </div>
                    <p className="text-gray-700 text-sm mt-1 max-w-[420px]">
                      L'IA vous accompagne à chaque étape (description, budget, horaires, etc). Vous validez et personnalisez chaque partie avant de passer à la suivante.
                    </p>
                    <div className="flex flex-col sm:flex-row sm:items-center gap-2 mt-2">
                      <span className={`text-sm font-semibold px-3 py-1.5 rounded-full border text-center mx-auto sm:mx-0 ${
                        credits >= 1
                          ? 'bg-[#FFF8F3] text-[#FF6B2C] border-[#FFE4BA]'
                          : 'bg-red-50 text-red-600 border-red-200'
                      }`}>
                        1 crédit IA par étape
                      </span>
                      {credits < 1 && (
                        <span className="text-sm text-red-600 italic flex items-center gap-1">
                          <AlertTriangle className="h-4 w-4" /> Crédits insuffisants ({credits}/1)
                        </span>
                      )}
                    </div>
                  </div>
                </div>
              </div>

              {/* Bloc crédits */}
              <div className="flex items-center justify-between bg-white border-t border-[#FFE4BA] px-2 sm:px-6 py-4">
                <div className="flex items-center gap-2">
                  <AlertTriangle className={`h-5 w-5 ${credits >= 1 ? 'text-[#FF6B2C]' : 'text-red-500'}`} />
                  <span className="text-sm text-gray-700">
                    Vous avez actuellement <span className={`font-bold ${credits >= 1 ? 'text-[#FF6B2C]' : 'text-red-500'}`}>{credits} crédit{credits !== 1 ? 's' : ''}</span>.
                  </span>
                </div>
                {credits < 1 && (
                  <button
                    onClick={() => {
                      window.open('/dashboard/ai-credits', '_blank');
                    }}
                    className="px-3 py-1.5 bg-[#FF6B2C] text-white text-xs rounded-lg hover:bg-[#FF7A35] transition-colors whitespace-nowrap"
                  >
                    Acheter des crédits
                  </button>
                )}
              </div>

              {/* Footer */}
              <div className="flex justify-end bg-[#FFF8F3] px-2 sm:px-6 py-4 border-t border-[#FFE4BA]">
                <button
                  onClick={() => setShowGenerationChoice(false)}
                  className="px-4 py-2 rounded-lg border border-gray-300 text-gray-700 bg-white hover:bg-gray-100 font-medium text-base shadow-sm transition-colors w-full sm:w-auto"
                >
                  Annuler
                </button>
              </div>
            </motion.div>
          </div>
        )}

        {/* Boîte de dialogue de confirmation pour les crédits IA */}
        {showConfirmation && (
          <div className="fixed inset-0 bg-black/70 flex items-center justify-center z-[70] p-4">
            <motion.div
              initial={{ opacity: 0, y: 20, scale: 0.98 }}
              animate={{ opacity: 1, y: 0, scale: 1 }}
              exit={{ opacity: 0, y: 20, scale: 0.98 }}
              transition={{ type: 'spring', stiffness: 300, damping: 25 }}
              className="bg-white rounded-2xl shadow-xl max-w-md w-full overflow-hidden max-h-[90vh] overflow-y-auto"
              tabIndex={-1}
              aria-modal="true"
              role="dialog"
            >
              {/* Image d'en-tête avec design attrayant */}
              <div className="relative h-32 bg-gradient-to-r from-[#FF6B2C] to-[#FF965E] flex items-center justify-center overflow-hidden">
                <div className="absolute inset-0 opacity-20">
                  <div className="absolute -top-5 -left-5 w-32 h-32 rounded-full bg-white/20"></div>
                  <div className="absolute top-10 right-10 w-20 h-20 rounded-full bg-white/20"></div>
                  <div className="absolute bottom-5 left-1/3 w-16 h-16 rounded-full bg-white/20"></div>
                </div>
                <div className="relative flex flex-col items-center text-white space-y-1 p-6">
                  <div className="bg-white/20 p-3 rounded-full">
                    <Sparkles className="h-8 w-8 text-white" />
                  </div>
                  <h3 className="text-xl font-bold mt-2">Confirmer l'action IA</h3>
                </div>
                <button
                  onClick={handleCancel}
                  className="absolute top-4 right-4 p-1.5 rounded-full bg-white/20 hover:bg-white/30 transition-colors focus:outline-none focus:ring-2 focus:ring-white/50"
                  aria-label="Fermer la confirmation"
                >
                  <X className="h-5 w-5 text-white" />
                </button>
              </div>

              {/* Corps de la modal */}
              <div className="p-6 pt-5">
                {/* Informations sur les crédits */}
                <div className="flex items-center justify-between mb-5">
                  <div className="flex items-center gap-3">
                    <div className={`h-12 w-12 rounded-full flex items-center justify-center ${
                      credits >= (pendingAction?.type === 'generate_all' ? 5 : 1)
                        ? 'bg-orange-100 text-[#FF6B2C]'
                        : 'bg-red-100 text-red-500'
                    }`}>
                      <Sparkles className="h-6 w-6" />
                    </div>
                    <div>
                      <p className="text-gray-500 text-sm">Coût de génération</p>
                      <p className="text-gray-900 font-bold text-xl">{pendingAction?.type === 'generate_all' ? '5' : '1'} crédit{(pendingAction?.type === 'generate_all' ? 5 : 1) > 1 ? 's' : ''} IA</p>
                    </div>
                  </div>
                  <div className="text-right">
                    <p className="text-gray-500 text-sm">Solde</p>
                    <p className={`font-bold text-xl ${
                      credits >= (pendingAction?.type === 'generate_all' ? 5 : 1)
                        ? 'text-gray-900'
                        : 'text-red-500'
                    }`}>
                      {credits} crédit{credits !== 1 ? 's' : ''}
                    </p>
                  </div>
                </div>

                {/* Action demandée */}
                <div className="bg-gray-50 p-4 rounded-xl mb-5">
                  <p className="text-gray-800 font-medium mb-1">Action demandée :</p>
                  <p className="text-gray-900 font-semibold">
                    {pendingAction?.type ? (
                      <>
                        {pendingAction.type === 'improve' ? "Améliorer" :
                          pendingAction.type === 'shorten' ? 'Raccourcir' :
                          pendingAction.type === 'lengthen' ? 'Développer' :
                          pendingAction.type === 'generate_all' ? 'Génération complète' : 'Modifier'} le contenu avec l'IA
                      </>
                    ) : (
                      <>Générer du contenu avec l'IA</>
                    )}
                  </p>
                </div>

                {/* Alerte avec design moderne */}
                {credits >= (pendingAction?.type === 'generate_all' ? 5 : 1) ? (
                  <div className="flex items-start gap-3 p-4 rounded-xl border border-amber-200 bg-amber-50 mb-5">
                    <AlertTriangle className="h-5 w-5 text-amber-500 flex-shrink-0 mt-0.5" />
                    <div>
                      <p className="text-amber-800 font-medium text-sm">Cette action décomptera vos crédits IA.</p>
                      {generatedData && Object.keys(generatedData).length <= 1 && pendingAction?.type === 'generate_all' && (
                        <p className="text-amber-700 text-xs mt-1">
                          La génération complète remplira automatiquement toutes les étapes. Vous pourrez vérifier et modifier chaque détail avant de publier.
                        </p>
                      )}
                    </div>
                  </div>
                ) : (
                  <div className="flex items-start gap-3 p-4 rounded-xl border border-red-200 bg-red-50 mb-5">
                    <AlertTriangle className="h-5 w-5 text-red-500 flex-shrink-0 mt-0.5" />
                    <div className="flex-1">
                      <p className="text-red-800 font-medium text-sm">Crédits insuffisants</p>
                      <p className="text-red-700 text-xs mt-1 mb-3">
                        Vous avez besoin de {pendingAction?.type === 'generate_all' ? '5' : '1'} crédit{(pendingAction?.type === 'generate_all' ? 5 : 1) > 1 ? 's' : ''} IA pour cette action.
                        Votre solde actuel est de {credits} crédit{credits !== 1 ? 's' : ''}.
                      </p>
                      <button
                        onClick={() => {
                          window.open('/dashboard/ai-credits', '_blank');
                        }}
                        className="px-3 py-1.5 bg-[#FF6B2C] text-white text-xs rounded-lg hover:bg-[#FF7A35] transition-colors"
                      >
                        Acheter des crédits IA
                      </button>
                    </div>
                  </div>
                )}

                {/* Option Ne plus demander */}
                <label className="flex items-center gap-2 cursor-pointer mb-6">
                  <div className="relative flex items-center">
                    <input
                      type="checkbox"
                      checked={dontAskAgain}
                      onChange={(e) => setDontAskAgain(e.target.checked)}
                      className="peer sr-only"
                    />
                    <div className="w-5 h-5 border-2 border-gray-300 rounded bg-white peer-checked:bg-[#FF6B2C] peer-checked:border-[#FF6B2C] transition-colors" />
                    <svg
                      className="absolute w-5 h-5 pointer-events-none opacity-0 peer-checked:opacity-100 text-white transition-opacity"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      strokeWidth="3"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                    >
                      <polyline points="20 6 9 17 4 12" />
                    </svg>
                  </div>
                  <span className="text-gray-700 text-sm">Ne plus me demander pendant 10 minutes</span>
                </label>

                {/* Boutons d'action */}
                <div className="flex flex-col gap-3">
                  {!hasConsent ? (
                    <button
                      onClick={() => window.dispatchEvent(new CustomEvent('open-ai-consent-modal'))}
                      className="w-full py-3.5 px-4 bg-[#FF6B2C] text-white rounded-xl hover:bg-[#FF7A35] active:bg-[#E75B1C] transition-colors font-bold flex items-center justify-center gap-2 shadow-sm text-base focus:outline-none focus:ring-2 focus:ring-[#FF6B2C] focus:ring-offset-2"
                    >
                      <AlertTriangle className="h-5 w-5" />
                      Accepter les CGU
                    </button>
                  ) : (
                    <button
                      onClick={async () => {
                        if (dontAskAgain) {
                          setCookie('skipIaCreditConfirmation', '1', 10 * 60); // 10 minutes
                        }
                        await handleConfirm();
                      }}
                      disabled={credits < (pendingAction?.type === 'generate_all' ? 5 : 1)}
                      className={`w-full py-3.5 px-4 rounded-xl transition-colors font-bold flex items-center justify-center gap-2 shadow-sm text-base focus:outline-none ${
                        credits >= (pendingAction?.type === 'generate_all' ? 5 : 1)
                          ? 'bg-[#FF6B2C] text-white hover:bg-[#FF7A35] active:bg-[#E75B1C] focus:ring-2 focus:ring-[#FF6B2C] focus:ring-offset-2'
                          : 'bg-gray-300 text-gray-500 cursor-not-allowed'
                      }`}
                      autoFocus={credits >= (pendingAction?.type === 'generate_all' ? 5 : 1)}
                    >
                      <Check className="h-5 w-5" />
                      {credits >= (pendingAction?.type === 'generate_all' ? 5 : 1) ? 'Confirmer' : 'Crédits insuffisants'}
                    </button>
                  )}
                  <button
                    onClick={handleCancel}
                    className="w-full py-3.5 px-4 border border-gray-300 bg-white text-gray-700 rounded-xl hover:bg-gray-50 active:bg-gray-100 transition-colors font-medium text-base focus:outline-none focus:ring-2 focus:ring-gray-400 focus:ring-offset-2"
                  >
                    Annuler
                  </button>
                </div>
              </div>
            </motion.div>
          </div>
        )}

        {/* Boîte de dialogue de confirmation pour la validation d'adresse */}
        {showAddressConfirmation && addressConfirmationData && (
          <div className="fixed inset-0 bg-black/70 flex items-center justify-center z-[70] p-4">
            <motion.div
              initial={{ opacity: 0, scale: 0.9 }}
              animate={{ opacity: 1, scale: 1 }}
              className="bg-[#FFF8F3] rounded-xl shadow-2xl p-6 max-w-md w-full mx-4 border border-[#FFE4BA] max-h-[90vh] overflow-y-auto"
            >
              <div className="flex items-center space-x-3 mb-4 pb-3 border-b border-[#FFE4BA]">
                <Info className="h-5 w-5 text-[#FF6B2C] flex-shrink-0" />
                <h3 className="text-lg font-semibold text-gray-800 break-words">Validation d'adresse</h3>
              </div>

              <div className="mb-6">
                <div className="bg-white rounded-lg p-4 border border-[#FFE4BA] mb-4">
                  <p className="text-gray-700 font-medium mb-2 break-words">
                    {addressConfirmationData.message}
                  </p>

                  <div className="p-3 bg-[#FFF8F3] rounded-lg border border-[#FFE4BA]">
                    <p className="text-sm text-gray-600 font-medium">Adresse actuelle :</p>
                    <p className="text-sm text-gray-800">{addressConfirmationData.address}</p>
                  </div>
                </div>

                <div className="bg-blue-50 border border-blue-200 rounded-lg p-3 mb-3">
                  <div className="flex items-start space-x-2">
                    <Info className="h-4 w-4 text-blue-500 flex-shrink-0 mt-0.5" />
                    <div>
                      <p className="text-sm text-gray-700 mb-1">
                        La validation d'adresse permet de s'assurer que l'adresse est correcte et complète.
                      </p>
                      <p className="text-xs text-gray-600">
                        Votre adresse complète n'est pas partagée avec les autres utilisateurs de la plateforme.
                        Elle sert uniquement à localiser la mission par VILLE afin de la proposer aux utilisateurs proches de chez vous.
                      </p>
                    </div>
                  </div>
                </div>
              </div>

              <div className="flex flex-col sm:flex-row justify-center space-y-3 sm:space-y-0 sm:space-x-4">
                <button
                  onClick={handleAddressCancel}
                  className="px-5 py-2.5 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-100 transition-colors font-medium w-full sm:w-auto"
                >
                  Non, continuer sans valider
                </button>
                <button
                  onClick={handleAddressConfirm}
                  className="px-5 py-2.5 bg-[#FF6B2C] text-white rounded-lg hover:bg-[#FF7A35] transition-colors font-medium flex items-center justify-center space-x-2 w-full sm:w-auto"
                >
                  <span>Oui, valider l'adresse</span>
                  <Check className="h-4 w-4" />
                </button>
              </div>
            </motion.div>
          </div>
        )}

        <motion.div
          initial={{ opacity: 0, scale: 0.9 }}
          animate={{ opacity: 1, scale: 1 }}
          exit={{ opacity: 0, scale: 0.9 }}
          className="bg-[#FFF8F3] rounded-xl shadow-2xl w-full max-w-3xl max-h-[90vh] overflow-y-auto flex flex-col"
        >
          {/* En-tête */}
          <div className="sticky top-0 z-10 bg-[#FFF8F3] flex items-center justify-between p-4 border-b border-gray-200">
            <div className="flex items-center space-x-2">
              <Sparkles className="h-5 w-5 text-[#FF6B2C]" />
              <h2 className="text-xl font-semibold text-gray-800">Assistant IA - Création de mission</h2>
              <Tooltip title="Cette fonctionnalité est en version bêta et peut contenir des bugs.">
                <span className="ml-2 px-2 py-0.5 rounded-full text-xs font-bold bg-[#FF6B2C] text-white">beta</span>
              </Tooltip>
            </div>
            <button
              onClick={onClose}
              className="p-1 rounded-full hover:bg-gray-200 transition-colors"
            >
              <X className="h-5 w-5 text-gray-500" />
            </button>
          </div>

          {/* Contenu */}
          <div className="p-4">
            {/* Étape initiale - Description du besoin */}
            {currentStep === 0 && !isProcessing && !isCompleted && !showGeneratedResult && (
              <div className="space-y-4">
                <p className="text-gray-700">
                  {steps[currentStep].message}
                </p>
                <div className="bg-white rounded-lg border border-gray-300 p-3">
                  <textarea
                    value={userInput}
                    onChange={(e) => {
                      const newValue = e.target.value;
                      setUserInput(newValue);

                      // Si c'est la première modification, sauvegarder la valeur originale
                      if (!inputModified && originalUserInput === '') {
                        setOriginalUserInput(userInput);
                      }

                      // Vérifier si le texte a été modifié par rapport à l'original
                      // Si le texte est remis comme avant, masquer le message de régénération
                      if (originalUserInput !== '') {
                        const isModified = newValue.trim() !== originalUserInput.trim();
                        setInputModified(isModified);
                      }
                    }}
                    placeholder="Ex: Tondre mon jardin 200m² ce week-end chez moi à Perpignan. J'ai 100€ de budget. Je suis disponible mardi prochain dès 9 heures."
                    className="w-full h-32 resize-none outline-none"
                  />
                </div>

                <div className="bg-white rounded-lg border border-gray-300 p-3">
                  <label className="cursor-pointer group">
                    <div className="flex items-center gap-2">
                      <div className="relative flex items-center">
                        <input
                          type="checkbox"
                          checked={generatedData.is_urgent || false}
                          onChange={(e) => setGeneratedData(prev => ({ ...prev, is_urgent: e.target.checked }))}
                          className="peer sr-only"
                        />
                        <div className="w-4 h-4 border border-gray-300 rounded bg-white peer-checked:bg-[#FF6B2C] peer-checked:border-[#FF6B2C] transition-colors" />
                        <svg
                          className="absolute w-4 h-4 pointer-events-none opacity-0 peer-checked:opacity-100 text-white transition-opacity"
                          viewBox="0 0 24 24"
                          fill="none"
                          stroke="currentColor"
                          strokeWidth="3"
                          strokeLinecap="round"
                          strokeLinejoin="round"
                        >
                          <polyline points="20 6 9 17 4 12" />
                        </svg>
                      </div>
                      <span className="text-gray-700 font-medium">Mission urgente</span>
                    </div>
                    <p className="text-sm text-gray-500 mt-0.5 ml-6">
                      Cochez cette case si votre mission nécessite une intervention rapide.
                    </p>
                  </label>
                </div>

                <div className="flex flex-col space-y-3 mt-2">
                  {/* Bouton de régénération si le texte a été modifié après une génération */}
                  {inputModified && Object.keys(generatedData).length > 0 && (
                    <div className="space-y-3">
                      <div className={`flex flex-col sm:flex-row sm:items-center sm:justify-between rounded-lg p-3 space-y-2 sm:space-y-0 ${
                        credits >= 1
                          ? 'bg-[#FFF8F3] border border-[#FFE4BA]'
                          : 'bg-red-50 border border-red-200'
                      }`}>
                        <p className={`text-sm ${
                          credits >= 1 ? 'text-gray-700' : 'text-red-700'
                        }`}>
                          Vous avez modifié le texte. {credits >= 1
                            ? 'Choisissez comment vous souhaitez régénérer la mission avec l\'IA.'
                            : 'Vous avez besoin de crédits IA pour régénérer la mission.'
                          }
                        </p>
                        {credits >= 1 ? (
                          <button
                            onClick={() => {
                              if (!hasConsent) {
                                notify('Vous devez accepter les conditions d\'utilisation de l\'IA avant de pouvoir générer du contenu', 'error');
                                window.dispatchEvent(new CustomEvent('open-ai-consent-modal'));
                                return;
                              }
                              setShowGenerationChoice(true);
                            }}
                            className="sm:ml-3 px-3 py-1.5 bg-[#FF6B2C] text-white rounded-lg hover:bg-[#FF7A35] transition-colors flex items-center justify-center sm:justify-start text-sm whitespace-nowrap"
                          >
                            <Sparkles className="h-3.5 w-3.5 mr-1.5" />
                            Choisir le mode de génération
                          </button>
                        ) : (
                          <button
                            onClick={() => {
                              window.open('/dashboard/ai-credits', '_blank');
                            }}
                            className="sm:ml-3 px-3 py-1.5 bg-[#FF6B2C] text-white rounded-lg hover:bg-[#FF7A35] transition-colors flex items-center justify-center sm:justify-start text-sm whitespace-nowrap"
                          >
                            <Sparkles className="h-3.5 w-3.5 mr-1.5" />
                            Acheter des crédits IA
                          </button>
                        )}
                      </div>
                    </div>
                  )}
                </div>

                {/* Section d'information sur les crédits - uniquement si pas encore de génération */}
                {Object.keys(generatedData).length === 0 && (
                  <div className="mt-4">
                    {/* Informations sur les crédits */}
                    <div className="flex items-center justify-between mb-4 bg-gray-50 rounded-lg p-3">
                      <div className="flex items-center gap-3">
                        <div className={`h-10 w-10 rounded-full flex items-center justify-center ${
                          credits >= 1
                            ? 'bg-orange-100 text-[#FF6B2C]'
                            : 'bg-red-100 text-red-500'
                        }`}>
                          <Sparkles className="h-5 w-5" />
                        </div>
                        <div>
                          <p className="text-gray-500 text-sm">Vos crédits IA</p>
                          <p className={`font-bold text-lg ${
                            credits >= 1
                              ? 'text-gray-900'
                              : 'text-red-500'
                          }`}>
                            {credits} crédit{credits !== 1 ? 's' : ''}
                          </p>
                        </div>
                      </div>
                      <div className="text-right">
                        <p className="text-gray-500 text-sm">Coût minimum</p>
                        <p className="text-gray-900 font-bold text-lg">1 crédit IA</p>
                      </div>
                    </div>

                    {/* Alerte si crédits insuffisants */}
                    {credits < 1 && (
                      <div className="flex items-start gap-3 p-4 rounded-xl border border-red-200 bg-red-50 mb-4">
                        <AlertTriangle className="h-5 w-5 text-red-500 flex-shrink-0 mt-0.5" />
                        <div className="flex-1">
                          <p className="text-red-800 font-medium text-sm">Crédits insuffisants</p>
                          <p className="text-red-700 text-xs mt-1 mb-3">
                            Vous avez besoin d'au moins 1 crédit IA pour utiliser l'assistant de création de mission.
                            Votre solde actuel est de {credits} crédit{credits !== 1 ? 's' : ''}.
                          </p>
                          <button
                            onClick={() => {
                              window.open('/dashboard/ai-credits', '_blank');
                            }}
                            className="px-3 py-1.5 bg-[#FF6B2C] text-white text-xs rounded-lg hover:bg-[#FF7A35] transition-colors"
                          >
                            Acheter des crédits IA
                          </button>
                        </div>
                      </div>
                    )}
                  </div>
                )}
              </div>
            )}

            {/* Affichage du résultat généré pour l'étape actuelle */}
            {!isProcessing && !isCompleted && showGeneratedResult && currentStep === 0 && !isEditing && (
              <div className="space-y-4">
                <h3 className="text-lg font-medium text-gray-800">Résultat généré</h3>
                <p className="text-gray-600 mb-4">
                  {steps[currentStep].resultMessage}
                </p>

                <div className="bg-white rounded-lg border border-gray-300 p-4">
                  <div className="mb-4">
                    <div className="flex justify-between items-center">
                      <div className="flex items-center gap-2">
                        <p className="font-medium text-gray-700">Titre de la mission:</p>
                        {generatedData.is_urgent && (
                          <span className="bg-red-100 text-red-800 text-xs font-medium px-2.5 py-0.5 rounded flex items-center gap-1">
                            <AlertTriangle className="h-3 w-3" />
                            Urgent
                          </span>
                        )}
                      </div>
                      <button
                        onClick={() => startEditing('modify')}
                        className="text-[#FF6B2C] hover:text-[#FF7A35] text-sm flex items-center"
                      >
                        <Edit className="h-3.5 w-3.5 mr-1" />
                        Modifier
                      </button>
                    </div>
                    <p className="text-gray-800 text-lg font-semibold mt-1">{generatedData.titre}</p>
                  </div>

                  <div className="mb-2">
                    <div className="flex flex-col sm:flex-row sm:justify-between sm:items-start space-y-2 sm:space-y-0">
                      <p className="font-medium text-gray-700">Description:</p>
                      <div className="flex flex-wrap gap-2">
                        <button
                          onClick={() => regenerateContent('improve')}
                          disabled={credits <= 0}
                          className="text-[#FF6B2C] hover:text-[#FF7A35] text-sm flex items-center disabled:opacity-50 disabled:cursor-not-allowed"
                          title={credits <= 0 ? "Vous n'avez pas assez de crédits IA" : "Utilise 1 crédit IA"}
                        >
                          <Sparkles className="h-3.5 w-3.5 mr-1" />
                          Améliorer
                        </button>
                        <button
                          onClick={() => regenerateContent('shorten')}
                          disabled={credits <= 0}
                          className="text-[#FF6B2C] hover:text-[#FF7A35] text-sm flex items-center disabled:opacity-50 disabled:cursor-not-allowed"
                          title={credits <= 0 ? "Vous n'avez pas assez de crédits IA" : "Utilise 1 crédit IA"}
                        >
                          <ChevronLeft className="h-3.5 w-3.5 mr-1" />
                          Raccourcir
                        </button>
                        <button
                          onClick={() => regenerateContent('lengthen')}
                          disabled={credits <= 0}
                          className="text-[#FF6B2C] hover:text-[#FF7A35] text-sm flex items-center disabled:opacity-50 disabled:cursor-not-allowed"
                          title={credits <= 0 ? "Vous n'avez pas assez de crédits IA" : "Utilise 1 crédit IA"}
                        >
                          <ChevronRight className="h-3.5 w-3.5 mr-1" />
                          Développer
                        </button>
                      </div>
                    </div>
                    <p className="text-gray-600 whitespace-pre-wrap">{generatedData.description}</p>

                    {/* Avertissement sur les crédits IA */}
                    <CreditWarning credits={credits} />
                  </div>
                </div>

                <div className="flex flex-col items-center justify-center space-y-2">
                  <p className="text-sm text-gray-600">
                    Cliquez sur "Continuer" pour passer à l'étape suivante
                  </p>
                </div>
              </div>
            )}

            {/* Mode édition pour le titre et la description */}
            {!isProcessing && !isCompleted && showGeneratedResult && currentStep === 0 && isEditing && editType === 'modify' && (
              <div className="space-y-4">
                <h3 className="text-lg font-medium text-gray-800">Modifier le contenu</h3>

                <div className="bg-white rounded-lg border border-gray-300 p-4">
                  <div className="mb-4">
                    <p className="font-medium text-gray-700 mb-2">Titre de la mission:</p>
                    <input
                      type="text"
                      value={editedData.titre || ''}
                      onChange={(e) => setEditedData(prev => ({ ...prev, titre: e.target.value }))}
                      className="w-full p-2 border border-gray-300 rounded-md"
                      maxLength={70}
                    />
                    <p className="text-xs text-gray-500 mt-1 text-right">
                      {(editedData.titre?.length || 0)}/70 caractères
                    </p>
                  </div>

                  <div className="mb-2">
                    <p className="font-medium text-gray-700 mb-2">Description:</p>
                    <textarea
                      value={editedData.description || ''}
                      onChange={(e) => setEditedData(prev => ({ ...prev, description: e.target.value }))}
                      className="w-full p-2 border border-gray-300 rounded-md h-32 resize-none"
                      maxLength={1200}
                    />
                    <p className="text-xs text-gray-500 mt-1 text-right">
                      {(editedData.description?.length || 0)}/1200 caractères
                    </p>
                  </div>
                </div>

                {/* Les boutons sont maintenant dans la barre d'actions en bas */}
              </div>
            )}

            {/* Affichage des données générées pour les étapes suivantes */}
            {!isProcessing && !isCompleted && currentStep > 0 && !showGeneratedResult && (
              <div className="space-y-4">
                <h3 className="text-lg font-medium text-gray-800">{steps[currentStep].title}</h3>
                <p className="text-gray-600">
                  {steps[currentStep].message}
                </p>
              </div>
            )}

            {/* Affichage du résultat généré pour les étapes suivantes */}
            {!isProcessing && !isCompleted && showGeneratedResult && currentStep > 0 && !isEditing && (
              <div className="space-y-4">
                <h3 className="text-lg font-medium text-gray-800">Résultat pour : {steps[currentStep].title}</h3>
                {/* Afficher le message d'intro du résumé uniquement si le résumé existe */}
                {(currentStep !== 6 || (currentStep === 6 && generatedData.summary)) && (
                  <p className="text-gray-600 mb-4">
                    {steps[currentStep].resultMessage}
                  </p>
                )}

                {/* Afficher les données générées pour cette étape */}
                <div className="bg-white rounded-lg border border-gray-300 p-4">
                  {currentStep === 1 && (
                    <>
                      <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center mb-2 space-y-2 sm:space-y-0">
                        <h4 className="font-medium text-gray-800">Catégorie et sous-catégorie</h4>
                        <div className="flex space-x-2">
                          <button
                            onClick={() => regenerateContent('improve')}
                            disabled={credits <= 0}
                            className="text-[#FF6B2C] hover:text-[#FF7A35] text-sm flex items-center disabled:opacity-50 disabled:cursor-not-allowed"
                            title={credits <= 0 ? "Vous n'avez pas assez de crédits IA" : "Utilise 1 crédit IA"}
                          >
                            <Sparkles className="h-3.5 w-3.5 mr-1" />
                            Régénérer
                          </button>
                          <button
                            onClick={() => startEditing('modify')}
                            className="text-[#FF6B2C] hover:text-[#FF7A35] text-sm flex items-center"
                          >
                            <Edit className="h-3.5 w-3.5 mr-1" />
                            Modifier
                          </button>
                        </div>
                      </div>
                      <div className="mb-3">
                        <p className="font-medium text-gray-700">Catégorie:</p>
                        <p className="text-gray-800">
                          {SERVICE_CATEGORIES.find(c => c.id === generatedData.category_id)?.nom || 'Non définie'}
                        </p>
                      </div>
                      <div className="mb-2">
                        <p className="font-medium text-gray-700">Sous-catégorie:</p>
                        <p className="text-gray-800">
                          {SERVICE_SUBCATEGORIES.find(sc => sc.id === generatedData.subcategory_id)?.nom || 'Non définie'}
                        </p>
                      </div>

                      {/* Avertissement sur les crédits IA */}
                      <CreditWarning credits={credits} />
                    </>
                  )}

                  {currentStep === 2 && (
                    <>
                      <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center mb-2 space-y-2 sm:space-y-0">
                        <h4 className="font-medium text-gray-800">Informations de paiement</h4>
                        <div className="flex space-x-2">
                          <button
                            onClick={() => regenerateContent('improve')}
                            disabled={credits <= 0}
                            className="text-[#FF6B2C] hover:text-[#FF7A35] text-sm flex items-center disabled:opacity-50 disabled:cursor-not-allowed"
                            title={credits <= 0 ? "Vous n'avez pas assez de crédits IA" : "Utilise 1 crédit IA"}
                          >
                            <Sparkles className="h-3.5 w-3.5 mr-1" />
                            Régénérer
                          </button>
                          <button
                            onClick={() => startEditing('modify')}
                            className="text-[#FF6B2C] hover:text-[#FF7A35] text-sm flex items-center"
                          >
                            <Edit className="h-3.5 w-3.5 mr-1" />
                            Modifier
                          </button>
                        </div>
                      </div>
                      {renderBudgetResult()}
                      {/* Avertissement sur les crédits IA */}
                      <CreditWarning credits={credits} />
                    </>
                  )}

                  {currentStep === 3 && (
                    <>
                      <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center mb-2 space-y-2 sm:space-y-0">
                        <h4 className="font-medium text-gray-800">Localisation</h4>
                        <div className="flex space-x-2">
                          {addressValidated && (
                            <div className="text-green-500 text-sm flex items-center">
                              <CheckCircle className="h-3.5 w-3.5 mr-1" />
                              Adresse validée
                            </div>
                          )}
                          <button
                            onClick={() => regenerateContent('improve')}
                            disabled={credits <= 0}
                            className="text-[#FF6B2C] hover:text-[#FF7A35] text-sm flex items-center disabled:opacity-50 disabled:cursor-not-allowed"
                            title={credits <= 0 ? "Vous n'avez pas assez de crédits IA" : "Utilise 1 crédit IA"}
                          >
                            <Sparkles className="h-3.5 w-3.5 mr-1" />
                            Régénérer
                          </button>
                          <button
                            onClick={() => startEditing('modify')}
                            className="text-[#FF6B2C] hover:text-[#FF7A35] text-sm flex items-center"
                          >
                            <Edit className="h-3.5 w-3.5 mr-1" />
                            Modifier
                          </button>
                        </div>
                      </div>
                      <div className="mb-3">
                        <p className="font-medium text-gray-700">Adresse:</p>
                        <div className="flex items-center">
                          <p className="text-gray-800">{generatedData.adresse || 'Non définie'}</p>
                          {addressValidated && (
                            <CheckCircle className="h-4 w-4 ml-2 text-green-500" />
                          )}
                        </div>
                      </div>
                      <div className="mb-3">
                        <p className="font-medium text-gray-700">Code postal:</p>
                        <div className="flex items-center">
                          <p className="text-gray-800">{generatedData.code_postal || 'Non défini'}</p>
                          {addressValidated && (
                            <CheckCircle className="h-4 w-4 ml-2 text-green-500" />
                          )}
                        </div>
                      </div>
                      <div className="mb-2">
                        <p className="font-medium text-gray-700">Ville:</p>
                        <div className="flex items-center">
                          <p className="text-gray-800">{generatedData.ville || 'Non définie'}</p>
                          {addressValidated && (
                            <CheckCircle className="h-4 w-4 ml-2 text-green-500" />
                          )}
                        </div>
                      </div>

                      <div className="mb-3 bg-blue-50 border border-blue-200 rounded-lg p-2">
                        <div className="flex items-start space-x-2">
                          <Info className="h-4 w-4 text-blue-500 flex-shrink-0 mt-0.5" />
                          <p className="text-xs text-gray-600">
                            Votre adresse complète n'est pas partagée avec les autres utilisateurs de la plateforme.
                            Elle sert uniquement à localiser la mission par VILLE afin de la proposer aux utilisateurs proches de chez vous.
                          </p>
                        </div>
                      </div>

                      {/* Carte pour afficher la localisation */}
                      {addressValidated && (
                        <div className="mt-4 mb-4">
                          <p className="font-medium text-gray-700 mb-2">Zone de la mission :</p>
                          <div
                            className="h-[300px] w-full rounded-lg overflow-hidden border border-gray-300 relative"
                            style={{
                              position: 'relative',
                              zIndex: 1
                            }}
                          >
                            <InterventionZoneMap
                              center={generatedData.coordinates || _userProfile?.intervention_zone?.center || [48.8566, 2.3522]}
                              radius={mapRadius}
                              onZoneChange={(zone) => {
                                setMapRadius(zone.radius);
                                setGeneratedData(prev => ({
                                  ...prev,
                                  intervention_zone: zone
                                }));
                              }}
                              onAddressChange={(address) => {
                                setGeneratedData(prev => ({
                                  ...prev,
                                  adresse: address.adresse,
                                  code_postal: address.code_postal,
                                  ville: address.ville,
                                  pays: address.pays
                                }));
                              }}
                            />
                          </div>
                          <p className="text-xs text-gray-500 mt-1">
                            Vous pouvez déplacer le marqueur pour ajuster la position exacte.
                          </p>
                        </div>
                      )}

                      {!addressValidated && (
                        <div className="mt-3 mb-3 bg-[#FFF8F3] border border-[#FFE4BA] rounded-lg p-3">
                          <div className="flex flex-col space-y-3">
                            <div className="flex items-start space-x-2">
                              <AlertTriangle className="h-4 w-4 text-[#FF6B2C] flex-shrink-0 mt-0.5" />
                              <div className="flex-1">
                                <p className="text-sm text-gray-700 mb-1">
                                  <strong>La validation d'adresse est obligatoire</strong> pour continuer.
                                </p>
                                <p className="text-xs text-gray-600 mb-2">
                                  Note : Votre adresse complète n'est pas partagée avec les autres utilisateurs de la plateforme.
                                  Elle sert uniquement à localiser la mission par VILLE afin de la proposer aux utilisateurs proches de chez vous.
                                </p>
                              </div>
                            </div>
                            <button
                              onClick={async () => {
                                if (generatedData.adresse && generatedData.ville) {
                                  setIsValidatingAddress(true);
                                  try {
                                    // Construire une adresse complète pour la validation
                                    const fullAddress = `${generatedData.adresse} ${generatedData.code_postal} ${generatedData.ville}`;
                                    const validatedAddress = await validateAddress(fullAddress);

                                    if (validatedAddress.isValid) {
                                      setAddressValidated(true);
                                      // Mettre à jour les données avec l'adresse validée
                                      setGeneratedData(prev => ({
                                        ...prev,
                                        adresse: validatedAddress.adresse,
                                        code_postal: validatedAddress.code_postal,
                                        ville: validatedAddress.ville,
                                        pays: validatedAddress.pays,
                                        coordinates: validatedAddress.coordinates,
                                        intervention_zone: {
                                          center: validatedAddress.coordinates || _userProfile?.intervention_zone?.center || [48.8566, 2.3522],
                                          radius: mapRadius
                                        }
                                      }));
                                      notify('Adresse validée avec succès', 'success');
                                      // Fermer la modale de confirmation si elle est ouverte
                                      setShowAddressConfirmation(false);
                                    } else {
                                      setAddressValidated(false);
                                      notify('Impossible de valider cette adresse', 'warning');
                                      // Fermer la modale de confirmation si elle est ouverte
                                      setShowAddressConfirmation(false);
                                    }
                                  } catch (error) {
                                    console.error('Erreur lors de la validation d\'adresse:', error);
                                    notify('Erreur lors de la validation d\'adresse', 'error');
                                  } finally {
                                    setIsValidatingAddress(false);
                                  }
                                } else {
                                  notify('Adresse incomplète, impossible de valider. Utilisez le bouton "Modifier" pour corriger.', 'warning');
                                }
                              }}
                              className="w-full py-2 px-4 bg-[#FF6B2C] text-white rounded-lg hover:bg-[#FF7A35] transition-colors flex items-center justify-center space-x-2 disabled:opacity-50"
                              disabled={isValidatingAddress}
                            >
                              {isValidatingAddress ? (
                                <>
                                  <CircularProgress size={16} className="mr-2" />
                                  Validation en cours...
                                </>
                              ) : (
                                <>
                                  <Check className="h-4 w-4 mr-1" />
                                  Valider l'adresse
                                </>
                              )}
                            </button>
                          </div>
                        </div>
                      )}

                      {/* Avertissement sur les crédits IA */}
                      <CreditWarning credits={credits} />
                    </>
                  )}

                  {currentStep === 4 && (
                    <>
                      <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center mb-2 space-y-2 sm:space-y-0">
                        <h4 className="font-medium text-gray-800">Horaires</h4>
                        <div className="flex space-x-2">
                          <button
                            onClick={() => regenerateContent('improve')}
                            disabled={credits <= 0}
                            className="text-[#FF6B2C] hover:text-[#FF7A35] text-sm flex items-center disabled:opacity-50 disabled:cursor-not-allowed"
                            title={credits <= 0 ? "Vous n'avez pas assez de crédits IA" : "Utilise 1 crédit IA"}
                          >
                            <Sparkles className="h-3.5 w-3.5 mr-1" />
                            Régénérer
                          </button>
                          <button
                            onClick={() => startEditing('modify')}
                            className="text-[#FF6B2C] hover:text-[#FF7A35] text-sm flex items-center"
                          >
                            <Edit className="h-3.5 w-3.5 mr-1" />
                            Modifier
                          </button>
                        </div>
                      </div>
                      <div className="mb-3">
                        <p className="font-medium text-gray-700">Date de mission:</p>
                        <p className="text-gray-800">{generatedData.date_mission || 'Non définie'}</p>
                      </div>
                      <div className="mb-3">
                        <p className="font-medium text-gray-700">Préférence horaire:</p>
                        <p className="text-gray-800">{generatedData.has_time_preference ? 'Oui' : 'Non'}</p>
                      </div>
                      {generatedData.has_time_preference && generatedData.time_slots && generatedData.time_slots.length > 0 && (
                        <div className="mb-2">
                          <p className="font-medium text-gray-700">Créneaux horaires:</p>
                          {generatedData.time_slots.map((slot: any, index: number) => (
                            <p key={index} className="text-gray-800">
                              {slot.date} de {slot.start} à {slot.end}
                            </p>
                          ))}
                        </div>
                      )}

                      {/* Avertissement sur les crédits IA */}
                      <CreditWarning credits={credits} />
                    </>
                  )}

                  {currentStep === 5 && (
                    <>
                      <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center mb-2 space-y-2 sm:space-y-0">
                        <h4 className="font-medium text-gray-800">Photos de la mission</h4>
                        <div className="flex items-center space-x-2 text-sm bg-[#FFF8F3] px-2 py-1 rounded-md border border-[#FFE4BA]">
                          <span className="text-[#FF6B2C] font-semibold">{previewUrls.length}/8</span>
                          <span className="text-gray-600">photos ajoutées</span>
                        </div>
                      </div>
                      <div className="mb-4">
                        <p className="text-gray-700 mb-3">
                          Ajoutez jusqu'à 8 photos pour illustrer votre mission et augmenter vos chances de trouver un jobbeur.
                        </p>
                        {/* Grille d'images seule si photos */}
                        {previewUrls.length > 0 && StyledImageList()}
                        {/* Zone upload + IA côte à côte, toujours en dehors de la grille */}
                        <Box
                          sx={{
                            display: 'flex',
                            flexDirection: { xs: 'column', md: 'row' },
                            alignItems: 'stretch',
                            gap: { xs: 2, md: 3 },
                            width: '100%',
                            mt: previewUrls.length > 0 ? 2 : 0
                          }}
                        >
                          <Box sx={{ flex: 1, minWidth: 0, display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
                            {previewUrls.length < 8 && (
                              <label htmlFor="photo-upload" className="add-photo-label" style={{ width: '100%' }}>
                                <Box sx={{
                                  backgroundColor: '#fff',
                                  borderRadius: '12px',
                                  padding: '16px',
                                  textAlign: 'center',
                                  border: '2px dashed #FFE4BA',
                                  transition: 'all 0.2s ease',
                                  cursor: 'pointer',
                                  height: '140px',
                                  display: 'flex',
                                  flexDirection: 'column',
                                  justifyContent: 'center',
                                  alignItems: 'center',
                                  position: 'relative',
                                  overflow: 'hidden',
                                  '&:hover': {
                                    borderColor: '#FF6B2C',
                                    transform: 'translateY(-2px)',
                                    boxShadow: '0 4px 12px rgba(255, 107, 44, 0.1)',
                                    '& .upload-icon': {
                                      transform: 'scale(1.1) translateY(-2px)',
                                      color: '#FF6B2C',
                                    },
                                    '& .upload-text': {
                                      color: '#FF6B2C',
                                    }
                                  }
                                }}>
                                  <Upload
                                    className="upload-icon"
                                    size={24}
                                    style={{
                                      color: '#FF965E',
                                      marginBottom: '8px',
                                      transition: 'all 0.2s ease',
                                    }}
                                  />
                                  <Typography
                                    variant="body2"
                                    className="upload-text"
                                    sx={{
                                      color: '#2D3748',
                                      mb: 0.5,
                                      fontWeight: 500,
                                      fontSize: '0.9rem',
                                      transition: 'color 0.2s ease',
                                    }}
                                  >
                                    Déposez vos photos ici
                                  </Typography>
                                  <Typography
                                    variant="body2"
                                    className="browse-text"
                                    sx={{
                                      color: '#666',
                                      backgroundColor: '#FFF8F3',
                                      padding: '2px 8px',
                                      borderRadius: '8px',
                                      fontSize: '0.8rem',
                                      transition: 'all 0.2s ease',
                                    }}
                                  >
                                    ou parcourir
                                  </Typography>
                                  <Typography
                                    variant="caption"
                                    sx={{
                                      color: '#666',
                                      mt: 1,
                                      fontSize: '0.7rem',
                                      fontStyle: 'italic'
                                    }}
                                  >
                                    JPG, PNG, WEBP • 5 Mo max
                                  </Typography>
                                </Box>
                              </label>
                            )}
                          </Box>
                          <Box sx={{ flex: 1, minWidth: 0, display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
                            <Box sx={{ width: '100%' }}>
                              <AiMissionImageGenerator
                                onImageGenerated={handleAiImageGenerated}
                                missionTitle={generatedData.titre || ''}
                                missionDescription={generatedData.description || ''}
                                className="h-[140px] w-full"
                              />
                            </Box>
                          </Box>
                        </Box>
                        <input
                          type="file"
                          accept="image/jpeg,image/png,image/webp"
                          multiple
                          onChange={handleFileChange}
                          style={{ display: 'none' }}
                          id="photo-upload"
                          disabled={previewUrls.length >= 8}
                        />
                        <div className="mt-4 bg-[#FFF8F3] p-3 rounded-md border border-[#FFE4BA] text-sm text-gray-600">
                          <div className="flex items-start space-x-2">
                            <Info className="h-4 w-4 text-[#FF6B2C] flex-shrink-0 mt-0.5" />
                            <p>
                              Pour des raisons écologiques d'optimisation de l'espace de stockage, les photos seront automatiquement supprimées après 90 jours.
                            </p>
                          </div>
                        </div>
                      </div>
                    </>
                  )}

                  {currentStep === 6 && (
                    <>
                      <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center mb-2 space-y-2 sm:space-y-0">
                        <h4 className="font-medium text-gray-800">Résumé de la mission</h4>
                        <div className="flex space-x-2">
                          <button
                            onClick={() => regenerateContent('improve')}
                            disabled={credits <= 0}
                            className="text-[#FF6B2C] hover:text-[#FF7A35] text-sm flex items-center disabled:opacity-50 disabled:cursor-not-allowed"
                            title={credits <= 0 ? "Vous n'avez pas assez de crédits IA" : "Utilise 1 crédit IA"}
                          >
                            <Sparkles className="h-3.5 w-3.5 mr-1" />
                            {generatedData.summary ? 'Régénérer' : 'Générer'}
                          </button>
                        </div>
                      </div>

                      {generatedData.summary && (
                        <div className="mb-4">
                          <p className="whitespace-pre-wrap text-gray-700">{generatedData.summary}</p>

                          <div className="mt-3 bg-blue-50 border border-blue-200 rounded-lg p-2">
                            <div className="flex items-start space-x-2">
                              <Info className="h-4 w-4 text-blue-500 flex-shrink-0 mt-0.5" />
                              <p className="text-xs text-gray-600">
                                Rappel : Votre adresse complète n'est pas partagée avec les autres utilisateurs de la plateforme.
                                Elle sert uniquement à localiser la mission par VILLE afin de la proposer aux utilisateurs proches de chez vous.
                              </p>
                            </div>
                          </div>
                        </div>
                      )}

                      {/* Avertissement sur les crédits IA */}
                      <CreditWarning credits={credits} />
                    </>
                  )}
                </div>

                <div className="flex items-center justify-center">
                  <p className="text-sm text-gray-600">
                    Cliquez sur "Continuer" pour passer à l'étape suivante
                  </p>
                </div>
              </div>
            )}

            {/* Mode édition pour les catégories et sous-catégories */}
            {!isProcessing && !isCompleted && showGeneratedResult && currentStep === 1 && isEditing && editType === 'modify' && (
              <div className="space-y-4">
                <h3 className="text-lg font-medium text-gray-800">Modifier la catégorie et sous-catégorie</h3>

                <div className="bg-white rounded-lg border border-gray-300 p-4">
                  <div className="mb-4">
                    <p className="font-medium text-gray-700 mb-2">Catégorie:</p>
                    <select
                      value={editedData.category_id || ''}
                      onChange={(e) => {
                        const newCategoryId = e.target.value;
                        setEditedData(prev => ({
                          ...prev,
                          category_id: newCategoryId,
                          // Réinitialiser la sous-catégorie si on change de catégorie
                          subcategory_id: ''
                        }));
                      }}
                      className="w-full p-2 border border-gray-300 rounded-md bg-white"
                      size={8}
                    >
                      <option value="">Sélectionnez une catégorie</option>
                      {[...SERVICE_CATEGORIES]
                        .sort((a, b) => a.nom.localeCompare(b.nom, 'fr'))
                        .map(category => (
                          <option key={category.id} value={category.id}>
                            {category.nom}
                          </option>
                        ))}
                    </select>
                  </div>

                  <div className="mb-2">
                    <p className="font-medium text-gray-700 mb-2">Sous-catégorie:</p>
                    <select
                      value={editedData.subcategory_id || ''}
                      onChange={(e) => setEditedData(prev => ({ ...prev, subcategory_id: e.target.value }))}
                      className="w-full p-2 border border-gray-300 rounded-md bg-white"
                      disabled={!editedData.category_id}
                      size={6}
                    >
                      <option value="">Sélectionnez une sous-catégorie</option>
                      {SERVICE_SUBCATEGORIES
                        .filter(subcat => subcat.categoryId === editedData.category_id)
                        .sort((a, b) => a.nom.localeCompare(b.nom, 'fr'))
                        .map(subcat => (
                          <option key={subcat.id} value={subcat.id}>
                            {subcat.nom}
                          </option>
                        ))}
                    </select>
                    {!editedData.category_id && (
                      <p className="text-sm text-gray-500 mt-1">
                        Veuillez d'abord sélectionner une catégorie
                      </p>
                    )}
                  </div>
                </div>

                {/* Les boutons sont maintenant dans la barre d'actions en bas */}
              </div>
            )}

            {/* Mode édition pour le budget */}
            {!isProcessing && !isCompleted && showGeneratedResult && currentStep === 2 && isEditing && editType === 'modify' && (
              <div className="space-y-4">
                <h3 className="text-lg font-medium text-gray-800">Modifier le budget</h3>

                <div className="bg-white rounded-lg border border-gray-300 p-4">
                  <div className="mb-4">
                    <p className="font-medium text-gray-700 mb-2">Type de budget:</p>
                    <div className="flex flex-col space-y-2">
                      <label className="flex items-center space-x-2 cursor-pointer">
                        <input
                          type="radio"
                          checked={editedData.has_budget === false}
                          onChange={() => setEditedData(prev => ({
                            ...prev,
                            has_budget: false,
                            budget: 0,
                            budget_defini: false,
                            payment_method: undefined
                          }))}
                          className="form-radio text-[#FF6B2C]"
                        />
                        <span>Ne pas définir de budget</span>
                      </label>
                      <label className="flex items-center space-x-2 cursor-pointer">
                        <input
                          type="radio"
                          checked={editedData.has_budget === true}
                          onChange={() => setEditedData(prev => ({
                            ...prev,
                            has_budget: true,
                            budget: prev.budget || 0,
                            budget_defini: prev.budget_defini || false,
                            payment_method: prev.payment_method || 'euros'
                          }))}
                          className="form-radio text-[#FF6B2C]"
                        />
                        <span>Définir un budget</span>
                      </label>
                      {editedData.has_budget && (
                        <div className="ml-6 mt-2 flex items-center space-x-4">
                          <label className="flex items-center space-x-2 cursor-pointer">
                            <input
                              type="radio"
                              checked={editedData.budget_defini === true}
                              onChange={() => setEditedData(prev => ({ ...prev, budget_defini: true }))}
                              className="form-radio text-[#FF6B2C]"
                            />
                            <span>Budget fixe</span>
                          </label>
                          <label className="flex items-center space-x-2 cursor-pointer">
                            <input
                              type="radio"
                              checked={editedData.budget_defini === false}
                              onChange={() => setEditedData(prev => ({ ...prev, budget_defini: false }))}
                              className="form-radio text-[#FF6B2C]"
                            />
                            <span>Budget flexible/estimation</span>
                          </label>
                        </div>
                      )}
                    </div>
                  </div>

                  {editedData.has_budget && (
                    <>
                      <div className="mb-4">
                        <p className="font-medium text-gray-700 mb-2">Budget:</p>
                        <div className="flex items-center space-x-2">
                          <input
                            type="number"
                            min="0"
                            value={editedData.budget || 0}
                            onChange={(e) => setEditedData(prev => ({ ...prev, budget: parseInt(e.target.value) || 0 }))}
                            className="w-full p-2 border border-gray-300 rounded-md"
                          />
                        </div>
                      </div>

                      <div className="mb-2">
                        <p className="font-medium text-gray-700 mb-2">Méthode de paiement:</p>
                        <div className="flex flex-wrap gap-4">
                          <label className="flex items-center space-x-2 cursor-pointer">
                            <input
                              type="radio"
                              checked={editedData.payment_method === 'jobi'}
                              onChange={() => setEditedData(prev => ({ ...prev, payment_method: 'jobi' }))}
                              className="form-radio text-[#FF6B2C]"
                            />
                            <span>Jobi (troc/échange)</span>
                          </label>
                          <label className="flex items-center space-x-2 cursor-pointer">
                            <input
                              type="radio"
                              checked={editedData.payment_method === 'euros'}
                              onChange={() => setEditedData(prev => ({ ...prev, payment_method: 'euros' }))}
                              className="form-radio text-[#FF6B2C]"
                            />
                            <span>Euros</span>
                          </label>
                          <label className="flex items-center space-x-2 cursor-pointer">
                            <input
                              type="radio"
                              checked={editedData.payment_method === 'both'}
                              onChange={() => setEditedData(prev => ({ ...prev, payment_method: 'both' }))}
                              className="form-radio text-[#FF6B2C]"
                            />
                            <span>Les deux (au choix)</span>
                          </label>
                        </div>
                      </div>
                    </>
                  )}
                </div>
              </div>
            )}

            {/* Mode édition pour la localisation */}
            {!isProcessing && !isCompleted && showGeneratedResult && currentStep === 3 && isEditing && editType === 'modify' && (
              <div className="space-y-4">
                <h3 className="text-lg font-medium text-gray-800">Modifier la localisation</h3>

                <div className="bg-white rounded-lg border border-gray-300 p-4">
                  <div className="mb-3">
                    <div className="flex justify-between items-center mb-2">
                      <p className="font-medium text-gray-700">Adresse complète:</p>
                      {addressValidated && (
                        <div className="text-green-500 text-sm flex items-center">
                          <CheckCircle className="h-3.5 w-3.5 mr-1" />
                          Adresse validée
                        </div>
                      )}
                    </div>
                    <input
                      type="text"
                      value={editedData.adresse || ''}
                      onChange={(e) => {
                        setEditedData(prev => ({ ...prev, adresse: e.target.value }));
                        setAddressValidated(false);
                      }}
                      className="w-full p-2 border border-gray-300 rounded-md"
                      placeholder="Exemple: 12 rue de la Paix"
                    />
                    <div className="mt-1 bg-blue-50 border border-blue-200 rounded-lg p-2">
                      <div className="flex items-start space-x-2">
                        <Info className="h-4 w-4 text-blue-500 flex-shrink-0 mt-0.5" />
                        <p className="text-xs text-gray-600">
                          Votre adresse complète n'est pas partagée avec les autres utilisateurs de la plateforme.
                          Elle sert uniquement à localiser la mission par VILLE afin de la proposer aux utilisateurs proches de chez vous.
                        </p>
                      </div>
                    </div>
                  </div>

                  <div className="mb-3">
                    <p className="font-medium text-gray-700 mb-2">Code postal:</p>
                    <input
                      type="text"
                      value={editedData.code_postal || ''}
                      onChange={(e) => {
                        setEditedData(prev => ({ ...prev, code_postal: e.target.value }));
                        setAddressValidated(false);
                      }}
                      className="w-full p-2 border border-gray-300 rounded-md"
                      maxLength={5}
                      placeholder="Exemple: 75001"
                    />
                  </div>

                  <div className="mb-2">
                    <p className="font-medium text-gray-700 mb-2">Ville:</p>
                    <input
                      type="text"
                      value={editedData.ville || ''}
                      onChange={(e) => {
                        setEditedData(prev => ({ ...prev, ville: e.target.value }));
                        setAddressValidated(false);
                      }}
                      className="w-full p-2 border border-gray-300 rounded-md"
                      placeholder="Exemple: Paris"
                    />
                  </div>

                  {/* Carte pour afficher la localisation */}
                  {addressValidated && (
                    <div className="mt-4 mb-4">
                      <p className="font-medium text-gray-700 mb-2">Zone de la mission :</p>
                      <div
                        className="h-[300px] w-full rounded-lg overflow-hidden border border-gray-300 relative"
                        style={{
                          position: 'relative',
                          zIndex: 1
                        }}
                      >
                        <InterventionZoneMap
                          center={editedData.coordinates || _userProfile?.intervention_zone?.center || [48.8566, 2.3522]}
                          radius={mapRadius}
                          onZoneChange={(zone) => {
                            setMapRadius(zone.radius);
                            setEditedData(prev => ({
                              ...prev,
                              intervention_zone: zone
                            }));
                          }}
                          onAddressChange={(address) => {
                            setEditedData(prev => ({
                              ...prev,
                              adresse: address.adresse,
                              code_postal: address.code_postal,
                              ville: address.ville,
                              pays: address.pays
                            }));
                            setAddressValidated(true);
                          }}
                        />
                      </div>
                      <p className="text-xs text-gray-500 mt-1">
                        Vous pouvez déplacer le marqueur pour ajuster la position exacte.
                      </p>
                    </div>
                  )}

                  {!addressValidated && (
                    <div className="mt-3 bg-[#FFF8F3] border border-[#FFE4BA] rounded-lg p-3">
                      <div className="flex flex-col space-y-3">
                        <div className="flex items-start space-x-2">
                          <AlertTriangle className="h-4 w-4 text-[#FF6B2C] flex-shrink-0 mt-0.5" />
                          <div className="flex-1">
                            <p className="text-sm text-gray-700 mb-1">
                              <strong>La validation d'adresse est obligatoire</strong> pour enregistrer les modifications.
                            </p>
                            <p className="text-xs text-gray-600 mb-2">
                              Note : Votre adresse complète n'est pas partagée avec les autres utilisateurs de la plateforme.
                              Elle sert uniquement à localiser la mission par VILLE afin de la proposer aux utilisateurs proches de chez vous.
                            </p>
                          </div>
                        </div>
                        <button
                          onClick={async () => {
                            if (editedData.adresse && editedData.ville) {
                              setIsValidatingAddress(true);
                              try {
                                // Construire une adresse complète pour la validation
                                const fullAddress = `${editedData.adresse} ${editedData.code_postal} ${editedData.ville}`;
                                const validatedAddress = await validateAddress(fullAddress);

                                if (validatedAddress.isValid) {
                                  setAddressValidated(true);
                                  // Mettre à jour les données avec l'adresse validée
                                  setEditedData(prev => ({
                                    ...prev,
                                    adresse: validatedAddress.adresse,
                                    code_postal: validatedAddress.code_postal,
                                    ville: validatedAddress.ville,
                                    pays: validatedAddress.pays,
                                    coordinates: validatedAddress.coordinates,
                                    intervention_zone: {
                                      center: validatedAddress.coordinates || _userProfile?.intervention_zone?.center || [48.8566, 2.3522],
                                      radius: mapRadius
                                    }
                                  }));
                                  notify('Adresse validée avec succès', 'success');
                                  // Fermer la modale de confirmation si elle est ouverte
                                  setShowAddressConfirmation(false);
                                } else {
                                  setAddressValidated(false);
                                  notify('Impossible de valider cette adresse', 'warning');
                                  // Fermer la modale de confirmation si elle est ouverte
                                  setShowAddressConfirmation(false);
                                }
                              } catch (error) {
                                console.error('Erreur lors de la validation d\'adresse:', error);
                                notify('Erreur lors de la validation d\'adresse', 'error');
                              } finally {
                                setIsValidatingAddress(false);
                              }
                            } else {
                              notify('Adresse incomplète, impossible de valider', 'warning');
                            }
                          }}
                          className="w-full py-2 px-4 bg-[#FF6B2C] text-white rounded-lg hover:bg-[#FF7A35] transition-colors flex items-center justify-center space-x-2 disabled:opacity-50"
                          disabled={isValidatingAddress}
                        >
                          {isValidatingAddress ? (
                            <>
                              <CircularProgress size={16} className="mr-2" />
                              Validation en cours...
                            </>
                          ) : (
                            <>
                              <Check className="h-4 w-4 mr-1" />
                              Valider l'adresse
                            </>
                          )}
                        </button>
                      </div>
                    </div>
                  )}
                </div>

                {/* Les boutons sont maintenant dans la barre d'actions en bas */}
              </div>
            )}

            {/* Mode édition pour les horaires */}
            {!isProcessing && !isCompleted && showGeneratedResult && currentStep === 4 && isEditing && editType === 'modify' && (
              <div className="space-y-4">
                <h3 className="text-lg font-medium text-gray-800">Modifier les horaires</h3>

                <div className="bg-white rounded-lg border border-gray-300 p-4">
                  <div className="mb-4">
                    <p className="font-medium text-gray-700 mb-2">Date de mission:</p>
                    <input
                      type="date"
                      value={editedData.date_mission || ''}
                      onChange={(e) => {
                        const newDate = e.target.value;
                        setEditedData(prev => {
                          // Mettre à jour la date de mission
                          const updatedData = { ...prev, date_mission: newDate };

                          // Si des créneaux existent, mettre à jour leur date également
                          if (Array.isArray(updatedData.time_slots) && updatedData.time_slots.length > 0) {
                            // Demander à l'utilisateur s'il souhaite mettre à jour tous les créneaux
                            const shouldUpdateAll = window.confirm(
                              "Voulez-vous mettre à jour la date de tous les créneaux existants avec cette nouvelle date?"
                            );

                            if (shouldUpdateAll) {
                              updatedData.time_slots = updatedData.time_slots.map(slot => ({
                                ...slot,
                                date: newDate
                              }));
                            }
                          }

                          return updatedData;
                        });
                      }}
                      className="w-full p-2 border border-gray-300 rounded-md"
                    />
                  </div>

                  <div className="mb-4">
                    <p className="font-medium text-gray-700 mb-2">Préférence horaire:</p>
                    <div className="flex items-center space-x-4">
                      <label className="flex items-center space-x-2 cursor-pointer">
                        <input
                          type="radio"
                          checked={editedData.has_time_preference === true}
                          onChange={() => setEditedData(prev => ({
                            ...prev,
                            has_time_preference: true,
                            time_slots: prev.time_slots || [{ date: prev.date_mission || '', start: '', end: '' }]
                          }))}
                          className="form-radio text-[#FF6B2C]"
                        />
                        <span>Oui</span>
                      </label>
                      <label className="flex items-center space-x-2 cursor-pointer">
                        <input
                          type="radio"
                          checked={editedData.has_time_preference === false}
                          onChange={() => setEditedData(prev => ({ ...prev, has_time_preference: false, time_slots: [] }))}
                          className="form-radio text-[#FF6B2C]"
                        />
                        <span>Non</span>
                      </label>
                    </div>
                  </div>

                  {editedData.has_time_preference && (
                    <div className="mb-2">
                      <div className="flex justify-between items-center mb-2">
                        <p className="font-medium text-gray-700">Créneaux horaires:</p>
                        <button
          onClick={() => {
                            // Ajouter un créneau avec des valeurs par défaut
                            const today = new Date();
                            const formattedDate = today.toISOString().split('T')[0]; // Format YYYY-MM-DD

                            setEditedData(prev => ({
                              ...prev,
                              time_slots: [
                                ...(prev.time_slots || []),
                                {
                                  date: prev.date_mission || formattedDate,
                                  start: '08:00',
                                  end: '18:00'
                                }
                              ]
                            }));
                          }}
                          className="text-[#FF6B2C] hover:text-[#FF7A35] text-sm flex items-center"
                        >
                          <span>+ Ajouter un créneau</span>
                        </button>
                      </div>

                      {editedData.time_slots && editedData.time_slots.map((slot, index) => (
                        <div key={index} className="mb-3 p-3 border border-gray-200 rounded-lg">
                          <div className="flex justify-between items-center mb-2">
                            <p className="font-medium text-gray-700">Créneau {index + 1}</p>
                            {editedData.time_slots && editedData.time_slots.length > 1 && (
                              <button
                                onClick={() => {
                                  setEditedData(prev => ({
                                    ...prev,
                                    time_slots: prev.time_slots?.filter((_, i) => i !== index) || []
                                  }));
                                }}
                                className="text-red-500 hover:text-red-700 text-sm"
                              >
                                Supprimer
                              </button>
                            )}
                          </div>

                          <div className="grid grid-cols-1 sm:grid-cols-3 gap-3">
                            <div>
                              <p className="text-sm text-gray-600 mb-1">Date:</p>
                              <input
                                type="date"
                                value={slot.date || editedData.date_mission || ''}
                                onChange={(e) => {
                                  const newSlots = [...(editedData.time_slots || [])];
                                  newSlots[index] = { ...newSlots[index], date: e.target.value };
                                  setEditedData(prev => ({ ...prev, time_slots: newSlots }));
                                }}
                                className="w-full p-2 border border-gray-300 rounded-md"
                              />
                            </div>
                            <div>
                              <p className="text-sm text-gray-600 mb-1">Heure de début:</p>
                              <input
                                type="time"
                                value={slot.start || '08:00'}
                                onChange={(e) => {
                                  const newSlots = [...(editedData.time_slots || [])];
                                  newSlots[index] = { ...newSlots[index], start: e.target.value };

                                  // Vérifier si l'heure de début est après l'heure de fin
                                  const startTime = e.target.value;
                                  const endTime = newSlots[index].end || '18:00';

                                  if (startTime >= endTime) {
                                    // Calculer une nouvelle heure de fin (start + 1h)
                                    const [hours, minutes] = startTime.split(':').map(Number);
                                    let newHours = hours + 1;
                                    if (newHours > 23) newHours = 23;
                                    const newEndTime = `${String(newHours).padStart(2, '0')}:${String(minutes).padStart(2, '0')}`;

                                    newSlots[index].end = newEndTime;
                                  }

                                  setEditedData(prev => ({ ...prev, time_slots: newSlots }));
                                }}
                                className="w-full p-2 border border-gray-300 rounded-md"
                              />
                            </div>
                            <div>
                              <p className="text-sm text-gray-600 mb-1">Heure de fin:</p>
                              <input
                                type="time"
                                value={slot.end || '18:00'}
                                onChange={(e) => {
                                  const newSlots = [...(editedData.time_slots || [])];

                                  // Vérifier si l'heure de fin est avant l'heure de début
                                  const startTime = newSlots[index].start || '08:00';
                                  const endTime = e.target.value;

                                  if (endTime <= startTime) {
                                    // Afficher un message d'avertissement
                                    notify('L\'heure de fin doit être après l\'heure de début', 'warning');

                                    // Calculer une nouvelle heure de fin (start + 1h)
                                    const [hours, minutes] = startTime.split(':').map(Number);
                                    let newHours = hours + 1;
                                    if (newHours > 23) newHours = 23;
                                    const newEndTime = `${String(newHours).padStart(2, '0')}:${String(minutes).padStart(2, '0')}`;

                                    newSlots[index] = { ...newSlots[index], end: newEndTime };
                                  } else {
                                    newSlots[index] = { ...newSlots[index], end: endTime };
                                  }

                                  setEditedData(prev => ({ ...prev, time_slots: newSlots }));
                                }}
                                className="w-full p-2 border border-gray-300 rounded-md"
                              />
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  )}
                </div>

                {/* Les boutons sont maintenant dans la barre d'actions en bas */}
              </div>
            )}

            {/* Résumé final */}
            {isCompleted && (
              <div className="space-y-4">
                <div className="flex items-center space-x-2 mb-4">
                  <Check className="h-6 w-6 text-green-500" />
                  <h3 className="text-lg font-medium text-gray-800">Mission générée avec succès</h3>
                </div>

                <div className="bg-white rounded-lg border border-gray-300 p-4">
                  <h4 className="font-bold text-lg text-gray-800 mb-2">{generatedData.titre}</h4>
                  <p className="text-gray-700 mb-4">{generatedData.description}</p>

                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <p className="font-medium text-gray-700">Catégorie:</p>
                      <p className="text-gray-600">
                        {SERVICE_CATEGORIES.find(c => c.id === generatedData.category_id)?.nom || 'Non définie'}
                      </p>
                    </div>
                    <div>
                      <p className="font-medium text-gray-700">Sous-catégorie:</p>
                      <p className="text-gray-600">
                        {SERVICE_SUBCATEGORIES.find(sc => sc.id === generatedData.subcategory_id)?.nom || 'Non définie'}
                      </p>
                    </div>
                    <div>
                      <p className="font-medium text-gray-700">Budget:</p>
                      <p className="text-gray-600">
                        {generatedData.budget
                          ? `${generatedData.budget} ${generatedData.payment_method === 'jobi' || generatedData.payment_method === 'jobi_only'
                              ? 'Jobi'
                              : generatedData.payment_method === 'both'
                              ? 'Jobi ou €'
                              : '€'}${!generatedData.budget_defini ? ' (flexible/estimation)' : ' (fixe)'}`
                          : 'Budget non défini'}
                      </p>
                    </div>
                    <div>
                      <p className="font-medium text-gray-700">Localisation:</p>
                      <div>
                        <p className="text-gray-600">
                          {generatedData.ville ? `${generatedData.ville} (${generatedData.code_postal})` : 'Non définie'}
                        </p>
                        <p className="text-xs text-blue-600 mt-1">
                          <Info className="h-3 w-3 inline mr-1" />
                          Seule la ville est partagée avec les autres utilisateurs
                        </p>
                      </div>
                    </div>
                    <div>
                      <p className="font-medium text-gray-700">Mission urgente:</p>
                      <p className="text-gray-600">
                        {generatedData.is_urgent ? 'Oui' : 'Non'}
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {/* Affichage pendant le traitement */}
            {isProcessing && (
              <div className="space-y-4">
                <div className="flex items-center space-x-2 mb-4">
                  <Brain className="h-5 w-5 text-[#FF6B2C] animate-pulse" />
                  <h3 className="text-lg font-medium text-gray-800">Génération en cours...</h3>
                </div>

                <LinearProgress
                  variant="determinate"
                  value={progress}
                  sx={{
                    height: 8,
                    borderRadius: 4,
                    backgroundColor: '#FFE4BA',
                    '& .MuiLinearProgress-bar': {
                      backgroundColor: '#FF6B2C',
                    }
                  }}
                />

                <p className="text-center text-gray-600">
                  {aiStep}
                </p>
              </div>
            )}
          </div>

          {/* Actions */}
          <div className="sticky bottom-0 z-10 bg-[#FFF8F3] p-4 border-t border-gray-200 flex flex-col sm:flex-row justify-between space-y-2 sm:space-y-0">
            {!isCompleted ? (
              <>
                <button
                  onClick={() => {
                    if (isEditing) {
                      // Si on est en mode édition, annuler l'édition
                      cancelEditing();
                    } else if (showGeneratedResult) {
                      // Si on affiche un résultat, revenir à l'étape de saisie
                      setShowGeneratedResult(false);
                    } else if (currentStep === 0) {
                      onClose();
                    } else {
                      // Revenir à l'étape précédente
                      setCurrentStep(currentStep - 1);

                      // Vérifier si nous avons déjà des données pour cette étape
                      const prevStepHasData = checkIfStepHasData(currentStep - 1);

                      if (prevStepHasData) {
                        // Si nous avons déjà des données pour cette étape, les afficher sans régénérer
                        setShowGeneratedResult(true);
                      } else {
                        // Sinon, afficher l'écran de saisie
                        setShowGeneratedResult(false);
                      }
                    }
                  }}
                  disabled={isProcessing}
                  className="px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors disabled:opacity-50 w-full sm:w-auto"
                >
                  {isEditing ? 'Annuler' : (currentStep === 0 && !showGeneratedResult ? 'Annuler' : 'Précédent')}
                </button>

                {isEditing ? (
                  <div className="flex flex-col sm:flex-row space-y-2 sm:space-y-0 sm:space-x-2 w-full sm:w-auto">
                    <button
                      onClick={cancelEditing}
                      className="px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors w-full sm:w-auto"
                    >
                      Annuler
                    </button>
                    <button
                      onClick={saveEdits}
                      className="px-4 py-2 bg-[#FF6B2C] text-white rounded-lg hover:bg-[#FF7A35] transition-colors flex items-center justify-center sm:justify-start space-x-2 w-full sm:w-auto"
                    >
                      <span>Enregistrer</span>
                      <Check className="h-4 w-4" />
                    </button>
                  </div>
                ) : (
                  <button
                    onClick={processCurrentStep}
                    disabled={
                      isProcessing ||
                      isModerating ||
                      (!showGeneratedResult && currentStep === 0 && userInput.trim().length < 10) ||
                      isRateLimited ||
                      (!showGeneratedResult && !checkIfStepHasData(currentStep) && credits <= 0) ||
                      (currentStep === 3 && showGeneratedResult && !addressValidated) ||
                      // Désactiver le bouton si le message de modification est affiché
                      (currentStep === 0 && inputModified && Object.keys(generatedData).length > 0)
                    }
                    title={
                      currentStep === 3 && showGeneratedResult && !addressValidated
                        ? "Vous devez valider l'adresse avant de continuer"
                        : ""
                    }
                    className={`px-4 py-2 rounded-lg transition-colors flex items-center justify-center sm:justify-start space-x-2 w-full sm:w-auto ${
                      currentStep === 3 && showGeneratedResult && !addressValidated
                        ? "bg-yellow-500 text-white opacity-50 cursor-not-allowed border-2 border-dashed border-yellow-400"
                        : credits < 1 && currentStep === 0 && !checkIfStepHasData(currentStep)
                        ? "bg-gray-400 text-gray-600 cursor-not-allowed"
                        : "bg-[#FF6B2C] text-white hover:bg-[#FF7A35] disabled:opacity-50"
                    } ${
                      // Masquer complètement le bouton si le message de modification est affiché
                      (currentStep === 0 && inputModified && Object.keys(generatedData).length > 0)
                        ? "hidden"
                        : ""
                    }`}
                  >
                    {isProcessing ? (
                      <>
                        <CircularProgress size={20} thickness={5} sx={{ color: 'white' }} />
                        <span>Traitement...</span>
                      </>
                    ) : isModerating ? (
                      <>
                        <CircularProgress size={20} thickness={5} sx={{ color: 'white' }} />
                        <span>Modération en cours...</span>
                      </>
                    ) : currentStep === 3 && showGeneratedResult && !addressValidated ? (
                      <>
                        <AlertTriangle className="h-4 w-4 mr-1" />
                        <span>Validez l'adresse pour continuer</span>
                      </>
                    ) : (
                      <>
                        {showGeneratedResult ? (
                          <>
                            <span>{currentStep === steps.length - 1 ? 'Résumé de la mission' : 'Continuer'}</span>
                            <ChevronRight className="h-4 w-4" />
                          </>
                        ) : (
                          <>
                            {currentStep === 0 && !checkIfStepHasData(currentStep) ? (
                              <>
                                {credits >= 1 ? (
                                  <>
                                    <span>Choisir le mode de génération</span>
                                    <Sparkles className="h-4 w-4 ml-1" />
                                  </>
                                ) : (
                                  <>
                                    <span>Crédits insuffisants</span>
                                    <AlertTriangle className="h-4 w-4 ml-1" />
                                  </>
                                )}
                              </>
                            ) : checkIfStepHasData(currentStep) ? (
                              <>
                                <span>Continuer</span>
                                <ChevronRight className="h-4 w-4" />
                              </>
                            ) : (
                              <>
                                <span>Générer avec IA (1 crédit)</span>
                                <Sparkles className="h-4 w-4 ml-1" />
                              </>
                            )}
                          </>
                        )}
                      </>
                    )}
                  </button>
                )}
              </>
            ) : (
              <>
                <button
                  onClick={resetAssistant}
                  className="px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors"
                >
                  Recommencer
                </button>

                <button
                  onClick={handleFinalize}
                  disabled={isSubmitting || isModerating}
                  className="px-4 py-2 bg-[#FF6B2C] text-white rounded-lg hover:bg-[#FF7A35] transition-colors flex items-center space-x-2 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {isSubmitting ? (
                    <>
                      <CircularProgress size={16} thickness={5} sx={{ color: 'white' }} />
                      <span>Création en cours...</span>
                    </>
                  ) : isModerating ? (
                    <>
                      <CircularProgress size={16} thickness={5} sx={{ color: 'white' }} />
                      <span>Modération en cours...</span>
                    </>
                  ) : (
                    <>
                      <span>Créer ma mission</span>
                      <Check className="h-4 w-4" />
                    </>
                  )}
                </button>
              </>
            )}
          </div>
        </motion.div>
      </div>

      {/* Modal de modération d'image */}
      {isModerationModalOpen && (
        <ModalPortal>
          <div className="fixed inset-0 z-50 flex items-center justify-center">
            {/* Overlay avec gestion du clic */}
            <div
              className="fixed inset-0 bg-black bg-opacity-50"
              onClick={() => {
                // Toujours permettre l'annulation, même pendant le chargement
                setIsModerationModalOpen(false);
                if (moderationPreviewUrl) {
                  URL.revokeObjectURL(moderationPreviewUrl);
                  setModerationPreviewUrl(null);
                }
                setIsImageRejected(false);
                setRejectionDescription(undefined);
              }}
            />
            {/* Contenu de la modale */}
            <div className="relative bg-white rounded-2xl shadow-2xl w-[calc(100%-32px)] sm:w-full max-w-lg overflow-hidden flex flex-col">
              {/* Header */}
              <div className="p-4 pt-5 flex justify-between items-center border-b">
                <h2 className="text-xl font-semibold text-gray-800">
                  {isModerationLoading
                    ? `Analyse de l'image ${currentModerationIndex + 1}/${moderationImages.length}`
                    : isImageRejected
                      ? "Image refusée"
                      : `Modération de l'image ${currentModerationIndex + 1}/${moderationImages.length}`}
                </h2>
                <button
                  onClick={() => {
                    // Toujours permettre l'annulation, même pendant le chargement
                    setIsModerationModalOpen(false);
                    if (moderationPreviewUrl) {
                      URL.revokeObjectURL(moderationPreviewUrl);
                      setModerationPreviewUrl(null);
                    }
                    setIsImageRejected(false);
                    setRejectionDescription(undefined);
                  }}
                  className="p-2 hover:bg-gray-100 rounded-full"
                >
                  <X className="h-5 w-5 text-gray-500" />
                </button>
              </div>

              {/* Contenu scrollable */}
              <div className="flex-1 overflow-y-auto max-h-[calc(90vh-80px)]">
                {isModerationLoading || (!isModerationLoading && !isImageRejected) ? (
                  <ImageModerationStatus
                    isLoading={isModerationLoading}
                    imageUrl={moderationPreviewUrl ?? undefined}
                    title={isModerationLoading
                      ? `Analyse de l'image ${currentModerationIndex + 1}/${moderationImages.length}`
                      : `Modération de l'image ${currentModerationIndex + 1}/${moderationImages.length}`}
                    showPreview={true}
                    onCancel={() => {
                      // Toujours permettre l'annulation, même pendant le chargement
                      setIsModerationModalOpen(false);
                      if (moderationPreviewUrl) {
                        URL.revokeObjectURL(moderationPreviewUrl);
                        setModerationPreviewUrl(null);
                      }
                      setIsImageRejected(false);
                      setRejectionDescription(undefined);
                      setRejectionImprovementSuggestions(undefined);
                    }}
                  />
                ) : isImageRejected ? (
                  <div className="p-6">
                    {/* Afficher l'image refusée */}
                    {moderationPreviewUrl && (
                      <div className="mb-6 flex justify-center">
                        <div className="relative w-32 h-32 sm:w-40 sm:h-40 rounded-xl overflow-hidden border-4 border-white shadow-lg">
                          <img
                            src={moderationPreviewUrl}
                            alt="Image refusée"
                            className="w-full h-full object-cover"
                          />
                          <div className="absolute inset-0 bg-red-900/20"></div>
                        </div>
                      </div>
                    )}

                    {/* Message de rejet détaillé */}
                    <RejectedImageMessage
                      contentType="mission"
                      description={rejectionDescription || "Cette image ne respecte pas nos règles de modération."}
                      improvementSuggestions={rejectionImprovementSuggestions}
                      variant="detailed"
                    />

                    {/* Bouton pour réessayer */}
                    <div className="mt-6 flex justify-center">
                      <button
                        onClick={() => {
                          setIsModerationModalOpen(false);
                          if (moderationPreviewUrl) {
                            URL.revokeObjectURL(moderationPreviewUrl);
                            setModerationPreviewUrl(null);
                          }
                          setIsImageRejected(false);
                          setRejectionDescription(undefined);
                          setRejectionImprovementSuggestions(undefined);

                          // Permettre à l'utilisateur de choisir une autre image
                          notify("Veuillez sélectionner une autre image qui respecte nos règles de modération.", "info");
                        }}
                        className="px-6 py-3 bg-[#FF6B2C] text-white rounded-lg hover:bg-[#FF7A35] transition-all duration-300 shadow-md hover:shadow-xl"
                      >
                        Choisir une autre image
                      </button>
                    </div>
                  </div>
                ) : null}
              </div>
            </div>
          </div>
        </ModalPortal>
      )}
    </ModalPortal>
  );
};

export default AiMissionAssistant;
