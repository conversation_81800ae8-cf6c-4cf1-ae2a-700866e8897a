import { useState } from 'react';
import { verifyUserPassword } from '../utils/passwordVerification';

export const usePasswordVerification = () => {
  const [password, setPassword] = useState<string>('');
  const [passwordError, setPasswordError] = useState<string>('');
  const [verifyingPassword, setVerifyingPassword] = useState<boolean>(false);

  const verifyPassword = async (passwordToVerify?: string): Promise<boolean> => {
    const pwd = passwordToVerify || password;
    
    try {
      setVerifyingPassword(true);
      setPasswordError('');

      if (!pwd.trim()) {
        setPasswordError('Veuillez saisir votre mot de passe');
        return false;
      }

      const result = await verifyUserPassword(pwd);
      
      if (!result.success) {
        setPasswordError(result.message || 'Mot de passe incorrect');
        return false;
      }

      return true;
    } catch (error) {
      console.error('Erreur lors de la vérification du mot de passe:', error);
      setPasswordError('Erreur lors de la vérification du mot de passe');
      return false;
    } finally {
      setVerifyingPassword(false);
    }
  };

  const resetPassword = () => {
    setPassword('');
    setPasswordError('');
    setVerifyingPassword(false);
  };

  return {
    password,
    setPassword,
    passwordError,
    verifyingPassword,
    verifyPassword,
    resetPassword,
  };
};
