import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Grid,
  Paper,
  Card,
  CardContent,
  Button,
  Chip,
  <PERSON>lider,
  <PERSON>ack,
  useTheme,
  useMediaQuery,
  Avatar,
  LinearProgress,
  alpha,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Checkbox,
  IconButton,
  FormControlLabel,
  List,
  ListItem,
  ListItemText,
  Divider,
  CardMedia,
  styled,
  Alert,
  TextField
} from '@mui/material';
import {
  CheckCircle as CheckIcon,
  Cancel as XIcon,
  StarRate as StarIcon,
  Bolt as BoltIcon,
  Adjust as AdjustIcon,
  AddCircleOutline as AddIcon,
  CreditCard as CreditCardIcon,
  Receipt as ReceiptIcon,
  Upgrade as UpgradeIcon,
  Diamond as DiamondIcon,
  Settings as SettingsIcon,
  History as HistoryIcon,
  Close as CloseIcon,
  Info as InfoIcon,
  Category as CategoryIcon,
  ArrowForward as ArrowForwardIcon,
  Lock as LockIcon,
  CalendarMonth as CalendarMonthIcon,
  Assessment as AssessmentIcon,
  PhotoLibrary as PhotoLibraryIcon,
  SupportAgent as SupportAgentIcon,
  TrendingUp as TrendingUpIcon,
  CheckCircle as CheckCircleIcon,
  Cancel as CancelIcon,
  Favorite as Favorite,
  SmartToy as SmartToyIcon,
  BusinessCenter as BusinessCenterIcon,
  Description as DescriptionIcon
} from '@mui/icons-material';
import { motion, AnimatePresence } from 'framer-motion';
import { format } from 'date-fns';
import { fr } from 'date-fns/locale';
import { notify } from '../../components/Notification';
import { usePasswordVerification } from '../../hooks/usePasswordVerification';
import subscriptionService from '../../services/subscriptionService';
import InterventionZoneMap from './components/maps/InterventionZoneMap';
import ModalPortal from '../../components/ModalPortal';
import axios from 'axios';
import { API_CONFIG } from '../../config/api';
import { getCommonHeaders } from '../../utils/headers';
import { fetchCsrfToken } from '../../services/csrf';
import { SERVICE_CATEGORIES, ServiceCategory, ServiceSubcategory, SERVICE_SUBCATEGORIES } from './services/types';

// Types pour les données de souscription
interface SubscriptionStatus {
  success: boolean;
  plan: string;
  startDate: string;
  endDate: string | null;
  autoRenew: boolean;
  price: number;
  options: Record<string, any>;
  features: Record<string, boolean>;
  limits: Record<string, number>;
  selectedServices?: Array<{categoryId: string, subcategoryIds: string[]}>;
}

interface SubscriptionOption {
  name: string;
  key: string;
  included: number;
  current: number;
  additionalCost: number;
  unit: string;
  icon: React.ReactNode;
  description: string;
}

interface SubscriptionConfig {
  success: boolean;
  data: {
    gratuit: Record<string, any>;
    premium: Record<string, any>;
  };
}

interface PlanFeature {
  name: string;
  gratuit: string | number | boolean;
  premium: string | number | boolean;
  description?: string;
  descriptionPremium?: string;
  highlight?: boolean;
}

interface SelectedService {
  categoryId: string;
  category: ServiceCategory;
  subcategoryIds: string[];
}

// Types pour les valeurs de configuration
interface PriceConfig {
  freePlanBasePrice: number;
  premiumPlanBasePrice: number;
  franceEntierePrice: number;
  servicesAdditionalCost: number;
  maxIncludedServices: number;
}

// Ajout d'un type pour l'usage des codes promo
interface UserPromoCodeUsage {
  id: string;
  promo_code_id: string;
  plan_type: string;
  applied_at: string;
  promo_codes: {
    code: string;
    discount_type: string;
    discount_value: number;
    duration_type: string;
    description?: string;
    expires_at?: string;
  };
}

const PageTitle = styled(Typography)(() => ({
  fontSize: '1.5rem',
  fontWeight: 700,
  color: '#2D3748',
  marginBottom: '16px',
  position: 'relative',
  '&::after': {
    content: '""',
    position: 'absolute',
    bottom: '-8px',
    left: 0,
    width: '60px',
    height: '3px',
    background: '#FF6B2C',
    borderRadius: '2px',
  },
}));

// Description stylisée pour les catégories
const CategoryDescription = styled(Typography)(({ theme }) => ({
  background: '#FFF8F3',
  border: '1.5px solid #FFE4BA',
  color: '#2D3748',
  borderRadius: '12px',
  padding: theme.spacing(2, 2.5),
  marginBottom: theme.spacing(2.5),
  textAlign: 'center',
  fontSize: '1.05rem',
  fontWeight: 500,
  lineHeight: 1.6,
  boxShadow: '0 2px 8px rgba(255, 107, 44, 0.06)',
  width: '100%',
  minHeight: 38,
  letterSpacing: 0.05,
  wordBreak: 'break-word',
  transition: 'box-shadow 0.2s',
  '&:hover': {
    boxShadow: '0 4px 16px rgba(255, 122, 53, 0.10)',
    borderColor: '#FF7A35',
  },
}));

// Fonction utilitaire pour formater les dates en français
const formatDateToFrench = (dateString: string): string => {
  const date = new Date(dateString);
  return format(date, 'dd MMMM yyyy', { locale: fr });
};

const PremiumPage: React.FC = () => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
  const isTablet = useMediaQuery(theme.breakpoints.down('md'));

  const [loading, setLoading] = useState<boolean>(true);
  const [subscriptionStatus, setSubscriptionStatus] = useState<SubscriptionStatus | null>(null);
  const [subscriptionConfig, setSubscriptionConfig] = useState<SubscriptionConfig | null>(null);
  const [customizableOptions, setCustomizableOptions] = useState<SubscriptionOption[]>([]);
  const [selectedValues, setSelectedValues] = useState<Record<string, number>>({});
  const [totalPrice, setTotalPrice] = useState<number>(0);
  const [basePrice, setBasePrice] = useState<number>(19); // Prix par défaut en attendant le chargement
  const [activeTab, setActiveTab] = useState<'current' | 'pricing' | 'customize'>('current');
  const [zoneCenter, setZoneCenter] = useState<[number, number]>([48.8566, 2.3522]);

  // État pour le dialogue de confirmation de passage au plan gratuit
  const [showGratuitConfirmDialog, setShowGratuitConfirmDialog] = useState<boolean>(false);

  // État pour le dialogue de confirmation de mise à jour de l'abonnement
  const [showUpdateConfirmDialog, setShowUpdateConfirmDialog] = useState<boolean>(false);

  // Hook pour la vérification du mot de passe
  const { password, setPassword, passwordError, verifyingPassword, verifyPassword, resetPassword } = usePasswordVerification();

  // États pour les codes promo
  const [promoCode, setPromoCode] = useState<string>('');
  const [promoCodeValid, setPromoCodeValid] = useState<boolean | null>(null);
  const [promoCodeMessage, setPromoCodeMessage] = useState<string>('');
  const [promoCodeDetails, setPromoCodeDetails] = useState<any>(null);
  const [discountedPrice, setDiscountedPrice] = useState<number | null>(null);

  // Nouveaux états pour les valeurs de prix provenant de la configuration
  const [priceConfig, setPriceConfig] = useState<PriceConfig>({
    freePlanBasePrice: 0,
    premiumPlanBasePrice: 19, // Prix par défaut
    franceEntierePrice: 200,  // Prix par défaut
    servicesAdditionalCost: 2, // Prix par défaut
    maxIncludedServices: 15   // Valeur par défaut
  });

  // Nouveaux états pour la gestion des zones d'intervention
  const [isFranceEntiere, setIsFranceEntiere] = useState<boolean>(false);
  const [maxInterventionDistance, setMaxInterventionDistance] = useState<number>(100);

  // Nouveau state pour la gestion des services
  const [servicesDialogOpen, setServicesDialogOpen] = useState<boolean>(false);
  const [selectedServices, setSelectedServices] = useState<SelectedService[]>([]);
  const [serviceSubcategories, setServiceSubcategories] = useState<ServiceSubcategory[]>([]);
  const [currentServiceCount, setCurrentServiceCount] = useState<number>(0);
  const [maxIncludedServices, setMaxIncludedServices] = useState<number>(15);
  const [totalSubcategoriesCount, setTotalSubcategoriesCount] = useState<number>(0);
  // Nouvel état pour suivre les cartes dépliées
  const [expandedCards, setExpandedCards] = useState<Record<string, boolean>>({});

  // Ajout du state pour la liste des codes promo utilisés
  const [userPromoCodes, setUserPromoCodes] = useState<UserPromoCodeUsage[]>([]);
  const [loadingPromoCodes, setLoadingPromoCodes] = useState(false);

  // État pour la raison d'annulation
  const [cancelReason, setCancelReason] = useState('');

  // Fonction pour récupérer les données du profil utilisateur avec sa zone d'intervention
  const fetchUserProfile = async () => {
    try {
      await fetchCsrfToken();
      const headers = await getCommonHeaders();

      // Récupérer les données du profil utilisateur
      const response = await axios.get(`${API_CONFIG.baseURL}/api/users/profil`, {
        headers,
        withCredentials: true
      });

      // Extraire la zone d'intervention
      if (response.data?.profil?.data?.intervention_zone?.center) {
        const center = response.data.profil.data.intervention_zone.center;
        if (Array.isArray(center) && center.length >= 2) {
          setZoneCenter([center[0], center[1]]);
        }
      }
    } catch (error) {
      console.error("Erreur lors de la récupération du profil utilisateur:", error);
    }
  };

  // Charger les données du profil utilisateur au montage du composant
  useEffect(() => {
    fetchUserProfile();
  }, []);

  // Fonction pour récupérer les sous-catégories de services
  const fetchServiceSubcategories = async () => {
    try {
      // Récupérer et trier les sous-catégories par ordre alphabétique
      const sortedSubcategories = [...SERVICE_SUBCATEGORIES].sort((a, b) =>
        a.nom.localeCompare(b.nom, 'fr', { sensitivity: 'base' })
      );
      setServiceSubcategories(sortedSubcategories);

      // Définir le nombre total de sous-catégories disponibles
      setTotalSubcategoriesCount(SERVICE_SUBCATEGORIES.length);
    } catch (error) {
      console.error('Erreur lors du chargement des sous-catégories de services:', error);
      notify("Impossible de charger les sous-catégories de services", "warning");
    }
  };

  // Initialiser les services sélectionnés à partir de la configuration
  const initializeSelectedServices = (status: SubscriptionStatus) => {
    if (!status) return;

    if (status.selectedServices && status.selectedServices.length > 0) {
      // Si l'utilisateur a déjà des services sélectionnés
      const services: SelectedService[] = status.selectedServices.map(service => {
        const category = SERVICE_CATEGORIES.find(cat => cat.id === service.categoryId);
        if (category) {
          return {
            categoryId: service.categoryId,
            category,
            subcategoryIds: service.subcategoryIds
          };
        }
        return {
          categoryId: service.categoryId,
          category: SERVICE_CATEGORIES[0], // Fallback
          subcategoryIds: service.subcategoryIds
        };
      });
      setSelectedServices(services);
      setCurrentServiceCount(countSelectedServices(services));
    } else {
      // Si c'est un nouvel utilisateur ou sans services sélectionnés
      setSelectedServices([]);
      setCurrentServiceCount(0);
    }
  };

  // Compter le nombre total de services sélectionnés
  const countSelectedServices = (services: SelectedService[]): number => {
    return services.reduce((count, service) => count + service.subcategoryIds.length, 0);
  };

  // Gérer l'ouverture du dialogue de sélection des services
  const handleOpenServicesDialog = () => {
    setServicesDialogOpen(true);
  };

  // Gérer la fermeture du dialogue de sélection des services
  const handleCloseServicesDialog = () => {
    setServicesDialogOpen(false);
  };

  // Gérer la sélection d'une sous-catégorie de service
  const handleServiceSelection = (categoryId: string, subcategoryId: string) => {
    const updatedServices = [...selectedServices];
    const categoryIndex = updatedServices.findIndex(s => s.categoryId === categoryId);

    // Vérifier si le nombre maximum de services est atteint
    const currentTotal = countSelectedServices(updatedServices);
    const isSelected = categoryIndex >= 0 && updatedServices[categoryIndex].subcategoryIds.includes(subcategoryId);

    if (currentTotal >= maxIncludedServices && !isSelected) {
      // Si on dépasse le quota inclus, augmenter automatiquement le quota total
      const newQuota = Math.min(currentTotal + 1, totalSubcategoriesCount); // Limite max au nombre de sous-catégories
      handleOptionChange('services', newQuota);

      // Récupérer le coût additionnel par service
      // const serviceCost = subscriptionConfig?.data?.premium?.services?.additionalCost || 2;

      // Notification pour informer l'utilisateur
      // notify(`Votre quota de services a été automatiquement augmenté à ${newQuota}. Les services au-delà de ${maxIncludedServices} sont facturés ${serviceCost}€ par service supplémentaire.`, 'info');
    }

    if (categoryIndex >= 0) {
      // La catégorie existe déjà
      const subcategoryIndex = updatedServices[categoryIndex].subcategoryIds.indexOf(subcategoryId);

      if (subcategoryIndex >= 0) {
        // Désélectionner la sous-catégorie
        updatedServices[categoryIndex].subcategoryIds.splice(subcategoryIndex, 1);
        // Supprimer la catégorie si elle n'a plus de sous-catégories
        if (updatedServices[categoryIndex].subcategoryIds.length === 0) {
          updatedServices.splice(categoryIndex, 1);
        }
      } else {
        // Ajouter la sous-catégorie
        updatedServices[categoryIndex].subcategoryIds.push(subcategoryId);
      }
    } else {
      // Ajouter une nouvelle catégorie avec la sous-catégorie
      const category = SERVICE_CATEGORIES.find(cat => cat.id === categoryId);
      if (category) {
        updatedServices.push({
          categoryId,
          category,
          subcategoryIds: [subcategoryId]
        });
      }
    }

    setSelectedServices(updatedServices);
    const newCount = countSelectedServices(updatedServices);
    setCurrentServiceCount(newCount);

    // Mettre à jour le nombre de services sélectionnés dans les options
    handleOptionChange('services', Math.max(newCount, maxIncludedServices));
  };

  // Vérifier si une sous-catégorie est sélectionnée
  const isSubcategorySelected = (categoryId: string, subcategoryId: string): boolean => {
    const category = selectedServices.find(s => s.categoryId === categoryId);
    return category ? category.subcategoryIds.includes(subcategoryId) : false;
  };

  // Initialisation des données et chargement des sous-catégories de services
  useEffect(() => {
    fetchServiceSubcategories();
  }, []);

  // Initialiser les données des prix depuis la configuration
  const initializePriceConfig = (config: SubscriptionConfig) => {
    if (!config || !config.data) return;

    const newPriceConfig: PriceConfig = {
      freePlanBasePrice: config.data.gratuit.prixDeBase || 0,
      premiumPlanBasePrice: config.data.premium.prixDeBase || 19,
      franceEntierePrice: config.data.premium.interventionAreas?.franceEntiere?.additionalCost || 200,
      servicesAdditionalCost: config.data.premium.services?.additionalCost || 2,
      maxIncludedServices: config.data.premium.services?.included || 15
    };

    setPriceConfig(newPriceConfig);
    setBasePrice(newPriceConfig.premiumPlanBasePrice);
  };

  // Chargement des données de souscription et de configuration
  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);

        // Récupérer le statut d'abonnement de l'utilisateur
        const statusResponse = await subscriptionService.getSubscriptionStatus();
        setSubscriptionStatus(statusResponse);

        // Récupérer la configuration des abonnements
        const configResponse = await subscriptionService.getSubscriptionConfig();
        setSubscriptionConfig(configResponse);

        // Initialiser les prix à partir de la configuration
        if (configResponse) {
          initializePriceConfig(configResponse);
        }

        // Initialiser les services sélectionnés
        initializeSelectedServices(statusResponse);

        // Définir l'onglet actif en fonction de l'abonnement
        if (statusResponse && statusResponse.plan === 'premium') {
          setActiveTab('current');

          // Vérifier si l'option France entière est activée
          if (statusResponse.options && statusResponse.options.franceEntiere) {
            setIsFranceEntiere(true);
          }
        } else {
          setActiveTab('pricing');
        }

        if (configResponse && configResponse.data && configResponse.data.premium) {
          setMaxIncludedServices(configResponse.data.premium.services.included || 15);

          // Récupérer la distance maximale d'intervention si disponible
          if (configResponse.data.premium.interventionAreas && configResponse.data.premium.interventionAreas.maxDistance) {
            setMaxInterventionDistance(configResponse.data.premium.interventionAreas.maxDistance);
          }
        }

        // Initialiser les options personnalisables
        if (statusResponse && configResponse) {
          initializeCustomizableOptions(statusResponse, configResponse);
        }
      } catch (error) {
        console.error("Erreur lors du chargement des données d'abonnement:", error);
        notify("Impossible de charger les informations d'abonnement", "error");
      } finally {
        setLoading(false);
      }
    };

    fetchData();

    // Exposer fetchData pour pouvoir l'appeler ailleurs
    (window as any).fetchSubscriptionData = fetchData;
  }, []);

  // Extrait fetchData depuis la référence window pour l'utiliser en dehors de useEffect
  const fetchSubscriptionData = () => {
    if ((window as any).fetchSubscriptionData) {
      (window as any).fetchSubscriptionData();
    }
  };

  // Initialiser les options personnalisables à partir de la configuration
  const initializeCustomizableOptions = (status: SubscriptionStatus, config: SubscriptionConfig) => {
    if (!status || !config || !config.data) return;

    const options: SubscriptionOption[] = [];
    const initialSelectedValues: Record<string, number> = {};

    // Prix de base premium
    if (config.data.premium && config.data.premium.prixDeBase) {
      setBasePrice(config.data.premium.prixDeBase);
    }

    // Options personnalisables avec coûts additionnels
    const customizableKeys = ['interventionAreas', 'services', 'galleries', 'conversations_messages_prives'];

    const icons = {
      interventionAreas: <AdjustIcon sx={{ color: '#FF7A35', fontSize: 28 }} />,
      services: <CategoryIcon sx={{ color: '#FF7A35', fontSize: 28 }} />,
      galleries: <StarIcon sx={{ color: '#FF7A35', fontSize: 28 }} />,
      conversations_messages_prives: <AddIcon sx={{ color: '#FF7A35', fontSize: 28 }} />
    };

    const descriptions = {
      interventionAreas: "Élargissez votre zone de chalandise pour atteindre plus de clients potentiels",
      services: "Diversifiez votre offre de services pour attirer plus de clients",
      galleries: "Présentez plus de projets dans votre portfolio pour mettre en valeur votre travail",
      conversations_messages_prives: "Nombre de conversations privées que vous pouvez créer par mois"
    };

    customizableKeys.forEach(key => {
      if (config.data.premium[key]) {
        const optionConfig = config.data.premium[key];
        const currentValue = key === 'services'
          ? Math.max(status.options[key] || optionConfig.included, currentServiceCount)
          : status.options[key] || optionConfig.included;

        // Ajouter à la liste des options personnalisables
        options.push({
          name: getOptionName(key),
          key,
          included: optionConfig.included,
          current: currentValue,
          additionalCost: optionConfig.additionalCost,
          unit: getOptionUnit(key),
          icon: icons[key as keyof typeof icons],
          description: descriptions[key as keyof typeof descriptions]
        });

        // Initialiser les valeurs sélectionnées
        initialSelectedValues[key] = currentValue;
      }
    });

    setCustomizableOptions(options);
    setSelectedValues(initialSelectedValues);

    // Calculer le prix total initial
    calculateTotalPrice(initialSelectedValues, config.data.premium);
  };

  // Obtenir le nom lisible de l'option
  const getOptionName = (key: string): string => {
    const names: Record<string, string> = {
      interventionAreas: 'Zones d\'intervention',
      services: 'Services',
      galleries: 'Galeries',
      conversations_messages_prives: 'Conversations privées',
    };
    return names[key] || key;
  };

  // Obtenir l'unité de l'option
  const getOptionUnit = (key: string): string => {
    const units: Record<string, string> = {
      interventionAreas: 'km',
      services: '',
      galleries: '',
      conversations_messages_prives: '',
    };
    return units[key] || '';
  };

  // Mettre à jour la valeur sélectionnée pour une option
  const handleOptionChange = (key: string, value: number) => {
    // Empêcher la modification des interventionAreas si France entière est activée
    if (key === 'interventionAreas' && isFranceEntiere) {
      return;
    }

    const newSelectedValues = { ...selectedValues, [key]: value };
    setSelectedValues(newSelectedValues);

    // Recalculer le prix total
    if (subscriptionConfig && subscriptionConfig.data) {
      calculateTotalPrice(newSelectedValues, subscriptionConfig.data.premium);
    }
  };

  // Gérer le changement de l'option France entière
  const handleFranceEntiereChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const checked = event.target.checked;
    setIsFranceEntiere(checked);

    // Recalculer le prix total avec l'option France entière
    if (subscriptionConfig && subscriptionConfig.data) {
      // Si on active France entière, forcer la réinitialisation complète des interventionAreas
      if (checked) {
        const includedValue = subscriptionConfig.data.premium.interventionAreas?.included || 0;

        const newSelectedValues = { ...selectedValues };

        newSelectedValues.interventionAreas = includedValue;

        setSelectedValues(newSelectedValues);

        // Calculer le prix avec France entière activée
        calculateTotalPrice(newSelectedValues, subscriptionConfig.data.premium, true);
      } else {
        // Sinon, on reprend le calcul normal avec les zones d'intervention
        calculateTotalPrice(selectedValues, subscriptionConfig.data.premium, false);
      }
    }
  };

  // Calculer le prix total en fonction des options sélectionnées
  const calculateTotalPrice = (selected: Record<string, number>, premiumConfig: Record<string, any>, useFranceEntiere: boolean = isFranceEntiere) => {

    // Commencer avec le prix de base
    let total = basePrice;

    // Parcourir toutes les options et ajouter leur coût si nécessaire
    Object.entries(selected).forEach(([key, value]) => {
      // Ignorer l'option interventionAreas si France entière est activée
      if (key === 'interventionAreas' && useFranceEntiere) {
        return; // Ne pas calculer le coût des zones d'intervention si France entière est activée
      }

      const config = premiumConfig[key];
      if (config && value > config.included) {
        // Calculer le coût supplémentaire selon le type d'option
        if (key === 'interventionAreas') {
          // Pour les zones d'intervention, calculer par tranches de 10
          const additionalZones = value - config.included;
          const additionalCost = Math.ceil(additionalZones / 10) * config.additionalCost;
          total += additionalCost;
        } else {
          // Pour les autres options, calculer par unité supplémentaire
          const addCost = (value - config.included) * config.additionalCost;
          total += addCost;
        }
      }
    });

    // Ajouter le coût de l'option France entière si elle est activée
    if (useFranceEntiere && premiumConfig.interventionAreas && premiumConfig.interventionAreas.franceEntiere) {
      const franceEntiereCost = premiumConfig.interventionAreas.franceEntiere.additionalCost || priceConfig.franceEntierePrice;
      total += franceEntiereCost;
    }

    // Mettre à jour le prix total
    setTotalPrice(total);

    // Appliquer la réduction du code promo si valide
    if (promoCodeValid && promoCodeDetails) {
      let discount = 0;
      if (promoCodeDetails.discount_type === 'percentage') {
        discount = total * (promoCodeDetails.discount_value / 100);
      } else if (promoCodeDetails.discount_type === 'fixed') {
        discount = promoCodeDetails.discount_value;
      }
      setDiscountedPrice(Math.max(0, total - discount));
    } else {
      setDiscountedPrice(null);
    }
  };

  // Gérer le clic sur le bouton de souscription
  const handleSubscribe = () => {
    // Afficher la boîte de dialogue de confirmation
    setShowUpdateConfirmDialog(true);
  };



  // Confirmer et procéder à la mise à jour de l'abonnement
  const confirmSubscribeUpdate = async () => {
    try {
      // Vérifier d'abord le mot de passe
      const passwordValid = await verifyPassword();
      if (!passwordValid) {
        return; // L'erreur est déjà affichée par verifyPassword
      }

      setLoading(true);
      setShowUpdateConfirmDialog(false); // Fermer la boîte de dialogue

      // Réinitialiser les champs de mot de passe
      resetPassword();

      // Préparer les données des services sélectionnés pour l'envoi
      const servicesData = selectedServices.map(service => ({
        categoryId: service.categoryId,
        subcategoryIds: service.subcategoryIds
      }));

      // Préparer les données de base de l'abonnement
      const baseSubscriptionData = {
        ...selectedValues,
        selectedServices: servicesData,
        franceEntiere: isFranceEntiere,
        promoCode: promoCode // Ajouter le code promo
      };

      // Créer l'objet final à envoyer, y compris les détails du prix réduit si un code promo est appliqué
      const finalSubscriptionData: Record<string, any> = {
        ...baseSubscriptionData
      };

      // Si un code promo valide est appliqué, inclure le prix réduit dans les données
      if (promoCodeValid && promoCodeDetails && discountedPrice !== null) {
        finalSubscriptionData.discountedPrice = discountedPrice;
        finalSubscriptionData.originalPrice = totalPrice;
        finalSubscriptionData.discountDetails = {
          code: promoCodeDetails.code,
          type: promoCodeDetails.discount_type,
          value: promoCodeDetails.discount_value
        };
      }

      const response = await subscriptionService.createSubscription('premium', finalSubscriptionData);

      if (response.success) {
        if (response.url) {
          // Cas de création d'abonnement - redirection vers Stripe
          window.location.href = response.url;
        } else if (response.message) {
          // Cas de modification d'abonnement - afficher le message de succès
          notify(response.message, response.toastType || "success");
          // Recharger les données de souscription pour refléter les changements
          fetchSubscriptionData();
        } else {
          notify("Abonnement traité avec succès", "success");
          fetchSubscriptionData();
        }
      } else {
        notify(response.message || "Impossible de créer l'abonnement", "error");
      }
    } catch (error) {
      console.error("Erreur lors de la création de l'abonnement:", error);
      notify("Une erreur est survenue lors de la création de l'abonnement", "error");
    } finally {
      setLoading(false);
    }
  };

  // Fermer la boîte de dialogue de confirmation de mise à jour sans action
  const handleCloseUpdateConfirmDialog = () => {
    setShowUpdateConfirmDialog(false);
    // Réinitialiser les champs de mot de passe
    resetPassword();
  };

  // Gérer le clic sur le bouton de souscription au plan gratuit
  const handleSubscribeGratuit = async () => {
    try {
      setLoading(true);
      setShowGratuitConfirmDialog(false); // Fermer la boîte de dialogue

      const response = await subscriptionService.createSubscription('gratuit', {
        autoRenew: false, // Désactiver le renouvellement automatique
        raison_annulation_abonnement: cancelReason
      });

      if (response.success) {
        if (response.message) {
          notify(response.message, "success");
        } else {
          notify("Vous êtes passé au plan Gratuit. Votre abonnement Premium restera actif jusqu'à sa date d'expiration.", "success");
        }
        // Recharger les données de souscription
        fetchSubscriptionData();
      } else {
        notify("Impossible de basculer vers le plan gratuit", "error");
      }
    } catch (error) {
      console.error("Erreur lors du changement vers le plan gratuit:", error);
      notify("Une erreur est survenue lors du changement de plan", "error");
    } finally {
      setLoading(false);
      setCancelReason(''); // Reset après action
    }
  };

  // Gérer l'ouverture du dialogue de confirmation pour passer au plan gratuit
  const handleOpenGratuitConfirmDialog = () => {
    // Si l'utilisateur est déjà sur le plan premium, on lui demande de confirmer
    if (subscriptionStatus?.plan === 'premium') {
      setShowGratuitConfirmDialog(true);
    } else {
      // Sinon, on peut directement passer à l'abonnement gratuit
      handleSubscribeGratuit();
    }
  };

  // Fermer la boîte de dialogue de confirmation sans action
  const handleCloseGratuitConfirmDialog = () => {
    setShowGratuitConfirmDialog(false);
  };

  // Gérer le clic sur le bouton de gestion de l'abonnement
  const handleManageSubscription = async () => {
    try {
      setLoading(true);
      const response = await subscriptionService.createPortalSession();

      if (response.success && response.url) {
        window.location.href = response.url;
      } else {
        notify("Impossible d'accéder au portail client", "error");
      }
    } catch (error) {
      console.error("Erreur lors de l'accès au portail client:", error);
      notify("Une erreur est survenue lors de l'accès au portail client", "error");
    } finally {
      setLoading(false);
    }
  };

  // Changer d'onglet
  const handleTabChange = (tab: 'current' | 'pricing' | 'customize') => {
    setActiveTab(tab);
    if (tab === 'customize') {
      window.scrollTo({ top: 0, behavior: 'smooth' });
    }
  };

  // Définir les styles communs
  const cardStyle = {
    height: '100%',
    borderRadius: 3,
    overflow: 'hidden',
    transition: 'all 0.3s ease',
    boxShadow: '0 8px 20px rgba(0, 0, 0, 0.06)',
    '&:hover': {
      transform: 'translateY(-5px)',
      boxShadow: '0 12px 28px rgba(0, 0, 0, 0.1)'
    }
  };

  const tabStyle = (isActive: boolean) => ({
    py: 1.2,
    px: 3,
    borderRadius: '30px',
    fontWeight: 'medium',
    color: isActive ? 'white' : 'text.primary',
    bgcolor: isActive ? '#FF7A35' : 'transparent',
    border: isActive ? 'none' : '1px solid #e0e0e0',
    transition: 'all 0.3s ease',
    cursor: 'pointer',
    '&:hover': {
      bgcolor: isActive ? '#FF7A35' : alpha('#FF7A35', 0.08),
      transform: 'translateY(-2px)',
      boxShadow: isActive ? '0 6px 15px rgba(255, 122, 53, 0.2)' : '0 4px 10px rgba(0, 0, 0, 0.05)'
    }
  });

  // Rendu du composant pour l'abonnement actuel
  const renderCurrentSubscription = () => {
    if (!subscriptionStatus) return null;
    const isPremium = subscriptionStatus.plan === 'premium';

    // Date de fin formatée ou indication
    const endDateLabel = subscriptionStatus.endDate ? formatDateToFrench(subscriptionStatus.endDate) : 'Renouvellement automatique';

    // Déterminer si c'est un cas de transition (plan premium actif mais renouvellement automatique désactivé)
    const isTransitioning = subscriptionStatus.plan === 'premium' && subscriptionStatus.autoRenew === false;

    // Nom du plan à afficher
    const planDisplayName = isTransitioning && subscriptionStatus.endDate
      ? `Premium (jusqu'au ${formatDateToFrench(subscriptionStatus.endDate)}) puis Gratuit`
      : (isPremium ? 'Premium' : 'Gratuit');

    // Limites principales (exemple : nombre de services inclus)
    const mainLimits = subscriptionStatus.limits || {};
    const mainFeatures = subscriptionStatus.features || {};

    return (
      <AnimatePresence mode="wait">
        <motion.div
          key="current-subscription"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          exit={{ opacity: 0, y: -20 }}
          transition={{ duration: 0.5, type: "spring", stiffness: 100 }}
        >
          {/* Bloc visuel principal existant */}
          <Paper
            elevation={0}
            sx={{
              p: { xs: 2, md: 3 },
              mb: 4,
              borderRadius: 3,
              bgcolor: isPremium || isTransitioning ? 'white' : 'white',
              border: '1px solid',
              borderColor: isPremium || isTransitioning ? alpha('#FF7A35', 0.3) : '#e0e0e0',
              position: 'relative',
              overflow: 'hidden',
              '&::before': (isPremium || isTransitioning) ? {
                content: '""',
                position: 'absolute',
                top: 0,
                right: 0,
                width: { xs: 100, md: 150 },
                height: { xs: 100, md: 150 },
                background: `radial-gradient(circle at top right, ${alpha('#FF7A35', 0.15)}, transparent 70%)`,
                borderRadius: '0 0 0 100%',
                zIndex: 0
              } : {}
            }}
          >
            <Grid container spacing={3}>
              {/* En-tête avec informations du plan */}
              <Grid size={{ xs: 12, md: 7 }}>
                <Stack
                  direction={isMobile ? "column" : "row"}
                  alignItems={isMobile ? "flex-start" : "center"}
                  spacing={isMobile ? 1.5 : 2.5}
                  sx={{ position: 'relative', zIndex: 1 }}
                >
                  <Avatar
                    sx={{
                      bgcolor: (isPremium || isTransitioning) ? '#FF7A35' : alpha('#f5f5f5', 0.7),
                      color: (isPremium || isTransitioning) ? 'white' : '#757575',
                      width: 64,
                      height: 64,
                      boxShadow: (isPremium || isTransitioning) ? '0 4px 12px rgba(255, 122, 53, 0.2)' : '0 4px 8px rgba(0, 0, 0, 0.05)',
                      border: '4px solid',
                      borderColor: (isPremium || isTransitioning) ? alpha('#FF7A35', 0.2) : alpha('#f5f5f5', 0.5),
                      mb: isMobile ? 1 : 0
                    }}
                  >
                    {(isPremium || isTransitioning) ? <DiamondIcon sx={{ fontSize: 32 }} /> : <StarIcon sx={{ fontSize: 28, color: '#9e9e9e' }} />}
                  </Avatar>
                  <Stack spacing={0.5} sx={{ flexGrow: 1 }}>
                    <Typography
                      variant="h4"
                      fontWeight="bold"
                      sx={{
                        display: 'flex',
                        alignItems: isMobile ? 'flex-start' : 'center',
                        flexDirection: isMobile ? 'column' : 'row',
                        gap: isMobile ? 1 : 0,
                        color: (isPremium || isTransitioning) ? '#FF7A35' : 'text.primary'
                      }}
                    >
                      {planDisplayName}
                      {(isPremium || isTransitioning) && (
                        <Box component="span" ml={isMobile ? 0 : 2} sx={{ display: 'inline-flex' }}>
                          <Chip
                            size="small"
                            label={isTransitioning ? "En transition" : "Actif"}
                            sx={{
                              fontWeight: 'bold',
                              borderRadius: '12px',
                              height: '24px',
                              fontSize: '0.7rem',
                              background: alpha('#FF7A35', 0.1),
                              border: '1px solid',
                              borderColor: alpha('#FF7A35', 0.3)
                            }}
                          />
                        </Box>
                      )}
                    </Typography>
                    <Typography variant="body1" color="text.secondary" sx={{ maxWidth: isMobile ? '100%' : 500 }}>
                      {isPremium && subscriptionStatus.autoRenew
                        ? "Votre abonnement est actif (renouvellement automatique)"
                        : isPremium && !subscriptionStatus.autoRenew
                          ? `Votre abonnement est actif jusqu'au ${subscriptionStatus.endDate ? formatDateToFrench(subscriptionStatus.endDate) : 'fin de la période'}`
                          : isTransitioning
                            ? "Vous conservez les fonctionnalités Premium jusqu'à la fin de votre période d'abonnement"
                            : "Forfait de base avec les fonctionnalités essentielles"}
                    </Typography>
                  </Stack>
                </Stack>
              </Grid>

              {/* Bouton pour passer à l'onglet plans pour les utilisateurs en plan gratuit */}
              {!isPremium && (
                <Grid size={{ xs: 12, md: 5 }} sx={{ display: 'flex', alignItems: 'center', justifyContent: {xs: 'flex-start', md: 'flex-end'} }}>
                  <motion.div whileHover={{ scale: 1.03 }} whileTap={{ scale: 0.98 }}>
                    <Button
                      variant="contained"
                      onClick={() => handleTabChange('pricing')}
                      startIcon={<DiamondIcon />}
                      endIcon={<ArrowForwardIcon />}
                      sx={{
                        bgcolor: '#FF7A35',
                        '&:hover': { bgcolor: '#E16B28' },
                        px: 3,
                        py: 1.5,
                        borderRadius: '30px',
                        fontWeight: 'bold',
                        boxShadow: '0 6px 15px rgba(255, 122, 53, 0.2)',
                        fontSize: '1rem'
                      }}
                    >
                      Découvrir nos plans Premium
                    </Button>
                  </motion.div>
                </Grid>
              )}

              {/* Informations détaillées de l'abonnement */}
              <Grid size={12}>
                <Divider sx={{ my: 2 }} />
                <Grid container spacing={3} sx={{ position: 'relative', zIndex: 1, mt: 1 }}>
                  <Grid size={{ xs: 12, sm: 6, md: 3 }}>
                    <motion.div whileHover={{ y: -5 }} transition={{ type: "spring", stiffness: 300, damping: 10 }}>
                      <Paper
                        elevation={0}
                        sx={{
                          p: 2.5,
                          borderRadius: 3,
                          bgcolor: 'background.paper',
                          boxShadow: '0 4px 10px rgba(0, 0, 0, 0.04)',
                          height: '100%',
                          border: '1px solid',
                          borderColor: alpha('#FF7A35', 0.15),
                          display: 'flex',
                          flexDirection: 'column',
                          transition: 'all 0.3s ease',
                          '&:hover': {
                            boxShadow: '0 8px 16px rgba(0, 0, 0, 0.08)',
                            borderColor: alpha('#FF7A35', 0.3),
                            bgcolor: alpha('#FF7A35', 0.02)
                          }
                        }}
                      >
                        <Stack direction="row" alignItems="center" spacing={1.5} sx={{ mb: 1.5 }}>
                          <Box
                            sx={{
                              p: 1,
                              borderRadius: '50%',
                              bgcolor: alpha('#FF7A35', 0.1)
                            }}
                          >
                            <StarIcon sx={{ fontSize: 18, color: '#FF7A35' }} />
                          </Box>
                          <Typography variant="body2" color="text.secondary">
                            Plan
                          </Typography>
                        </Stack>
                        <Typography variant="h6" fontWeight="bold" sx={{ color: isPremium ? '#FF7A35' : 'text.primary' }}>
                          {subscriptionStatus.plan.charAt(0).toUpperCase() + subscriptionStatus.plan.slice(1)}
                        </Typography>
                      </Paper>
                    </motion.div>
                  </Grid>
                  <Grid size={{ xs: 12, sm: 6, md: 3 }}>
                    <motion.div whileHover={{ y: -5 }} transition={{ type: "spring", stiffness: 300, damping: 10 }}>
                      <Paper
                        elevation={0}
                        sx={{
                          p: 2.5,
                          borderRadius: 3,
                          bgcolor: 'background.paper',
                          boxShadow: '0 4px 10px rgba(0, 0, 0, 0.04)',
                          height: '100%',
                          border: '1px solid',
                          borderColor: alpha('#FF7A35', 0.15),
                          display: 'flex',
                          flexDirection: 'column',
                          transition: 'all 0.3s ease',
                          '&:hover': {
                            boxShadow: '0 8px 16px rgba(0, 0, 0, 0.08)',
                            borderColor: alpha('#FF7A35', 0.3),
                            bgcolor: alpha('#FF7A35', 0.02)
                          }
                        }}
                      >
                        <Stack direction="row" alignItems="center" spacing={1.5} sx={{ mb: 1.5 }}>
                          <Box
                            sx={{
                              p: 1,
                              borderRadius: '50%',
                              bgcolor: alpha('#FF7A35', 0.1)
                            }}
                          >
                            <CreditCardIcon sx={{ fontSize: 18, color: '#FF7A35' }} />
                          </Box>
                          <Typography variant="body2" color="text.secondary">
                            Prix
                          </Typography>
                        </Stack>
                        <Typography variant="h6" fontWeight="bold">
                          {subscriptionStatus.price}€ <Typography component="span" variant="body2" color="text.secondary">/mois</Typography>
                        </Typography>
                      </Paper>
                    </motion.div>
                  </Grid>
                  <Grid size={{ xs: 12, sm: 6, md: 3 }}>
                    <motion.div whileHover={{ y: -5 }} transition={{ type: "spring", stiffness: 300, damping: 10 }}>
                      <Paper
                        elevation={0}
                        sx={{
                          p: 2.5,
                          borderRadius: 3,
                          bgcolor: 'background.paper',
                          boxShadow: '0 4px 10px rgba(0, 0, 0, 0.04)',
                          height: '100%',
                          border: '1px solid',
                          borderColor: alpha('#FF7A35', 0.15),
                          display: 'flex',
                          flexDirection: 'column',
                          transition: 'all 0.3s ease',
                          '&:hover': {
                            boxShadow: '0 8px 16px rgba(0, 0, 0, 0.08)',
                            borderColor: alpha('#FF7A35', 0.3),
                            bgcolor: alpha('#FF7A35', 0.02)
                          }
                        }}
                      >
                        <Stack direction="row" alignItems="center" spacing={1.5} sx={{ mb: 1.5 }}>
                          <Box
                            sx={{
                              p: 1,
                              borderRadius: '50%',
                              bgcolor: alpha('#FF7A35', 0.1)
                            }}
                          >
                            <HistoryIcon sx={{ fontSize: 18, color: '#FF7A35' }} />
                          </Box>
                          <Typography variant="body2" color="text.secondary">
                            Date de début
                          </Typography>
                        </Stack>
                        <Typography variant="h6" fontWeight="bold">
                          {formatDateToFrench(subscriptionStatus.startDate)}
                        </Typography>
                      </Paper>
                    </motion.div>
                  </Grid>
                  <Grid size={{ xs: 12, sm: 6, md: 3 }}>
                    <motion.div whileHover={{ y: -5 }} transition={{ type: "spring", stiffness: 300, damping: 10 }}>
                      <Paper
                        elevation={0}
                        sx={{
                          p: 2.5,
                          borderRadius: 3,
                          bgcolor: 'background.paper',
                          boxShadow: '0 4px 10px rgba(0, 0, 0, 0.04)',
                          height: '100%',
                          border: '1px solid',
                          borderColor: alpha('#FF7A35', 0.15),
                          display: 'flex',
                          flexDirection: 'column',
                          transition: 'all 0.3s ease',
                          '&:hover': {
                            boxShadow: '0 8px 16px rgba(0, 0, 0, 0.08)',
                            borderColor: alpha('#FF7A35', 0.3),
                            bgcolor: alpha('#FF7A35', 0.02)
                          }
                        }}
                      >
                        <Stack direction="row" alignItems="center" spacing={1.5} sx={{ mb: 1.5 }}>
                          <Box
                            sx={{
                              p: 1,
                              borderRadius: '50%',
                              bgcolor: alpha('#FF7A35', 0.1)
                            }}
                          >
                            <SettingsIcon sx={{ fontSize: 18, color: '#FF7A35' }} />
                          </Box>
                          <Typography variant="body2" color="text.secondary">
                            Renouvellement
                          </Typography>
                        </Stack>
                        <Typography variant="h6" fontWeight="bold">
                          {subscriptionStatus.autoRenew ? 'Automatique' : 'Manuel'}
                        </Typography>
                      </Paper>
                    </motion.div>
                  </Grid>
                </Grid>
              </Grid>
            </Grid>

            <Stack direction={isMobile ? "column" : "row"} spacing={2} sx={{ mt: 3 }}>
                {isPremium ? (
                  <>
                    <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.97 }}>
                      <Button
                        variant="contained"
                        onClick={handleManageSubscription}
                        fullWidth={isMobile}
                        startIcon={<CreditCardIcon />}
                        aria-label="Portail abonnement"
                        sx={{
                          bgcolor: '#FF7A35',
                          '&:hover': { bgcolor: '#E16B28' },
                          px: 2.5,
                          py: 1,
                          borderRadius: '14px',
                          fontWeight: 'bold',
                          fontSize: '0.85rem',
                          boxShadow: '0 8px 24px rgba(255, 122, 53, 0.22)',
                          outline: 'none',
                          border: '2px solid transparent',
                          transition: 'all 0.2s',
                          '&:focus-visible': {
                            border: '2px solid #212121',
                            boxShadow: '0 0 0 4px ' + alpha('#FF7A35', 0.25),
                          },
                          mb: isMobile ? 1.5 : 0
                        }}
                      >
                        Gérer mode paiement
                      </Button>
                    </motion.div>
                    <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.97 }}>
                      <Button
                        variant="outlined"
                        onClick={handleManageSubscription}
                        fullWidth={isMobile}
                        startIcon={<ReceiptIcon />}
                        aria-label="Voir factures"
                        sx={{
                          borderColor: '#FF7A35',
                          color: '#FF7A35',
                          '&:hover': { 
                            bgcolor: alpha('#FF7A35', 0.1),
                            borderColor: '#E16B28',
                            color: '#E16B28'
                          },
                          px: 2.5,
                          py: 1,
                          borderRadius: '14px',
                          fontWeight: 'bold',
                          fontSize: '0.85rem',
                          outline: 'none',
                          border: '2px solid #FF7A35',
                          transition: 'all 0.2s',
                          '&:focus-visible': {
                            border: '2px solid #212121',
                            boxShadow: '0 0 0 4px ' + alpha('#FF7A35', 0.25),
                          },
                          mb: isMobile ? 1.5 : 0
                        }}
                      >
                        Voir factures
                      </Button>
                    </motion.div>
                  </>
                ) : (
                  <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.97 }}>
                    <Button
                      variant="contained"
                      onClick={handleManageSubscription}
                      fullWidth={isMobile}
                      startIcon={<CreditCardIcon />}
                      aria-label="Gérer factures"
                      sx={{
                        bgcolor: '#FF7A35',
                        '&:hover': { bgcolor: '#E16B28' },
                        px: 2.5,
                        py: 1,
                        borderRadius: '14px',
                        fontWeight: 'bold',
                        fontSize: '0.85rem',
                        boxShadow: '0 8px 24px rgba(255, 122, 53, 0.22)',
                        outline: 'none',
                        border: '2px solid transparent',
                        transition: 'all 0.2s',
                        '&:focus-visible': {
                          border: '2px solid #212121',
                          boxShadow: '0 0 0 4px ' + alpha('#FF7A35', 0.25),
                        },
                        mb: isMobile ? 1.5 : 0
                      }}
                    >
                      Gérer mes factures et moyens de paiement
                    </Button>
                  </motion.div>
                )}
                {isPremium && <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.97 }}>
                  <Button
                    variant="outlined"
                    fullWidth={isMobile}
                    onClick={() => handleTabChange('customize')}
                    startIcon={<SettingsIcon />}
                    aria-label="Gérer mes options"
                    sx={{
                      borderColor: '#FF7A35',
                      color: '#FF7A35',
                      px: 2.5,
                      py: 1,
                      borderRadius: '14px',
                      fontWeight: 'bold',
                      fontSize: '0.85rem',
                      background: 'white',
                      outline: 'none',
                      borderWidth: 2,
                      boxShadow: '0 4px 16px rgba(255, 122, 53, 0.08)',
                      transition: 'all 0.2s',
                      '&:hover': {
                        backgroundColor: alpha('#FF7A35', 0.08),
                        borderColor: '#FF7A35',
                        color: '#E16B28',
                      },
                      '&:focus-visible': {
                        border: '2px solid #212121',
                        boxShadow: '0 0 0 4px ' + alpha('#FF7A35', 0.18),
                      },
                    }}
                  >
                    Gérer mes options
                  </Button>
                </motion.div>}
                {/* Bouton de réactivation du renouvellement automatique */}
                {isTransitioning && (
                  <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.97 }}>
                    <Button
                      variant="contained"
                      color="success"
                      fullWidth={isMobile}
                      onClick={handleReactivateAutoRenew}
                      startIcon={<SettingsIcon />}
                      aria-label="Réactiver le renouvellement automatique"
                      sx={{
                        bgcolor: '#43a047',
                        '&:hover': { bgcolor: '#388e3c' },
                        px: 2.5,
                        py: 1,
                        borderRadius: '14px',
                        fontWeight: 'bold',
                        fontSize: '0.85rem',
                        boxShadow: '0 8px 24px rgba(67, 160, 71, 0.18)',
                        outline: 'none',
                        border: '2px solid transparent',
                        transition: 'all 0.2s',
                        '&:focus-visible': {
                          border: '2px solid #212121',
                          boxShadow: '0 0 0 4px #43a04744',
                        },
                        mb: isMobile ? 1.5 : 0
                      }}
                      disabled={loading}
                    >
                      Réactiver le renouvellement automatique
                    </Button>
                  </motion.div>
                )}
              </Stack>
          </Paper>

          {/* Bloc résumé détaillé harmonisé */}
          <Paper elevation={0} sx={{ p: { xs: 2, md: 3 }, borderRadius: 3, bgcolor: isPremium ? 'white' : 'white', border: '1px solid', borderColor: isPremium ? alpha('#FF7A35', 0.3) : '#e0e0e0', mb: 2 }}>
            <Typography variant="h6" fontWeight="bold" color={isPremium ? '#FF7A35' : 'text.primary'} sx={{ mb: 2 }}>
              Résumé de mon abonnement
            </Typography>

            {/* Grid principale avec effet d'animation cascadée */}
            <Grid container spacing={3}>
              {/* Grille d'informations de base */}
              <Grid size={{ xs: 12, md: 6 }}>
                <Paper
                  elevation={0}
                  sx={{
                    p: 2.5,
                    borderRadius: 3,
                    bgcolor: 'white',
                    boxShadow: '0 6px 18px rgba(0, 0, 0, 0.06)',
                    height: '100%',
                    border: '1px solid',
                    borderColor: alpha('#FF7A35', 0.15),
                    overflow: 'hidden',
                    position: 'relative'
                  }}
                >
                  <Typography variant="subtitle1" fontWeight="bold" sx={{ mb: 2, color: '#FF7A35' }}>
                    Détails de l'abonnement
                  </Typography>

                  <Grid container spacing={2}>
                    <Grid size={{ xs: 12, sm: 6 }}>
                      <Stack spacing={1} sx={{ mb: 2 }}>
                        <Stack direction="row" alignItems="center" spacing={1}>
                          <Box sx={{ p: 0.8, bgcolor: alpha('#FF7A35', 0.1), borderRadius: '50%' }}>
                            <StarIcon sx={{ fontSize: 16, color: '#FF7A35' }} />
                          </Box>
                          <Typography variant="body2" color="text.secondary">Nom du plan</Typography>
                        </Stack>
                        <Typography variant="h6" fontWeight="bold" color={isPremium ? '#FF7A35' : 'text.primary'} sx={{ ml: 4 }}>
                          {subscriptionStatus.plan.charAt(0).toUpperCase() + subscriptionStatus.plan.slice(1)}
                        </Typography>
                      </Stack>
                    </Grid>

                    <Grid size={{ xs: 12, sm: 6 }}>
                      <Stack spacing={1} sx={{ mb: 2 }}>
                        <Stack direction="row" alignItems="center" spacing={1}>
                          <Box sx={{ p: 0.8, bgcolor: alpha('#FF7A35', 0.1), borderRadius: '50%' }}>
                            <CreditCardIcon sx={{ fontSize: 16, color: '#FF7A35' }} />
                          </Box>
                          <Typography variant="body2" color="text.secondary">Prix mensuel</Typography>
                        </Stack>
                        <Typography variant="h6" fontWeight="bold" sx={{ ml: 4 }}>
                          {subscriptionStatus.price} € <Typography component="span" variant="body2" color="text.secondary">/mois</Typography>
                        </Typography>
                      </Stack>
                    </Grid>

                    <Grid size={{ xs: 12, sm: 6 }}>
                      <Stack spacing={1} sx={{ mb: 2 }}>
                        <Stack direction="row" alignItems="center" spacing={1}>
                          <Box sx={{ p: 0.8, bgcolor: alpha('#FF7A35', 0.1), borderRadius: '50%' }}>
                            <HistoryIcon sx={{ fontSize: 16, color: '#FF7A35' }} />
                          </Box>
                          <Typography variant="body2" color="text.secondary">Début d'abonnement</Typography>
                        </Stack>
                        <Typography variant="h6" fontWeight="bold" sx={{ ml: 4 }}>
                          {formatDateToFrench(subscriptionStatus.startDate)}
                        </Typography>
                      </Stack>
                    </Grid>

                    <Grid size={{ xs: 12, sm: 6 }}>
                      <Stack spacing={1} sx={{ mb: 2 }}>
                        <Stack direction="row" alignItems="center" spacing={1}>
                          <Box sx={{ p: 0.8, bgcolor: alpha('#FF7A35', 0.1), borderRadius: '50%' }}>
                            <SettingsIcon sx={{ fontSize: 16, color: '#FF7A35' }} />
                          </Box>
                          <Typography variant="body2" color="text.secondary">Renouvellement</Typography>
                        </Stack>
                        <Typography variant="h6" fontWeight="bold" sx={{ ml: 4 }}>
                          {subscriptionStatus.autoRenew ? 'Automatique' : 'Manuel'}
                        </Typography>
                      </Stack>
                    </Grid>

                    <Grid size={{ xs: 12, sm: 6 }}>
                      <Stack spacing={1}>
                        <Stack direction="row" alignItems="center" spacing={1}>
                          <Box sx={{ p: 0.8, bgcolor: alpha('#FF7A35', 0.1), borderRadius: '50%' }}>
                            <CalendarMonthIcon sx={{ fontSize: 16, color: '#FF7A35' }} />
                          </Box>
                          <Typography variant="body2" color="text.secondary">Fin d'abonnement</Typography>
                        </Stack>
                        <Typography variant="h6" fontWeight="bold" sx={{ ml: 4 }}>
                          {endDateLabel}
                        </Typography>
                      </Stack>
                    </Grid>
                  </Grid>

                  {/* Effet visuel */}
                  <Box sx={{
                    position: 'absolute',
                    top: -30,
                    right: -30,
                    width: 150,
                    height: 150,
                    borderRadius: '50%',
                    background: alpha('#FF7A35', 0.05),
                    zIndex: 0
                  }} />
                </Paper>
              </Grid>

              {/* Fonctionnalités incluses */}
              {Object.keys(mainFeatures).length > 0 && (
                <Grid size={{ xs: 12, md: 6 }}>
                  <Paper
                    elevation={0}
                    sx={{
                      p: 2.5,
                      borderRadius: 3,
                      bgcolor: 'white',
                      boxShadow: '0 6px 18px rgba(0, 0, 0, 0.06)',
                      height: '100%',
                      border: '1px solid',
                      borderColor: alpha('#FF7A35', 0.15),
                      overflow: 'hidden',
                      position: 'relative'
                    }}
                  >
                    <Typography variant="subtitle1" fontWeight="bold" sx={{ mb: 2, color: '#FF7A35' }}>
                      Fonctionnalités incluses
                    </Typography>

                    <Grid container spacing={2}>
                      {Object.entries(mainFeatures).map(([key, enabled]) => {
                        // Mapper les clés techniques à des noms et descriptions utilisateur
                        let label = '';
                        let description = '';
                        let icon = null;

                        switch(key) {
                          case 'advancedStats':
                            label = 'Statistiques avancées';
                            description = 'Accès aux statistiques détaillées de votre activité';
                            icon = <AssessmentIcon fontSize="small" />;
                            break;
                          case 'unlimitedPhotos':
                            label = 'Photos +';
                            description = 'Téléchargez encore plus de photos sur votre profil';
                            icon = <PhotoLibraryIcon fontSize="small" />;
                            break;
                          case 'prioritySupport':
                            label = 'Support prioritaire';
                            description = 'Assistance technique prioritaire par tickets';
                            icon = <SupportAgentIcon fontSize="small" />;
                            break;
                          case 'featuredListing':
                            label = 'Profil mis en avant';
                            description = 'Afficher le badge premium sur votre profil';
                            icon = <TrendingUpIcon fontSize="small" />;
                            break;
                          case 'Visibilité du numéro de téléphone':
                            label = 'Visibilité du numéro de téléphone';
                            description = 'Vous ne pouvez pas voir le numéro de téléphone des autres utilisateurs directement sur leur profil.';
                            icon = <CancelIcon sx={{ fontSize: 24, color: '#FF7A35' }} />;
                            break;
                          default:
                            label = key.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase());
                            description = 'Fonctionnalité premium';
                            icon = <CheckCircleIcon fontSize="small" />;
                        }

                        return (
                          <Grid size={{ xs: 12, sm: 6 }} key={key}>
                            <Paper
                              elevation={0}
                              sx={{
                                p: 2,
                                borderRadius: 2,
                                bgcolor: alpha(enabled ? '#FF7A35' : '#f5f5f5', enabled ? 0.05 : 0.6),
                                border: '1px solid',
                                borderColor: enabled ? alpha('#FF7A35', 0.2) : alpha('#e0e0e0', 0.9),
                                display: 'flex',
                                flexDirection: 'column',
                                alignItems: 'flex-start',
                                minHeight: 120,
                                height: '100%',
                                position: 'relative'
                              }}
                            >
                              <Stack direction="row" alignItems="center" spacing={1}>
                                <Box sx={{
                                  p: 0.8,
                                  bgcolor: enabled ? alpha('#FF7A35', 0.1) : alpha('#9e9e9e', 0.08),
                                  borderRadius: '50%',
                                  color: enabled ? '#FF7A35' : '#9e9e9e',
                                  display: 'flex',
                                  alignItems: 'center',
                                  justifyContent: 'center'
                                }}>
                                  {icon}
                                </Box>
                                <Typography variant="body2" fontWeight="medium" color={enabled ? 'text.primary' : 'text.secondary'}>
                                  {label}
                                </Typography>
                              </Stack>
                              <Typography variant="caption" color="text.secondary" sx={{ mt: 1, mb: 1, lineHeight: 1.5 }}>
                                {description}
                              </Typography>
                              <Box sx={{ width: '100%', display: 'flex', justifyContent: { xs: 'center', sm: 'flex-end' } }}>
                                {enabled ? (
                                  <CheckCircleIcon sx={{ fontSize: 18, color: '#4caf50' }} />
                                ) : (
                                  <Chip
                                    label={subscriptionStatus.plan === 'gratuit' ? 'Premium uniquement' : 'Non inclus'}
                                    size="small"
                                    sx={{
                                      bgcolor: alpha('#9e9e9e', 0.12),
                                      color: '#9e9e9e',
                                      fontWeight: 'bold',
                                      px: 1.5,
                                      borderRadius: 1,
                                      mt: 1
                                    }}
                                  />
                                )}
                              </Box>
                            </Paper>
                          </Grid>
                        );
                      })}
                    </Grid>

                    {/* Effet visuel */}
                    <Box sx={{
                      position: 'absolute',
                      bottom: -30,
                      left: -30,
                      width: 150,
                      height: 150,
                      borderRadius: '50%',
                      background: alpha('#FF7A35', 0.05),
                      zIndex: 0
                    }} />
                  </Paper>
                </Grid>
              )}
            </Grid>

            {/* Section Limites principales reste inchangée */}
            {Object.keys(mainLimits).length > 0 && (
              <Box sx={{ mt: 3 }}>
                <Typography
                  variant="h6"
                  fontWeight="bold"
                  color={isPremium ? '#FF7A35' : 'text.primary'}
                  sx={{
                    mb: 2,
                    position: 'relative',
                    display: 'inline-block',
                    '&:after': {
                      content: '""',
                      position: 'absolute',
                      width: '40%',
                      height: '3px',
                      bottom: -5,
                      left: 0,
                      backgroundColor: '#FF7A35',
                      borderRadius: 2
                    }
                  }}
                >
                  Limites principales actuelles
                </Typography>
                <Grid container spacing={2}>
                  {Object.entries(mainLimits).map(([key, value]) => {
                    // Mapping label utilisateur + description backend
                    let label = '';
                    let description = '';
                    let icon = null;
                    let planData: Record<string, any> = {};

                    if (subscriptionStatus.plan === 'gratuit' || subscriptionStatus.plan === 'premium') {
                      planData = subscriptionConfig?.data?.[subscriptionStatus.plan] as Record<string, any>;
                    }

                    switch (key) {
                      case 'services':
                        label = 'Sous-catégories de services';
                        description = planData.services?.description || 'Nombre de sous-catégories de services que vous pouvez proposer gratuitement.';
                        icon = <CategoryIcon sx={{ fontSize: 24, color: '#FF7A35' }} />;
                        break;
                      case 'galleries':
                        label = 'Galeries / Portfolio';
                        description = planData.galleries?.description || 'Nombre de galeries photos pour présenter vos réalisations.';
                        icon = <StarIcon sx={{ fontSize: 24, color: '#FF7A35' }} />;
                        break;
                      case 'interventionAreas':
                        label = "Zones d'intervention";
                        description = planData.interventionAreas?.description || 'Kilomètres de votre zone géographique d\'intervention où vous pouvez proposer vos services.';
                        icon = <AdjustIcon sx={{ fontSize: 24, color: '#FF7A35' }} />;
                        break;
                      case 'conversations_messages_prives':
                        label = 'Conversations privées';
                        description = planData.conversations_messages_prives?.description || 'Nombre de conversations privées avec des clients.';
                        icon = <AddIcon sx={{ fontSize: 24, color: '#FF7A35' }} />;
                        break;
                      case 'favoriteLimit':
                        label = 'Favoris maximum';
                        description = planData.favoriteLimit?.description || 'Nombre maximum de profils favoris enregistrables.';
                        icon = <Favorite sx={{ fontSize: 24, color: '#FF7A35' }} />;
                        break;
                      case 'history_logs':
                        label = 'Historique d\'activité';
                        description = planData.history_logs?.description || 'Historique des activités consultables. Limité pour l\'offre gratuite.';
                        icon = <HistoryIcon sx={{ fontSize: 24, color: '#FF7A35' }} />;
                        break;
                      case 'transactions':
                        label = 'Transactions Jobi';
                        description = planData.transactions?.description || 'Nombre historique de transactions Jobi incluses.';
                        icon = <BoltIcon sx={{ fontSize: 24, color: '#FF7A35' }} />;
                        break;
                      case 'invoices':
                        label = 'Factures';
                        description = planData.invoices?.description || 'Nombre de factures générables gratuitement. Facturation supplémentaire au-delà.';
                        icon = <CreditCardIcon sx={{ fontSize: 24, color: '#FF7A35' }} />;
                        break;
                      case 'quotes':
                        label = 'Devis';
                        description = planData.quotes?.description || 'Nombre de devis générables gratuitement. Devis supplémentaire payant.';
                        icon = <DiamondIcon sx={{ fontSize: 24, color: '#FF7A35' }} />;
                        break;
                      case 'missionResponses':
                        label = 'Réponses aux missions';
                        description = planData.missionResponses?.description || 'Nombre de candidatures/offres que vous pouvez envoyer par mois.';
                        icon = <BoltIcon sx={{ fontSize: 24, color: '#FF7A35' }} />;
                        break;
                      case 'aiCredits':
                        label = 'Crédits IA mensuels';
                        description = planData.aiCredits?.description || 'Crédits IA mensuels pour générer du contenu (mission, biographie, etc).';
                        icon = <SmartToyIcon sx={{ fontSize: 24, color: '#FF7A35' }} />;
                        break;
                      case 'businessCards':
                        label = 'Cartes de visite';
                        description = planData.businessCards?.description || 'Nombre de cartes de visite que vous pouvez créer et utiliser.';
                        icon = <BusinessCenterIcon sx={{ fontSize: 24, color: '#FF7A35' }} />;
                        break;
                      case 'flyers':
                        label = 'Flyers';
                        description = planData.flyers?.description || 'Nombre de flyers que vous pouvez créer et utiliser.';
                        icon = <DescriptionIcon sx={{ fontSize: 24, color: '#FF7A35' }} />;
                        break;
                      default:
                        label = key;
                        description = '';
                        icon = <InfoIcon sx={{ fontSize: 24, color: '#FF7A35' }} />;
                    }

                    // Après la card Favoris maximum, on insère la card visibilité numéros
                    const isAfterFavorite = key === 'favoriteLimit';
                    const cards = [];
                    cards.push(
                      <Grid size={{ xs: 12, sm: 6, md: 6, lg: 4 }} key={key}>
                        <motion.div whileHover={{ y: -5 }} transition={{ type: "spring", stiffness: 300, damping: 10 }}>
                          <Paper
                            elevation={0}
                            sx={{
                              p: 2.5,
                              borderRadius: 3,
                              bgcolor: 'background.paper',
                              boxShadow: '0 4px 10px rgba(0, 0, 0, 0.04)',
                              height: '100%',
                              minHeight: 200,
                              border: '1px solid',
                              borderColor: alpha('#FF7A35', 0.15),
                              display: 'flex',
                              flexDirection: 'column',
                              transition: 'all 0.3s ease',
                              '&:hover': {
                                boxShadow: '0 8px 16px rgba(0, 0, 0, 0.08)',
                                borderColor: alpha('#FF7A35', 0.3),
                                bgcolor: alpha('#FF7A35', 0.02)
                              }
                            }}
                          >
                            <Stack direction="row" alignItems="center" spacing={1.5} sx={{ mb: 1.5 }}>
                              <Box
                                sx={{
                                  p: 1,
                                  borderRadius: '50%',
                                  bgcolor: alpha('#FF7A35', 0.1)
                                }}
                              >
                                {icon}
                              </Box>
                              <Typography variant="subtitle1" fontWeight="bold" color="text.primary" noWrap>
                                {label}
                              </Typography>
                            </Stack>
                            <Box sx={{ mt: 0.5, mb: 2, display: 'flex', alignItems: 'center' }}>
                              <Typography variant="h4" fontWeight="bold" color="#FF7A35" sx={{ mr: 1 }}>
                                {value}
                              </Typography>
                            </Box>
                            <Typography variant="body2" color="text.secondary" sx={{
                              mt: 'auto',
                              display: '-webkit-box',
                              WebkitLineClamp: 3,
                              WebkitBoxOrient: 'vertical',
                              overflow: 'hidden',
                              lineHeight: 1.5
                            }}>
                              {description}
                            </Typography>
                          </Paper>
                        </motion.div>
                      </Grid>
                    );
                    if (isAfterFavorite) {
                      // Ajout de la card visibilité numéros
                      const isPremium = subscriptionStatus.plan === 'premium';
                      cards.push(
                        <Grid size={{ xs: 12, sm: 6, md: 6, lg: 4 }} key="phoneVisibility">
                          <motion.div whileHover={{ y: -5 }} transition={{ type: "spring", stiffness: 300, damping: 10 }}>
                            <Paper
                              elevation={0}
                              sx={{
                                p: 2.5,
                                borderRadius: 3,
                                bgcolor: 'background.paper',
                                boxShadow: '0 4px 10px rgba(0, 0, 0, 0.04)',
                                height: '100%',
                                minHeight: 200,
                                border: '1px solid',
                                borderColor: alpha('#FF7A35', 0.15),
                                display: 'flex',
                                flexDirection: 'column',
                                transition: 'all 0.3s ease',
                                '&:hover': {
                                  boxShadow: '0 8px 16px rgba(0, 0, 0, 0.08)',
                                  borderColor: alpha('#FF7A35', 0.3),
                                  bgcolor: alpha('#FF7A35', 0.02)
                                }
                              }}
                            >
                              <Stack direction="row" alignItems="center" spacing={1.5} sx={{ mb: 1.5 }}>
                                <Box
                                  sx={{
                                    p: 1,
                                    borderRadius: '50%',
                                    bgcolor: alpha('#FF7A35', 0.1)
                                  }}
                                >
                                  <Favorite sx={{ fontSize: 24, color: '#FF7A35' }} />
                                </Box>
                                <Typography variant="subtitle1" fontWeight="bold" color="text.primary" noWrap>
                                  Visibilité des numéros sur les profils
                                </Typography>
                              </Stack>
                              <Box sx={{ mt: 0.5, mb: 2, display: 'flex', alignItems: 'center' }}>
                                <Typography variant="h4" fontWeight="bold" color="#FF7A35" sx={{ mr: 1 }}>
                                  {isPremium ? 'Oui' : 'Non'}
                                </Typography>
                              </Box>
                              <Typography variant="body2" color="text.secondary" sx={{
                                mt: 'auto',
                                display: '-webkit-box',
                                WebkitLineClamp: 3,
                                WebkitBoxOrient: 'vertical',
                                overflow: 'hidden',
                                lineHeight: 1.5
                              }}>
                                {isPremium ? 'Vous pouvez voir le numéro de téléphone des autres utilisateurs sur leur profil public directement.' : 'Vous ne pouvez pas voir le numéro de téléphone des autres utilisateurs sur leur profil public directement.'}
                              </Typography>
                            </Paper>
                          </motion.div>
                        </Grid>
                      );

                      // Ajout de la card crédits IA
                      const aiCreditsValue = isPremium
                        ? (subscriptionConfig?.data?.premium?.aiCredits?.included || 20)
                        : (subscriptionConfig?.data?.gratuit?.aiCredits?.included || 2);
                      const aiCreditsDescription = isPremium
                        ? (subscriptionConfig?.data?.premium?.aiCredits?.description || 'Crédits IA mensuels pour générer du contenu (mission, biographie, etc). Uniquement ajoutés quand vous avez moins de crédits que la limite.')
                        : (subscriptionConfig?.data?.gratuit?.aiCredits?.description || 'Crédits IA mensuels pour générer du contenu (mission, biographie, etc). Uniquement ajoutés quand vous avez moins de crédits que la limite.');

                      cards.push(
                        <Grid size={{ xs: 12, sm: 6, md: 3 }} key="aiCredits">
                          <motion.div whileHover={{ y: -5 }} transition={{ type: "spring", stiffness: 300, damping: 10 }}>
                            <Paper
                              elevation={0}
                              sx={{
                                p: 2.5,
                                borderRadius: 3,
                                bgcolor: 'background.paper',
                                boxShadow: '0 4px 10px rgba(0, 0, 0, 0.04)',
                                height: '100%',
                                minHeight: 200,
                                border: '1px solid',
                                borderColor: alpha('#FF7A35', 0.15),
                                display: 'flex',
                                flexDirection: 'column',
                                transition: 'all 0.3s ease',
                                '&:hover': {
                                  boxShadow: '0 8px 16px rgba(0, 0, 0, 0.08)',
                                  borderColor: alpha('#FF7A35', 0.3),
                                  bgcolor: alpha('#FF7A35', 0.02)
                                }
                              }}
                            >
                              <Stack direction="row" alignItems="center" spacing={1.5} sx={{ mb: 1.5 }}>
                                <Box
                                  sx={{
                                    p: 1,
                                    borderRadius: '50%',
                                    bgcolor: alpha('#FF7A35', 0.1)
                                  }}
                                >
                                  <SmartToyIcon sx={{ fontSize: 24, color: '#FF7A35' }} />
                                </Box>
                                <Typography variant="subtitle1" fontWeight="bold" color="text.primary" noWrap>
                                  Crédits IA mensuels
                                </Typography>
                              </Stack>
                              <Box sx={{ mt: 0.5, mb: 2, display: 'flex', alignItems: 'center' }}>
                                <Typography variant="h4" fontWeight="bold" color="#FF7A35" sx={{ mr: 1 }}>
                                  {aiCreditsValue}
                                </Typography>
                              </Box>
                              <Typography variant="body2" color="text.secondary" sx={{
                                mt: 'auto',
                                display: '-webkit-box',
                                WebkitLineClamp: 3,
                                WebkitBoxOrient: 'vertical',
                                overflow: 'hidden',
                                lineHeight: 1.5
                              }}>
                                {aiCreditsDescription}
                              </Typography>
                            </Paper>
                          </motion.div>
                        </Grid>
                      );
                    }
                    return cards;
                  }).flat()}
                </Grid>
              </Box>
            )}

            {!isPremium && (
              <Typography variant="caption" color="text.secondary" sx={{ mt: 1, display: 'block' }}>
                Ceci est l'offre gratuite. Passez à Premium pour débloquer toutes les fonctionnalités et lever les limites.
              </Typography>
            )}
          </Paper>
        </motion.div>
      </AnimatePresence>
    );
  };

  // Rendu du composant pour comparer les plans
  const renderPricingPlans = () => {
    if (!subscriptionConfig) return null;

    const planFeatures: PlanFeature[] = [
      {
        name: 'Sous-catégories de services',
        gratuit: subscriptionConfig.data.gratuit.services.included,
        premium: subscriptionConfig.data.premium.services.included,
        description: subscriptionConfig.data.gratuit.services.description || 'Nombre de sous-catégories de services que vous pouvez proposer gratuitement.',
        descriptionPremium: subscriptionConfig.data.premium.services.description || 'Nombre de sous-catégories de services que vous pouvez proposer gratuitement.'
      },
      {
        name: 'Galeries / Portfolio',
        gratuit: subscriptionConfig.data.gratuit.galleries.included,
        premium: subscriptionConfig.data.premium.galleries.included,
        description: subscriptionConfig.data.gratuit.galleries.description || 'Nombre de galeries pour présenter vos réalisations',
        descriptionPremium: subscriptionConfig.data.premium.galleries.description || 'Nombre de galeries pour présenter vos réalisations'
      },
      {
        name: 'Zone d\'intervention',
        gratuit: `${subscriptionConfig.data.gratuit.interventionAreas.included} km maximum`,
        premium: `${subscriptionConfig.data.premium.interventionAreas.included} km`,
        description: subscriptionConfig.data.gratuit.interventionAreas.description || 'Nombre de zones d\'intervention géographiques',
        descriptionPremium: subscriptionConfig.data.premium.interventionAreas.description || 'Nombre de zones d\'intervention géographiques'
      },
      {
        name: 'Conversations privées',
        gratuit: subscriptionConfig.data.gratuit.conversations_messages_prives.included,
        premium: subscriptionConfig.data.premium.conversations_messages_prives.included,
        description: subscriptionConfig.data.gratuit.conversations_messages_prives.description || 'Nombre de discussions simultanées avec des clients',
        descriptionPremium: subscriptionConfig.data.premium.conversations_messages_prives.description || 'Nombre de discussions simultanées avec des clients'
      },
      {
        name: 'Historique d\'activité',
        gratuit: subscriptionConfig.data.gratuit.history_logs.included,
        premium: subscriptionConfig.data.premium.history_logs.included,
        description: subscriptionConfig.data.gratuit.history_logs.description || 'Accès à l\'historique de vos activités',
        descriptionPremium: subscriptionConfig.data.premium.history_logs.description || 'Accès à l\'historique de vos activités'
      },
      {
        name: 'Transactions Jobi',
        gratuit: subscriptionConfig.data.gratuit.transactions.included,
        premium: subscriptionConfig.data.premium.transactions.included,
        description: subscriptionConfig.data.gratuit.transactions.description || 'Nombre de transactions Jobi incluses',
        descriptionPremium: subscriptionConfig.data.premium.transactions.description || 'Nombre de transactions Jobi incluses'
      },
      {
        name: 'Factures',
        gratuit: subscriptionConfig.data.gratuit.invoices.included,
        premium: subscriptionConfig.data.premium.invoices.included,
        description: subscriptionConfig.data.gratuit.invoices.description || 'Nombre de factures générables gratuitement. Facturation supplémentaire au-delà.',
        descriptionPremium: subscriptionConfig.data.premium.invoices.description || 'Nombre de factures générables gratuitement. Facturation supplémentaire au-delà.'
      },
      {
        name: 'Devis',
        gratuit: subscriptionConfig.data.gratuit.quotes.included,
        premium: subscriptionConfig.data.premium.quotes.included,
        description: subscriptionConfig.data.gratuit.quotes.description || 'Nombre de devis générables gratuitement. Devis supplémentaire payant.',
        descriptionPremium: subscriptionConfig.data.premium.quotes.description || 'Nombre de devis générables gratuitement. Devis supplémentaire payant.'
      },
      {
        name: 'Réponses aux missions',
        gratuit: subscriptionConfig.data.gratuit.missionResponses.included,
        premium: subscriptionConfig.data.premium.missionResponses.included,
        description: subscriptionConfig.data.gratuit.missionResponses.description || 'Nombre de candidatures/offres que vous pouvez envoyer par mois.',
        descriptionPremium: subscriptionConfig.data.premium.missionResponses.description || 'Nombre de candidatures/offres que vous pouvez envoyer par mois.'
      },
      {
        name: 'Favoris maximum',
        gratuit: subscriptionConfig.data.gratuit.favoriteLimit?.included || 3,
        premium: subscriptionConfig.data.premium.favoriteLimit?.included || 60,
        description: subscriptionConfig.data.gratuit.favoriteLimit?.description || 'Nombre maximum de profils favoris enregistrables.',
        descriptionPremium: subscriptionConfig.data.premium.favoriteLimit?.description || 'Nombre maximum de profils favoris enregistrables.'
      },
      {
        name: 'Crédits IA mensuels',
        gratuit: subscriptionConfig.data.gratuit.aiCredits?.included || 2,
        premium: subscriptionConfig.data.premium.aiCredits?.included || 20,
        description: subscriptionConfig.data.gratuit.aiCredits?.description || 'Crédits IA mensuels pour générer du contenu (mission, biographie, etc).',
        descriptionPremium: subscriptionConfig.data.premium.aiCredits?.description || 'Crédits IA mensuels pour générer du contenu (mission, biographie, etc).',
        highlight: true
      },
      {
        name: 'Cartes de visite',
        gratuit: subscriptionConfig.data.gratuit.businessCards?.included || 1,
        premium: subscriptionConfig.data.premium.businessCards?.included || 5,
        description: subscriptionConfig.data.gratuit.businessCards?.description || 'Nombre de cartes de visite que vous pouvez créer et utiliser.',
        descriptionPremium: subscriptionConfig.data.premium.businessCards?.description || 'Nombre de cartes de visite que vous pouvez créer et utiliser.'
      },
      {
        name: 'Flyers',
        gratuit: subscriptionConfig.data.gratuit.flyers?.included || 1,
        premium: subscriptionConfig.data.premium.flyers?.included || 20,
        description: subscriptionConfig.data.gratuit.flyers?.description || 'Nombre de flyers que vous pouvez créer et utiliser.',
        descriptionPremium: subscriptionConfig.data.premium.flyers?.description || 'Nombre de flyers que vous pouvez créer et utiliser.'
      },
      {
        name: 'Jobi bonus mensuel',
        gratuit: false,
        premium: true,
        description: 'Pas de bonus mensuel',
        descriptionPremium: '20 Jobi offerts chaque mois au renouvellement de l\'abonnement',
        highlight: true
      },
      {
        name: 'Visibilité du numéro de téléphone',
        gratuit: false,
        premium: true,
        description: 'Vous ne pouvez pas voir le numéro de téléphone des autres utilisateurs directement sur leur profil.',
        descriptionPremium: 'Vous pouvez voir le numéro de téléphone des autres utilisateurs sur leur profil public directement.'
      },
    ];

    const fadeInUpVariants = {
      hidden: { opacity: 0, y: 20 },
      visible: { opacity: 1, y: 0 }
    };

    return (
      <AnimatePresence mode="wait">
        <motion.div
          key="pricing-plans"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          transition={{ duration: 0.5 }}
        >
          <Box sx={{ mb: 4 }}>
            <Grid container spacing={4}>
              {/* Plan Gratuit */}
              <Grid size={{ xs: 12, md: 6 }}>
                <Box sx={{ height: '100%', display: 'flex', alignItems: 'stretch' }}>
                  <motion.div
                    initial="hidden"
                    animate="visible"
                    variants={fadeInUpVariants}
                    transition={{ delay: 0, duration: 0.5, ease: "easeOut" }}
                    style={{ flex: 1, display: 'flex' }}
                  >
                    <Card sx={{
                      ...cardStyle,
                      transition: 'all 0.4s ease',
                      display: 'flex',
                      flexDirection: 'column',
                      height: '100%',
                      flex: 1
                    }}>
                      <Box sx={{
                        p: 3,
                        bgcolor: alpha('#f8f9fc', 0.8),
                        borderBottom: '1px solid #e0e0e0',
                        position: 'relative'
                      }}>
                        <Stack
                          direction={isMobile ? "column" : "row"}
                          justifyContent={isMobile ? "flex-start" : "space-between"}
                          alignItems={isMobile ? "flex-start" : "center"}
                          spacing={isMobile ? 1.5 : 0}
                        >
                          <Typography variant="h5" fontWeight="bold">Gratuit</Typography>
                          <Chip
                            label="Gratuit"
                            sx={{
                              bgcolor: '#f0f0f0',
                              fontWeight: 'bold',
                              px: 1,
                              borderRadius: '20px',
                              fontSize: '0.9rem',
                              color: '#616161'
                            }}
                          />
                        </Stack>
                        <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
                          Pour démarrer et explorer la plateforme
                        </Typography>
                        <Typography variant="h3" fontWeight="bold" sx={{ mt: 2, color: '#616161' }}>
                          {priceConfig.freePlanBasePrice}€ <Typography component="span" variant="body2" color="text.secondary">/mois</Typography>
                        </Typography>
                      </Box>
                      <CardContent sx={{ p: 0 }}>
                        <Box sx={{ px: 3, py: 2, borderBottom: '1px solid #f0f0f0' }}>
                          <Typography variant="subtitle2" fontWeight="medium" color="text.secondary">
                            Fonctionnalités incluses:
                          </Typography>
                        </Box>
                        <Stack sx={{ px: 0, py: 2 }}>
                          {planFeatures.map((feature, index) => (
                            <Box
                              key={feature.name}
                              sx={{
                                px: 3,
                                py: 1.5,
                                borderBottom: index < planFeatures.length - 1 ? '1px solid #f5f5f5' : 'none',
                                bgcolor: feature.highlight ? alpha('#f5f5f5', 0.5) : 'transparent'
                              }}
                            >
                              <Stack direction="row" alignItems="center" spacing={1.5}>
                                {typeof feature.gratuit === 'boolean' ? (
                                  feature.gratuit ?
                                    <CheckIcon sx={{ color: '#FF7A35', fontSize: 20 }} /> :
                                    <XIcon sx={{ color: 'error.main', fontSize: 20 }} />
                                ) : (
                                  <CheckIcon sx={{ color: '#FF7A35', fontSize: 20 }} />
                                )}
                                <Box>
                                  <Typography variant="body2" fontWeight="medium">
                                    {feature.name}
                                    {typeof feature.gratuit !== 'boolean' && (
                                      <Typography component="span" fontWeight="bold" color="#616161" ml={1}>
                                        {feature.gratuit}
                                      </Typography>
                                    )}
                                  </Typography>
                                  <Typography variant="caption" color="text.secondary" sx={{ display: 'block' }}>
                                    {feature.description || feature.descriptionPremium}
                                  </Typography>
                                </Box>
                              </Stack>
                            </Box>
                          ))}
                        </Stack>
                      </CardContent>
                      <Box sx={{ p: 3, bgcolor: alpha('#f8f9fc', 0.8), borderTop: '1px solid #e0e0e0' }}>
                        <motion.div whileHover={{ scale: 1.03 }} whileTap={{ scale: 0.98 }}>
                          <Button
                            variant="outlined"
                            fullWidth
                            disabled={subscriptionStatus?.plan === 'gratuit'}
                            onClick={handleOpenGratuitConfirmDialog}
                            sx={{
                              py: 1.2,
                              borderRadius: '30px',
                              fontWeight: 'bold',
                              borderWidth: 2,
                              borderColor: '#FF7A35',
                              color: '#FF7A35',
                              '&:hover': {
                                borderColor: '#E16B28',
                                color: '#E16B28',
                                bgcolor: alpha('#FF7A35', 0.05)
                              },
                              '&.Mui-disabled': {
                                bgcolor: '#f0f0f0',
                                color: '#9e9e9e'
                              }
                            }}
                          >
                            {subscriptionStatus?.plan === 'gratuit' ? 'Plan actuel' : 'Choisir le plan Gratuit'}
                          </Button>
                        </motion.div>
                      </Box>
                    </Card>
                  </motion.div>
                </Box>
              </Grid>

              {/* Plan Premium */}
              <Grid size={{ xs: 12, md: 6 }}>
                <Box sx={{ height: '100%', display: 'flex', alignItems: 'stretch', position: 'relative' }}>
                  <motion.div
                    initial={{ opacity: 0, y: -20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 0.5, duration: 0.4 }}
                    style={{
                      position: 'absolute',
                      zIndex: 20,
                      width: '100%',
                      display: 'flex',
                      justifyContent: 'center',
                      top: -15
                    }}
                  >
                    <Box sx={{
                      bgcolor: '#FF7A35',
                      color: 'white',
                      py: 0.8,
                      px: 3.5,
                      fontSize: '1rem',
                      fontWeight: 'bold',
                      letterSpacing: '0.5px',
                      textTransform: 'uppercase',
                      borderRadius: '6px',
                      boxShadow: '0 6px 15px rgba(255, 122, 53, 0.3)',
                      display: 'inline-block',
                      position: 'relative',
                      transform: 'translateY(-10px)',
                      '&::before': {
                        content: '""',
                        position: 'absolute',
                        bottom: -10,
                        left: 10,
                        width: 0,
                        height: 0,
                        borderTop: '10px solid #E16B28',
                        borderLeft: '10px solid transparent',
                      },
                      '&::after': {
                        content: '""',
                        position: 'absolute',
                        bottom: -10,
                        right: 10,
                        width: 0,
                        height: 0,
                        borderTop: '10px solid #E16B28',
                        borderRight: '10px solid transparent',
                      }
                    }}>
                      RECOMMANDÉ
                    </Box>
                  </motion.div>
                  <motion.div
                    initial="hidden"
                    animate="visible"
                    variants={fadeInUpVariants}
                    transition={{ delay: 0.1, duration: 0.5, ease: "easeOut" }}
                    style={{ flex: 1, display: 'flex' }}
                  >
                    <Card sx={{
                      ...cardStyle,
                      border: '2px solid #FF7A35',
                      position: 'relative',
                      transition: 'all 0.4s ease',
                      overflow: 'visible',
                      zIndex: 10,
                      display: 'flex',
                      flexDirection: 'column',
                      height: '100%',
                      flex: 1
                    }}>
                      <Box sx={{
                        p: 3,
                        bgcolor: alpha('#FF7A35', 0.04),
                        borderBottom: '1px solid',
                        borderColor: alpha('#FF7A35', 0.2),
                        position: 'relative',
                        overflow: 'hidden',
                        '&::before': {
                          content: '""',
                          position: 'absolute',
                          top: -40,
                          right: -40,
                          width: 180,
                          height: 180,
                          background: `radial-gradient(circle at top right, ${alpha('#FF7A35', 0.15)}, transparent 70%)`,
                          borderRadius: '0 0 0 100%',
                          zIndex: 0
                        }
                      }}>
                        <Stack
                          direction={isMobile ? "column" : "row"}
                          justifyContent={isMobile ? "flex-start" : "space-between"}
                          alignItems={isMobile ? "flex-start" : "center"}
                          spacing={isMobile ? 1.5 : 0}
                          sx={{ position: 'relative', zIndex: 1 }}
                        >
                          <Typography variant="h5" fontWeight="bold" color="#FF7A35">Premium</Typography>
                          <Chip
                            label={`À partir de ${priceConfig.premiumPlanBasePrice}€/mois`}
                            sx={{
                              bgcolor: '#FF7A35',
                              color: 'white',
                              fontWeight: 'bold',
                              px: 1,
                              borderRadius: '20px',
                              fontSize: '0.9rem'
                            }}
                          />
                        </Stack>
                        <Typography variant="body2" color="text.secondary" sx={{ mt: 1, position: 'relative', zIndex: 1 }}>
                          Idéal pour les professionnels qui veulent développer leur activité
                        </Typography>
                        <Typography variant="h3" fontWeight="bold" sx={{ mt: 2, color: '#FF7A35', position: 'relative', zIndex: 1 }}>
                        <Typography component="span" variant="body2" color="text.secondary">Dès</Typography>{priceConfig.premiumPlanBasePrice}€ <Typography component="span" variant="body2" color="text.secondary">/mois</Typography>
                        </Typography>
                      </Box>
                      <CardContent sx={{ p: 0 }}>
                        <Box sx={{ px: 3, py: 2, borderBottom: '1px solid #f0f0f0' }}>
                          <Typography variant="subtitle2" fontWeight="medium" color="text.secondary">
                            Tout ce qui est inclus dans Gratuit, plus:
                          </Typography>
                        </Box>
                        <Stack sx={{ px: 0, py: 2 }}>
                          {planFeatures.map((feature, index) => (
                            <Box
                              key={feature.name}
                              sx={{
                                px: 3,
                                py: 1.5,
                                borderBottom: index < planFeatures.length - 1 ? '1px solid #f5f5f5' : 'none',
                                bgcolor: feature.highlight ? alpha('#FF7A35', 0.05) : 'transparent'
                              }}
                            >
                              <Stack direction="row" alignItems="center" spacing={1.5}>
                                {typeof feature.premium === 'boolean' ? (
                                  feature.premium ?
                                    <CheckIcon sx={{ color: '#FF7A35', fontSize: 20 }} /> :
                                    <XIcon sx={{ color: 'error.main', fontSize: 20 }} />
                                ) : (
                                  <CheckIcon sx={{ color: '#FF7A35', fontSize: 20 }} />
                                )}
                                <Box>
                                  <Typography variant="body2" fontWeight="medium">
                                    {feature.name}
                                    {typeof feature.premium !== 'boolean' && (
                                      <Typography component="span" fontWeight="bold" color="#FF7A35" ml={1}>
                                        {feature.premium}
                                      </Typography>
                                    )}
                                  </Typography>
                                  <Typography variant="caption" color="text.secondary" sx={{ display: 'block' }}>
                                    {feature.descriptionPremium || feature.description}
                                  </Typography>
                                </Box>
                              </Stack>
                            </Box>
                          ))}
                        </Stack>
                      </CardContent>
                      <Box sx={{ p: 3, bgcolor: alpha('#FF7A35', 0.04), borderTop: '1px solid', borderColor: alpha('#FF7A35', 0.2) }}>
                        {subscriptionStatus?.plan !== 'premium' ? (
                          <motion.div whileHover={{ scale: 1.03 }} whileTap={{ scale: 0.98 }}>
                            <Button
                              variant="contained"
                              fullWidth
                              onClick={() => handleTabChange('customize')}
                              startIcon={<UpgradeIcon />}
                              sx={{
                                py: 1.2,
                                bgcolor: '#FF7A35',
                                '&:hover': { bgcolor: '#E16B28' },
                                borderRadius: '30px',
                                fontWeight: 'bold',
                                boxShadow: '0 6px 15px rgba(255, 122, 53, 0.25)'
                              }}
                            >
                              Choisir le plan Premium
                            </Button>
                          </motion.div>
                        ) : (
                          <Button
                            variant="contained"
                            fullWidth
                            disabled
                            sx={{
                              py: 1.2,
                              bgcolor: '#FF7A35',
                              '&:hover': { bgcolor: '#E16B28' },
                              borderRadius: '30px',
                              fontWeight: 'bold',
                              '&.Mui-disabled': { opacity: 0.7 }
                            }}
                          >
                            Plan actuel
                          </Button>
                        )}
                      </Box>
                    </Card>
                  </motion.div>
                </Box>
              </Grid>
            </Grid>
          </Box>

          {/* FAQ ou Informations complémentaires */}
          <Paper
            elevation={0}
            sx={{
              p: 3,
              borderRadius: 3,
              border: '1px solid #e0e0e0',
              boxShadow: '0 4px 12px rgba(0, 0, 0, 0.03)'
            }}
          >
            <Box sx={{ mb: 2 }}>
              <Typography variant="h5" fontWeight="bold">
                Questions fréquentes
              </Typography>
            </Box>
            <Stack spacing={2.5}>
              <Box>
                <Typography variant="subtitle1" fontWeight="bold">
                  Comment fonctionne la facturation ?
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Vous êtes facturé mensuellement à la date anniversaire de votre souscription. Le paiement s'effectue automatiquement par carte bancaire sur un système sécurisé.
                </Typography>
              </Box>
              <Box>
                <Typography variant="subtitle1" fontWeight="bold">
                  Puis-je changer de plan à tout moment ?
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Oui, vous pouvez passer du plan Gratuit au plan Premium à tout moment. Les modifications sont appliquées immédiatement et votre carte sera débitée du montant total du mois en cours (pas de prorata).
                </Typography>
              </Box>
              <Box>
                <Typography variant="subtitle1" fontWeight="bold">
                  Comment modifier mes options Premium ?
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Vous pouvez ajuster vos options à tout moment depuis cette page. Les modifications seront appliquées immédiatement. Vous pouvez personnaliser le nombre de services, de galeries et votre zone d'intervention.
                </Typography>
              </Box>
              <Box>
                <Typography variant="subtitle1" fontWeight="bold">
                  Quelles sont les différences entre les plans Gratuit et Premium ?
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Le plan Premium offre davantage de services, plus de galeries photos, une zone d'intervention étendue, des fonctionnalités avancées comme les statistiques détaillées et le support prioritaire, ainsi que des factures et devis illimités.
                </Typography>
              </Box>
              <Box>
                <Typography variant="subtitle1" fontWeight="bold">
                  Que se passe-t-il si j'annule mon abonnement Premium ?
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Votre abonnement reste actif jusqu'à la fin de la période de facturation en cours. Ensuite, votre compte basculera automatiquement vers le plan Gratuit.
                </Typography>
              </Box>
              <Box>
                <Typography variant="subtitle1" fontWeight="bold">
                  Comment fonctionnent les services additionnels ?
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Vous pouvez ajouter des services supplémentaires à votre forfait. Ces services vous permettent de diversifier votre offre et d'apparaître dans plus de catégories de recherche sur JobPartiel.
                </Typography>
              </Box>
              <Box>
                <Typography variant="subtitle1" fontWeight="bold">
                  Comment fonctionne l'option "France entière" ?
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  L'option France entière vous permet de proposer vos services sur tout le territoire français, sans limitation géographique. Cette option est disponible uniquement pour les abonnés Premium moyennant un supplément mensuel.
                </Typography>
              </Box>
              <Box>
                <Typography variant="subtitle1" fontWeight="bold">
                  Y a-t-il des frais supplémentaires pour les transactions ?
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  JobPartiel n'applique pas de commission sur les transactions réalisées via la plateforme.
                </Typography>
              </Box>
              <Box>
                <Typography variant="subtitle1" fontWeight="bold">
                  Y a-t-il une limite de réponses aux missions pour les comptes gratuits ?
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Oui, avec l'offre gratuite, vous pouvez envoyer jusqu'à 5 candidatures ou offres par mois. Passez Premium pour lever cette limite et postuler sans restriction à toutes les missions qui vous intéressent.
                </Typography>
              </Box>
            </Stack>
          </Paper>
        </motion.div>
      </AnimatePresence>
    );
  };

  // Rendu du composant pour personnaliser les options premium
  const renderCustomizeOptions = () => {
    if (!customizableOptions.length) return null;

    return (
      <AnimatePresence mode="wait">
        <motion.div
          key="customize-options"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          exit={{ opacity: 0, y: -20 }}
          transition={{ duration: 0.5, type: "spring" }}
        >
          <Paper
            elevation={0}
            sx={{
              p: { xs: 2, md: 3 },
              mb: 4,
              borderRadius: 3,
              border: '1px solid',
              borderColor: alpha('#FF7A35', 0.3),
              background: `linear-gradient(135deg, white 0%, ${alpha('#FF7A35', 0.03)} 100%)`,
              position: 'relative',
              overflow: 'hidden',
              '&::before': {
                content: '""',
                position: 'absolute',
                top: -100,
                right: -100,
                width: 300,
                height: 300,
                background: `radial-gradient(circle at top right, ${alpha('#FF7A35', 0.1)}, transparent 70%)`,
                borderRadius: '0 0 0 100%',
                zIndex: 0
              }
            }}
          >
            <Box sx={{ mb: 3, position: 'relative', zIndex: 1 }}>
              <Typography variant="h4" fontWeight="bold" color="#FF7A35">
                Personnalisez votre plan Premium
              </Typography>
              <Typography variant="body1" color="text.secondary" sx={{ mt: 0.5 }}>
                Adaptez votre abonnement selon vos besoins spécifiques et payez uniquement pour ce que vous utilisez
              </Typography>
            </Box>

            <Grid container spacing={2.5} sx={{ position: 'relative', zIndex: 1 }}>
              {customizableOptions.map((option, index) => (
                <Grid size={12} key={option.key}>
                  <motion.div
                    initial={{ opacity: 0, y: 10 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: index * 0.1, duration: 0.3 }}
                    whileHover={{
                      y: -5,
                      boxShadow: '0 10px 25px rgba(0, 0, 0, 0.08)'
                    }}
                  >
                    <Paper
                      elevation={0}
                      sx={{
                        p: { xs: 2, md: 3 },
                        borderRadius: 3,
                        border: '1px solid',
                        borderColor: (selectedValues[option.key] > option.included || (option.key === 'interventionAreas' && isFranceEntiere))
                          ? alpha('#FF7A35', 0.3)
                          : '#e0e0e0',
                        transition: 'all 0.3s ease',
                        background: (selectedValues[option.key] > option.included || (option.key === 'interventionAreas' && isFranceEntiere))
                          ? `linear-gradient(135deg, white 0%, ${alpha('#FF7A35', 0.05)} 100%)`
                          : 'white',
                        boxShadow: (selectedValues[option.key] > option.included || (option.key === 'interventionAreas' && isFranceEntiere))
                          ? '0 6px 16px rgba(255, 122, 53, 0.1)'
                          : '0 4px 12px rgba(0, 0, 0, 0.03)'
                      }}
                    >
                      <Grid container spacing={2} alignItems="center">
                        <Grid size={{ xs: 12, md: 3 }}>
                          <Stack spacing={1} direction="row" alignItems="center">
                            <Avatar
                              sx={{
                                bgcolor: alpha('#FF7A35', 0.1),
                                color: '#FF7A35',
                                width: 48,
                                height: 48
                              }}
                            >
                              {option.icon}
                            </Avatar>
                            <Stack spacing={0.5}>
                              <Typography variant="h6" fontWeight="bold">
                                {option.name}
                              </Typography>
                              <Typography variant="body2" color="text.secondary">
                                {option.description}
                              </Typography>
                            </Stack>
                          </Stack>
                        </Grid>
                        <Grid size={{ xs: 12, md: 6 }}>
                          <Box>
                            <Stack
                              direction={isMobile ? "column" : "row"}
                              justifyContent="space-between"
                              spacing={isMobile ? 1 : 0}
                              sx={{ mb: 1 }}
                            >
                              <Typography variant="body2" fontWeight="medium">
                                <span style={{ color: '#9e9e9e' }}>Base:</span> {option.included} inclus
                              </Typography>
                              <Typography variant="body2" fontWeight="medium">
                                <span style={{ color: '#FF7A35' }}>Sélectionné:</span> {
                                  option.key === 'interventionAreas' && isFranceEntiere
                                    ? 'France entière'
                                    : `${selectedValues[option.key] || option.included}${option.unit ? ` ${option.unit}` : ''}`
                                }
                              </Typography>
                            </Stack>

                            {option.key === 'services' ? (
                              <Box>
                                <Stack
                                  direction={isMobile ? "column" : "row"}
                                  spacing={isMobile ? 1.5 : 2}
                                  alignItems={isMobile ? "flex-start" : "center"}
                                  sx={{ mt: 2, mb: 1 }}
                                >
                                  <Button
                                    variant="outlined"
                                    color="warning"
                                    onClick={handleOpenServicesDialog}
                                    startIcon={<CategoryIcon />}
                                    size="medium"
                                    fullWidth={isMobile}
                                    sx={{
                                      borderRadius: '20px',
                                      borderColor: '#FF7A35',
                                      color: '#FF7A35',
                                      '&:hover': {
                                        backgroundColor: alpha('#FF7A35', 0.05),
                                        borderColor: '#FF7A35',
                                      }
                                    }}
                                  >
                                    Choisir mes services
                                  </Button>

                                  <Typography variant="body2" color="text.secondary">
                                    {currentServiceCount} services sélectionnés sur {selectedValues[option.key] || option.included} possibles
                                  </Typography>
                                </Stack>

                                {selectedServices.length > 0 && (
                                  <Box sx={{ mt: 2, p: 2, backgroundColor: alpha('#f5f5f5', 0.5), borderRadius: 2 }}>
                                    <Typography variant="subtitle2" fontWeight="medium" gutterBottom>
                                      Services sélectionnés:
                                    </Typography>
                                    <Grid container spacing={1}>
                                      {selectedServices.map((service) => (
                                        <Grid size={{ xs: 12, sm: 6, md: 4 }} key={service.categoryId}>
                                          <Box sx={{ mb: 1 }}>
                                            <Typography variant="body2" fontWeight="bold" color="#FF7A35">
                                              {service.category.nom} ({service.subcategoryIds.length})
                                            </Typography>
                                            {service.subcategoryIds.map(subId => {
                                              const subcategory = serviceSubcategories.find(s => s.id === subId);
                                              return (
                                                <Typography key={subId} variant="caption" component="div" sx={{ display: 'flex', alignItems: 'center' }}>
                                                  <CheckIcon sx={{ fontSize: 12, color: '#FF7A35', mr: 0.5 }} />
                                                  {subcategory?.nom || subId}
                                                </Typography>
                                              );
                                            })}
                                          </Box>
                                        </Grid>
                                      ))}
                                    </Grid>
                                  </Box>
                                )}

                                <Slider
                                  value={selectedValues[option.key] || option.included}
                                  min={option.included}
                                  max={totalSubcategoriesCount}
                                  step={1}
                                  onChange={(_, value) => handleOptionChange(option.key, value as number)}
                                  valueLabelDisplay="auto"
                                  sx={{
                                    color: '#FF7A35',
                                    height: 8,
                                    '& .MuiSlider-thumb': {
                                      width: 28,
                                      height: 28,
                                      bgcolor: 'white',
                                      border: '2px solid #FF7A35',
                                      boxShadow: '0 3px 8px rgba(255, 122, 53, 0.3)',
                                      '&:hover, &.Mui-focusVisible': {
                                        boxShadow: `0px 0px 0px 8px ${alpha('#FF7A35', 0.16)}`
                                      }
                                    },
                                    '& .MuiSlider-rail': {
                                      height: 8,
                                      borderRadius: 4,
                                      bgcolor: alpha('#e0e0e0', 0.5)
                                    },
                                    '& .MuiSlider-track': {
                                      height: 8,
                                      borderRadius: 4
                                    },
                                    '& .MuiSlider-valueLabel': {
                                      backgroundColor: '#FF7A35',
                                      borderRadius: '10px',
                                      boxShadow: '0 2px 10px rgba(0, 0, 0, 0.1)',
                                      '&::before': {
                                        border: 'none'
                                      }
                                    }
                                  }}
                                />
                                {/* Ajout d'une information explicite pour l'utilisateur */}
                                <Typography variant="caption" color="text.secondary" sx={{ mt: 1, display: 'block', fontStyle: 'italic' }}>
                                  Il n'est pas obligatoire de choisir une catégorie ou sous-catégorie précise. Ce qui compte, c'est le nombre total de sous-catégories dans lesquelles vous souhaitez proposer un service.
                                </Typography>
                              </Box>
                            ) : option.key === 'interventionAreas' ? (
                              <Box>
                                {/* Option France entière */}
                                <FormControlLabel
                                  control={
                                    <Checkbox
                                      checked={isFranceEntiere}
                                      onChange={handleFranceEntiereChange}
                                      sx={{
                                        color: '#bdbdbd',
                                        '&.Mui-checked': {
                                          color: '#FF7A35',
                                        },
                                      }}
                                    />
                                  }
                                  label={
                                    <Typography variant="body2" fontWeight={isFranceEntiere ? 'bold' : 'medium'} color={isFranceEntiere ? '#FF7A35' : 'text.primary'}>
                                      France entière (+{subscriptionConfig?.data?.premium?.interventionAreas?.franceEntiere?.additionalCost || priceConfig.franceEntierePrice}€/mois)
                                    </Typography>
                                  }
                                  sx={{
                                    mb: 2,
                                    p: 1,
                                    bgcolor: isFranceEntiere ? alpha('#FF7A35', 0.08) : 'transparent',
                                    borderRadius: 2,
                                    border: isFranceEntiere ? `1px solid ${alpha('#FF7A35', 0.2)}` : 'none'
                                  }}
                                />

                                {!isFranceEntiere && (
                                  <Slider
                                    value={selectedValues[option.key] || option.included}
                                    min={option.included}
                                    max={maxInterventionDistance || 120}
                                    step={10}
                                    disabled={isFranceEntiere}
                                    onChange={(_, value) => handleOptionChange(option.key, value as number)}
                                    valueLabelDisplay="auto"
                                    valueLabelFormat={value => `${value}${option.unit ? ` ${option.unit}` : ''}`}
                                    sx={{
                                      color: '#FF7A35',
                                      height: 8,
                                      '& .MuiSlider-thumb': {
                                        width: 28,
                                        height: 28,
                                        bgcolor: 'white',
                                        border: '2px solid #FF7A35',
                                        boxShadow: '0 3px 8px rgba(255, 122, 53, 0.3)',
                                        '&:hover, &.Mui-focusVisible': {
                                          boxShadow: `0px 0px 0px 8px ${alpha('#FF7A35', 0.16)}`
                                        }
                                      },
                                      '& .MuiSlider-rail': {
                                        height: 8,
                                        borderRadius: 4,
                                        bgcolor: alpha('#e0e0e0', 0.5)
                                      },
                                      '& .MuiSlider-track': {
                                        height: 8,
                                        borderRadius: 4
                                      },
                                      '& .MuiSlider-valueLabel': {
                                        backgroundColor: '#FF7A35',
                                        borderRadius: '10px',
                                        boxShadow: '0 2px 10px rgba(0, 0, 0, 0.1)',
                                        '&::before': {
                                          border: 'none'
                                        }
                                      }
                                    }}
                                  />
                                )}

                                {/* Affichage de la carte pour la zone d'intervention */}
                                {!isFranceEntiere && (
                                  <Box sx={{ mt: 2 }}>
                                    <InterventionZoneMap
                                      center={zoneCenter}
                                      radius={selectedValues[option.key] || option.included}
                                      onCenterChange={(center) => setZoneCenter([center[0], center[1]] as [number, number])}
                                      isOwnProfil={false}
                                      zoom={9}
                                    />
                                    <Typography variant="caption" color="text.secondary" sx={{ mt: 1, display: 'block' }}>
                                      Visualisation de votre zone d'intervention actuelle. Déverrouillez la carte pour zoomer et explorer votre zone.
                                    </Typography>
                                  </Box>
                                )}
                              </Box>
                            ) : (
                              <Slider
                                value={selectedValues[option.key] || option.included}
                                min={option.included}
                                max={30}
                                step={1}
                                onChange={(_, value) => handleOptionChange(option.key, value as number)}
                                valueLabelDisplay="auto"
                                valueLabelFormat={value => `${value}${option.unit ? ` ${option.unit}` : ''}`}
                                sx={{
                                  color: '#FF7A35',
                                  height: 8,
                                  '& .MuiSlider-thumb': {
                                    width: 28,
                                    height: 28,
                                    bgcolor: 'white',
                                    border: '2px solid #FF7A35',
                                    boxShadow: '0 3px 8px rgba(255, 122, 53, 0.3)',
                                    '&:hover, &.Mui-focusVisible': {
                                      boxShadow: `0px 0px 0px 8px ${alpha('#FF7A35', 0.16)}`
                                    }
                                  },
                                  '& .MuiSlider-rail': {
                                    height: 8,
                                    borderRadius: 4,
                                    bgcolor: alpha('#e0e0e0', 0.5)
                                  },
                                  '& .MuiSlider-track': {
                                    height: 8,
                                    borderRadius: 4
                                  },
                                  '& .MuiSlider-valueLabel': {
                                    backgroundColor: '#FF7A35',
                                    borderRadius: '10px',
                                    boxShadow: '0 2px 10px rgba(0, 0, 0, 0.1)',
                                    '&::before': {
                                      border: 'none'
                                    }
                                  }
                                }}
                              />
                            )}
                          </Box>
                        </Grid>
                        <Grid size={{ xs: 12, md: 3 }}>
                          <Box
                            sx={{
                              p: 2,
                              borderRadius: 3,
                              bgcolor: (selectedValues[option.key] > option.included || (option.key === 'interventionAreas' && isFranceEntiere))
                                ? alpha('#FF7A35', 0.08)
                                : alpha('#f8f9fc', 0.8),
                              border: '1px solid',
                              borderColor: (selectedValues[option.key] > option.included || (option.key === 'interventionAreas' && isFranceEntiere))
                                ? alpha('#FF7A35', 0.3)
                                : '#e0e0e0',
                              transition: 'all 0.3s ease'
                            }}
                          >
                            <Stack spacing={0.5}>
                              <Typography variant="body2" color="text.secondary">
                                Coût additionnel:
                              </Typography>
                              <Typography variant="h6" fontWeight="bold" color={(selectedValues[option.key] > option.included || (option.key === 'interventionAreas' && isFranceEntiere)) ? '#FF7A35' : '#616161'}>
                                {(
                                  option.key === 'interventionAreas' && isFranceEntiere
                                    ? (subscriptionConfig?.data?.premium?.interventionAreas?.franceEntiere?.additionalCost || priceConfig.franceEntierePrice)
                                    : option.key === 'interventionAreas'
                                      ? Math.ceil((selectedValues[option.key] - option.included) / 10) * option.additionalCost
                                      : (selectedValues[option.key] - option.included) * option.additionalCost
                                )}€
                              </Typography>
                              <Typography variant="body2" color="text.secondary">
                                {option.key === 'interventionAreas' && isFranceEntiere ? (
                                  'Option France entière'
                                ) : selectedValues[option.key] > option.included ? (
                                  <>
                                    Pour {selectedValues[option.key] - option.included}
                                    {option.unit ? ` ${option.unit}` : ''} supplémentaires
                                  </>
                                ) : (
                                  'Inclus dans le forfait'
                                )}
                              </Typography>
                            </Stack>
                          </Box>
                        </Grid>
                      </Grid>
                    </Paper>
                  </motion.div>
                </Grid>
              ))}
            </Grid>

            {/* Ajouter le champ de code promo */}
            {renderPromoCodeInput()}
            {/* Affichage de la liste des codes promo utilisateur */}
            {renderUserPromoCodes()}

            {/* Remplacer la section du prix total par l'appel à renderPriceSection */}
            <Box
              sx={{
                display: 'flex',
                flexDirection: { xs: 'column', md: 'row' },
                justifyContent: 'space-between',
                alignItems: { xs: 'flex-start', md: 'center' },
                p: 3,
                mt: 4,
                mb: 2,
                borderRadius: 2,
                bgcolor: alpha('#FF7A35', 0.03),
                border: '1px solid',
                borderColor: alpha('#FF7A35', 0.2),
              }}
            >
              {renderPriceSection()}
            </Box>
          </Paper>
        </motion.div>
      </AnimatePresence>
    );
  };

  // Nouvelle fonction pour gérer l'expansion/contraction des cartes de catégories
  const handleCardExpand = (categoryId: string, rowIndex: number) => {
    if (isMobile) {
      // Sur mobile : toggle individuel, ne ferme pas les autres
      setExpandedCards(prev => ({ ...prev, [categoryId]: !prev[categoryId] }));
      return;
    }
    // Desktop : comportement par ligne (inchangé)
    const sortedCategories = [...SERVICE_CATEGORIES].sort((a, b) =>
      a.nom.localeCompare(b.nom, 'fr', { sensitivity: 'base' })
    );

    const firstRowCategories = sortedCategories.slice(0, 6);
    const secondRowCategories = sortedCategories.slice(6, 12);

    // Cartes sur la même ligne (3 par ligne en desktop)
    const cardsPerRow = 3;
    const startIdx = rowIndex * cardsPerRow;
    const endIdx = Math.min(startIdx + cardsPerRow, rowIndex < 2 ? 6 : 12);

    // Obtenir les catégories de la même rangée
    const rowCategories = rowIndex < 2
      ? firstRowCategories.slice(startIdx, endIdx)
      : secondRowCategories.slice(startIdx - 6, endIdx - 6);

    // Vérifier si les cartes sont actuellement dépliées ou repliées
    const isCurrentlyExpanded = expandedCards[categoryId] || false;

    // Créer un nouvel état pour l'expansion des cartes
    const newExpandedState = { ...expandedCards };

    // Si la carte cliquée est actuellement fermée, on ouvre toutes les cartes de la rangée
    // Si la carte cliquée est actuellement ouverte, on ferme toutes les cartes de la rangée
    rowCategories.forEach(cat => {
      newExpandedState[cat.id] = !isCurrentlyExpanded;
    });

    setExpandedCards(newExpandedState);
  };

  // Dialog pour la sélection des services
  const renderServicesSelectionDialog = () => (
    <Dialog
      open={servicesDialogOpen}
      onClose={handleCloseServicesDialog}
      fullWidth
      maxWidth="md"
      PaperProps={{
        sx: {
          borderRadius: 3,
          overflow: 'hidden',
          maxWidth: { xs: '94vw', sm: 980 },
          m: { xs: 0, sm: 2 },
          width: { xs: '94vw', sm: 'auto' },
        }
      }}
    >
      <DialogTitle sx={{ bgcolor: alpha('#FF7A35', 0.05), py: { xs: 1.5, sm: 2 }, px: { xs: 1, sm: 3 }, position: 'relative' }}>
        <Stack
          direction={{ xs: 'column', sm: 'row' }}
          justifyContent="space-between"
          alignItems={{ xs: 'flex-start', sm: 'center' }}
          spacing={1.5}
        >
          <Typography variant="h5" fontWeight="bold" color="#212121">
            Personnaliser mes services
          </Typography>
          <Stack
            direction="row"
            spacing={2}
            alignItems="center"
            justifyContent={{ xs: 'flex-start', sm: 'center' }}
            sx={{ mt: { xs: 1, sm: 0 }, width: { xs: '100%', sm: 'auto' } }}
          >
            <Chip
              label={`${currentServiceCount}/${selectedValues.services || priceConfig.maxIncludedServices} services`}
              color={currentServiceCount >= (selectedValues.services || priceConfig.maxIncludedServices) ? "error" : "default"}
              sx={{
                borderRadius: '20px',
                bgcolor: currentServiceCount >= (selectedValues.services || priceConfig.maxIncludedServices) ? alpha('#f44336', 0.1) : alpha('#FF7A35', 0.1),
                color: currentServiceCount >= (selectedValues.services || priceConfig.maxIncludedServices) ? '#f44336' : '#FF7A35',
                fontWeight: 'bold',
                border: '1px solid',
                borderColor: currentServiceCount >= (selectedValues.services || priceConfig.maxIncludedServices) ? alpha('#f44336', 0.3) : alpha('#FF7A35', 0.3),
                transform: { xs: 'none', sm: 'translateX(-32px)' }
              }}
            />
          </Stack>
        </Stack>
        <IconButton onClick={handleCloseServicesDialog} aria-label="fermer" size="small" sx={{ position: 'absolute', top: 8, right: 8 }}>
          <CloseIcon />
        </IconButton>
      </DialogTitle>
      <DialogContent sx={{ py: { xs: 1, sm: 3 }, px: { xs: 1, sm: 3 } }}>
        <Box sx={{
          mb: 3,
          p: { xs: 1.5, sm: 3 },
          borderRadius: 2,
          bgcolor: alpha('#f5f5f5', 0.5),
          border: '1px solid #e0e0e0',
          boxShadow: '0 2px 8px rgba(0,0,0,0.05)'
        }}>
          <Stack direction="row" spacing={2} alignItems="flex-start">
            <InfoIcon color="info" sx={{ mt: 0.5 }} />
            <Box>
              <Typography variant="subtitle1" fontWeight="bold" gutterBottom>
                Information importante
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Votre abonnement Premium inclut <strong>{priceConfig.maxIncludedServices} services</strong> de base.
                Si vous avez besoin de plus de {priceConfig.maxIncludedServices} services, vous pouvez augmenter cette limite
                avec le curseur dans les options, ou <strong>en sélectionnant simplement plus de services ici</strong> -
                le quota sera automatiquement ajusté (<strong>{subscriptionConfig?.data?.premium?.services?.additionalCost || priceConfig.servicesAdditionalCost}€ par service</strong> en plus de votre abonnement de base).
              </Typography>
            </Box>
          </Stack>
        </Box>

        <Grid container spacing={4}>
          {/* Trier les catégories par ordre alphabétique */}
          {[...SERVICE_CATEGORIES]
            .sort((a, b) => a.nom.localeCompare(b.nom, 'fr', { sensitivity: 'base' }))
            .slice(0, 6)
            .map((category, catIndex) => {
            // Récupérer et trier les sous-catégories pour cette catégorie
            const categorySubcategories = serviceSubcategories
              .filter(s => s.categoryId === category.id)
              .sort((a, b) => a.nom.localeCompare(b.nom, 'fr', { sensitivity: 'base' }));

            // Vérifier si cette carte est dépliée
            const isExpanded = expandedCards[category.id] || false;

            // Déterminer à quelle rangée appartient cette carte
            const rowIndex = Math.floor(catIndex / 3);

            return (
              <Grid size={{ xs: 12, sm: 6, md: 4 }} key={category.id}>
                <Card
                  sx={{
                    height: isExpanded ? 'auto' : '100%',
                    borderRadius: 4,
                    boxShadow: '0 4px 16px rgba(0,0,0,0.08)',
                    overflow: 'hidden',
                    display: 'flex',
                    flexDirection: 'column',
                    position: 'relative',
                    transition: 'box-shadow 0.3s, transform 0.3s',
                    background: 'transparent',
                    border: 'none',
                    '&:hover': {
                      transform: 'translateY(-6px) scale(1.03)',
                      boxShadow: '0 12px 32px rgba(255,122,53,0.13)',
                    },
                    cursor: 'pointer',
                  }}
                  onClick={() => handleCardExpand(category.id, rowIndex)}
                >
                  <Box sx={{ position: 'relative', width: '100%', height: 180, overflow: 'hidden', borderTopLeftRadius: 16, borderTopRightRadius: 16 }}>
                    <CardMedia
                      component="img"
                      height="180"
                      image={category.image.webp}
                      alt={category.image.alt}
                      sx={{ objectFit: 'cover', width: '100%', height: 180, borderTopLeftRadius: 16, borderTopRightRadius: 16 }}
                    />
                    {/* Overlay pour le titre */}
                    <Box sx={{
                      position: 'absolute',
                      top: 0,
                      left: 0,
                      width: '100%',
                      height: '100%',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      pointerEvents: 'none',
                    }}>
                      <Box sx={{
                        bgcolor: 'rgba(34,34,34,0.68)',
                        borderRadius: 3,
                        px: 2.5,
                        py: 1.1,
                        boxShadow: '0 4px 16px rgba(0,0,0,0.18)',
                        display: 'inline-block',
                        maxWidth: '92%',
                      }}>
                        <Typography variant="h6" fontWeight="bold" color="#fff" sx={{
                          textAlign: 'center',
                          letterSpacing: 0.2,
                          fontSize: { xs: '1rem', md: '1.08rem' },
                          textShadow: '0 2px 8px rgba(0,0,0,0.18)',
                          lineHeight: 1.25,
                          px: 0.5,
                          overflow: 'hidden',
                          textOverflow: 'ellipsis',
                          display: '-webkit-box',
                          WebkitLineClamp: 2,
                          WebkitBoxOrient: 'vertical',
                          whiteSpace: 'normal',
                          wordBreak: 'break-word',
                          maxHeight: 48,
                          minHeight: 32,
                        }}>
                          {category.nom}
                        </Typography>
                      </Box>
                    </Box>
                  </Box>
                  <CardContent sx={{ flexGrow: 1, p: 0, pt: 0, pb: 0, display: 'flex', flexDirection: 'column', alignItems: 'center', background: 'transparent', minHeight: 0 }}>
                    <CategoryDescription
                      sx={{
                        borderRadius: 0,
                        marginBottom: 0,
                        marginTop: 0,
                        paddingLeft: 0,
                        paddingRight: 0,
                        width: '100%',
                        boxShadow: 'none',
                        border: 'none',
                        flexGrow: 1,
                        minHeight: 0,
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        height: '100%',
                      }}
                    >
                      {category.description}
                    </CategoryDescription>
                    <AnimatePresence>
                      {isExpanded && (
                        <motion.div
                          initial={{ opacity: 0, height: 0 }}
                          animate={{ opacity: 1, height: 'auto' }}
                          exit={{ opacity: 0, height: 0 }}
                          transition={{ duration: 0.3 }}
                        >
                          <Box sx={{
                            display: 'flex',
                            alignItems: 'center',
                            justifyContent: 'space-between',
                            mb: 1
                          }}>
                            <Typography variant="subtitle2" fontWeight="bold" color="#555">
                              Sous-catégories disponibles
                            </Typography>
                            <Typography variant="caption" color="text.secondary">
                              {categorySubcategories.length} options
                            </Typography>
                          </Box>
                          <List dense sx={{
                            pt: 0,
                            maxHeight: 300,
                            overflow: 'auto',
                            px: 0.5,
                            mt: 1,
                            '& .MuiListItem-root': {
                              transition: 'all 0.2s ease'
                            }
                          }}>
                            {categorySubcategories.map((subcategory) => {
                              const isSelected = isSubcategorySelected(category.id, subcategory.id);

                              return (
                                <ListItem
                                  key={subcategory.id}
                                  disablePadding
                                  sx={{
                                    py: 0.8,
                                    mb: 0.8,
                                    borderRadius: 2,
                                    bgcolor: isSelected ? alpha('#FF7A35', 0.08) : 'transparent',
                                    border: isSelected ? `1px solid ${alpha('#FF7A35', 0.15)}` : 'none',
                                    '&:hover': {
                                      bgcolor: isSelected ? alpha('#FF7A35', 0.12) : alpha('#f5f5f5', 0.8)
                                    }
                                  }}
                                  onClick={(e) => {
                                    e.stopPropagation();
                                    handleServiceSelection(category.id, subcategory.id);
                                  }}
                                >
                                  <FormControlLabel
                                    control={
                                      <Checkbox
                                        checked={isSelected}
                                        onChange={(e) => {
                                          e.stopPropagation();
                                          handleServiceSelection(category.id, subcategory.id);
                                        }}
                                        sx={{
                                          color: '#bdbdbd',
                                          '&.Mui-checked': {
                                            color: '#FF7A35',
                                          },
                                        }}
                                      />
                                    }
                                    label={
                                      <ListItemText
                                        primary={subcategory.nom}
                                        secondary={subcategory.description}
                                        primaryTypographyProps={{
                                          variant: 'body2',
                                          fontWeight: isSelected ? 'bold' : 'medium',
                                          color: isSelected ? '#FF7A35' : 'text.primary'
                                        }}
                                        secondaryTypographyProps={{
                                          variant: 'caption'
                                        }}
                                      />
                                    }
                                    sx={{ width: '100%', ml: 0 }}
                                    onClick={(e) => e.stopPropagation()}
                                  />
                                </ListItem>
                              );
                            })}
                          </List>
                        </motion.div>
                      )}
                    </AnimatePresence>
                  </CardContent>
                </Card>
              </Grid>
            );
          })}
        </Grid>

        {SERVICE_CATEGORIES.length > 6 && (
          <Grid container spacing={4} sx={{ mt: 2 }}>
            {/* Trier les catégories par ordre alphabétique */}
            {[...SERVICE_CATEGORIES]
              .sort((a, b) => a.nom.localeCompare(b.nom, 'fr', { sensitivity: 'base' }))
              .slice(6, 12)
              .map((category, catIndex) => {
              // Récupérer et trier les sous-catégories pour cette catégorie
              const categorySubcategories = serviceSubcategories
                .filter(s => s.categoryId === category.id)
                .sort((a, b) => a.nom.localeCompare(b.nom, 'fr', { sensitivity: 'base' }));

              // Vérifier si cette carte est dépliée
              const isExpanded = expandedCards[category.id] || false;

              // Déterminer à quelle rangée appartient cette carte (2 ou 3 pour la deuxième grille)
              const rowIndex = 2 + Math.floor(catIndex / 3);

              return (
                <Grid size={{ xs: 12, sm: 6, md: 4 }} key={category.id}>
                  <Card
                    sx={{
                      height: isExpanded ? 'auto' : '100%',
                      borderRadius: 4,
                      boxShadow: '0 4px 16px rgba(0,0,0,0.08)',
                      overflow: 'hidden',
                      display: 'flex',
                      flexDirection: 'column',
                      position: 'relative',
                      transition: 'box-shadow 0.3s, transform 0.3s',
                      '&:hover': {
                        transform: 'translateY(-6px) scale(1.03)',
                        boxShadow: '0 12px 32px rgba(255,122,53,0.13)',
                      },
                      cursor: 'pointer',
                      background: '#fff',
                    }}
                    onClick={() => handleCardExpand(category.id, rowIndex)}
                  >
                    <Box sx={{ position: 'relative', width: '100%', height: 180, overflow: 'hidden', borderTopLeftRadius: 16, borderTopRightRadius: 16 }}>
                      <CardMedia
                        component="img"
                        height="180"
                        image={category.image.webp}
                        alt={category.image.alt}
                        sx={{ objectFit: 'cover', width: '100%', height: 180, borderTopLeftRadius: 16, borderTopRightRadius: 16 }}
                      />
                      {/* Overlay pour le titre */}
                      <Box sx={{
                        position: 'absolute',
                        top: 0,
                        left: 0,
                        width: '100%',
                        height: '100%',
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        pointerEvents: 'none',
                      }}>
                        <Box sx={{
                          bgcolor: 'rgba(34,34,34,0.68)',
                          borderRadius: 3,
                          px: 2.5,
                          py: 1.1,
                          boxShadow: '0 4px 16px rgba(0,0,0,0.18)',
                          display: 'inline-block',
                          maxWidth: '92%',
                        }}>
                          <Typography variant="h6" fontWeight="bold" color="#fff" sx={{
                            textAlign: 'center',
                            letterSpacing: 0.2,
                            fontSize: { xs: '1rem', md: '1.08rem' },
                            textShadow: '0 2px 8px rgba(0,0,0,0.18)',
                            lineHeight: 1.25,
                            px: 0.5,
                            overflow: 'hidden',
                            textOverflow: 'ellipsis',
                            display: '-webkit-box',
                            WebkitLineClamp: 2,
                            WebkitBoxOrient: 'vertical',
                            whiteSpace: 'normal',
                            wordBreak: 'break-word',
                            maxHeight: 48,
                            minHeight: 32,
                          }}>
                            {category.nom}
                          </Typography>
                        </Box>
                      </Box>
                    </Box>
                    <CardContent sx={{ flexGrow: 1, p: 0, pt: 0, pb: 0, display: 'flex', flexDirection: 'column', alignItems: 'center', background: 'transparent', minHeight: 0 }}>
                      <CategoryDescription
                        sx={{
                          borderRadius: 0,
                          marginBottom: 0,
                          marginTop: 0,
                          paddingLeft: 0,
                          paddingRight: 0,
                          width: '100%',
                          boxShadow: 'none',
                          border: 'none',
                          flexGrow: 1,
                          minHeight: 0,
                          display: 'flex',
                          alignItems: 'center',
                          justifyContent: 'center',
                          height: '100%',
                        }}
                      >
                        {category.description}
                      </CategoryDescription>
                      <AnimatePresence>
                        {isExpanded && (
                          <motion.div
                            initial={{ opacity: 0, height: 0 }}
                            animate={{ opacity: 1, height: 'auto' }}
                            exit={{ opacity: 0, height: 0 }}
                            transition={{ duration: 0.3 }}
                          >
                            <Box sx={{
                              display: 'flex',
                              alignItems: 'center',
                              justifyContent: 'space-between',
                              mb: 1
                            }}>
                              <Typography variant="subtitle2" fontWeight="bold" color="#555">
                                Sous-catégories disponibles
                              </Typography>
                              <Typography variant="caption" color="text.secondary">
                                {categorySubcategories.length} options
                              </Typography>
                            </Box>
                            <List dense sx={{
                              pt: 0,
                              maxHeight: 300,
                              overflow: 'auto',
                              px: 0.5,
                              mt: 1,
                              '& .MuiListItem-root': {
                                transition: 'all 0.2s ease'
                              }
                            }}>
                              {categorySubcategories.map((subcategory) => {
                                const isSelected = isSubcategorySelected(category.id, subcategory.id);

                                return (
                                  <ListItem
                                    key={subcategory.id}
                                    disablePadding
                                    sx={{
                                      py: 0.8,
                                      mb: 0.8,
                                      borderRadius: 2,
                                      bgcolor: isSelected ? alpha('#FF7A35', 0.08) : 'transparent',
                                      border: isSelected ? `1px solid ${alpha('#FF7A35', 0.15)}` : 'none',
                                      '&:hover': {
                                        bgcolor: isSelected ? alpha('#FF7A35', 0.12) : alpha('#f5f5f5', 0.8)
                                      }
                                    }}
                                    onClick={(e) => {
                                      e.stopPropagation();
                                      handleServiceSelection(category.id, subcategory.id);
                                    }}
                                  >
                                    <FormControlLabel
                                      control={
                                        <Checkbox
                                          checked={isSelected}
                                          onChange={(e) => {
                                            e.stopPropagation();
                                            handleServiceSelection(category.id, subcategory.id);
                                          }}
                                          sx={{
                                            color: '#bdbdbd',
                                            '&.Mui-checked': {
                                              color: '#FF7A35',
                                            },
                                          }}
                                        />
                                      }
                                      label={
                                        <ListItemText
                                          primary={subcategory.nom}
                                          secondary={subcategory.description}
                                          primaryTypographyProps={{
                                            variant: 'body2',
                                            fontWeight: isSelected ? 'bold' : 'medium',
                                            color: isSelected ? '#FF7A35' : 'text.primary'
                                          }}
                                          secondaryTypographyProps={{
                                            variant: 'caption'
                                          }}
                                        />
                                      }
                                      sx={{ width: '100%', ml: 0 }}
                                      onClick={(e) => e.stopPropagation()}
                                    />
                                  </ListItem>
                                );
                              })}
                            </List>
                          </motion.div>
                        )}
                      </AnimatePresence>
                    </CardContent>
                  </Card>
                </Grid>
              );
            })}
          </Grid>
        )}

      </DialogContent>
      <DialogActions sx={{ px: { xs: 1, sm: 4 }, py: { xs: 1.5, sm: 2.5 }, bgcolor: alpha('#f5f5f5', 0.3) }}>
        <Stack direction={{ xs: 'column', sm: 'row' }} spacing={2} alignItems={{ xs: 'stretch', sm: 'center' }} sx={{ width: '100%' }}>
          <Typography variant="caption" color="text.secondary" sx={{ flexGrow: 1, fontStyle: 'italic', mb: { xs: 1, sm: 0 } }}>
            <InfoIcon sx={{ fontSize: 14, verticalAlign: 'middle', mr: 0.5, color: '#FF7A35' }} />
            Si vous sélectionnez plus de {priceConfig.maxIncludedServices} services, votre quota sera automatiquement augmenté.
            Chaque service supplémentaire est facturé <strong>{subscriptionConfig?.data?.premium?.services?.additionalCost || priceConfig.servicesAdditionalCost}€</strong>.
          </Typography>
          <Button
            onClick={handleCloseServicesDialog}
            variant="outlined"
            fullWidth={true}
            sx={{
              borderColor: '#9e9e9e',
              color: '#616161',
              py: 1,
              px: 3,
              '&:hover': {
                borderColor: '#757575',
                bgcolor: alpha('#9e9e9e', 0.05)
              }
            }}
          >
            Annuler
          </Button>
          <Button
            onClick={handleCloseServicesDialog}
            variant="contained"
            color="warning"
            fullWidth={true}
            sx={{
              bgcolor: '#FF7A35',
              '&:hover': { bgcolor: '#E16B28' },
              py: 1,
              px: 3,
              fontWeight: 'bold',
              boxShadow: '0 4px 12px rgba(255, 122, 53, 0.2)'
            }}
          >
            Valider ma sélection
          </Button>
        </Stack>
      </DialogActions>
    </Dialog>
  );

  // Vérifier la validité d'un code promo
  const validatePromoCode = async () => {
    try {
      if (!promoCode.trim()) {
        setPromoCodeMessage('Veuillez entrer un code promo');
        setPromoCodeValid(false);
        return;
      }

      setLoading(true);

      await fetchCsrfToken();
      let headers = await getCommonHeaders();
      headers = { ...headers, 'Content-Type': 'application/json' };
      headers['X-CSRF-Token'] = await fetchCsrfToken();
      const response = await axios.post(
        `${API_CONFIG.baseURL}/api/promocodes/validate`,
        {
          code: promoCode,
          plan: 'premium'
        },
        {
          headers,
          withCredentials: true
        }
      );

      if (response.data.success) {
        setPromoCodeValid(true);
        // Ajout du détail du montant ou pourcentage dans le message
        const details = response.data.data;
        let reduction = '';
        if (details.discount_type === 'percentage') {
          reduction = ` (${details.discount_value}% de réduction)`;
        } else if (details.discount_type === 'fixed') {
          reduction = ` (${details.discount_value}€ de réduction)`;
        }
        setPromoCodeMessage(`Code promo valide : ${details.description || 'Réduction appliquée'}${reduction}`);
        setPromoCodeDetails(details);

        // Calculer le prix réduit immédiatement
        let reducedPrice = totalPrice;
        if (details.discount_type === 'percentage') {
          // Appliquer un pourcentage de réduction
          const discountAmount = totalPrice * (details.discount_value / 100);
          reducedPrice = totalPrice - discountAmount;
        } else if (details.discount_type === 'fixed') {
          // Appliquer un montant fixe de réduction
          reducedPrice = Math.max(0, totalPrice - details.discount_value);
        }
        // Mettre à jour le prix réduit
        setDiscountedPrice(reducedPrice);
      } else {
        setPromoCodeValid(false);
        setPromoCodeMessage(response.data.message || 'Code promo invalide');
        setPromoCodeDetails(null);
        setDiscountedPrice(null);
      }
    } catch (error: any) {
      console.error('Erreur lors de la validation du code promo:', error);
      setPromoCodeValid(false);
      setPromoCodeMessage(error.response?.data?.message || 'Erreur lors de la validation du code promo');
      setPromoCodeDetails(null);
      setDiscountedPrice(null);
    } finally {
      setLoading(false);
    }
  };

  // Dans le renderCustomizeOptions (ou là où se trouve votre formulaire)
  const renderPromoCodeInput = () => {
    return (
      <Box sx={{ mt: 4, mb: 2 }}>
        <Typography variant="h6" sx={{ mb: 1, fontWeight: 'bold' }}>
          Code promo
        </Typography>
        <Box sx={{ display: 'flex', gap: 2, alignItems: 'center' }}>
          <TextField
            fullWidth
            variant="outlined"
            label="Avez-vous un code promo ?"
            value={promoCode}
            onChange={(e: React.ChangeEvent<HTMLInputElement>) => setPromoCode(e.target.value.toUpperCase())}
            sx={{ maxWidth: '300px' }}
            inputProps={{ style: { textTransform: 'uppercase' } }}
            error={promoCodeValid === false}
            helperText={promoCodeMessage}
          />
          <Button
            variant="outlined"
            onClick={validatePromoCode}
            disabled={!promoCode.trim() || loading}
            sx={{
              borderColor: '#FF7A35',
              color: '#FF7A35',
              fontWeight: 'bold',
              borderWidth: 2,
              borderRadius: '8px',
              px: 3,
              py: 1.2,
              transition: 'all 0.2s',
              '&:hover': {
                borderColor: '#E16B28',
                color: '#fff',
                backgroundColor: '#FF7A35',
              },
              '&.Mui-disabled': {
                color: '#bdbdbd',
                borderColor: '#e0e0e0',
                backgroundColor: '#f5f5f5',
              }
            }}
          >
            Vérifier
          </Button>
        </Box>
        {promoCodeValid && (
          <Alert severity="success" sx={{ mt: 1, maxWidth: '400px' }}>
            {promoCodeMessage}
          </Alert>
        )}
      </Box>
    );
  };

  // Dans la section de prix (dans renderCustomizeOptions ou équivalent)
  const renderPriceSection = () => (
    <Box>
      <Stack spacing={1.2} alignItems="flex-start">
        {discountedPrice !== null && promoCodeValid ? (
          <>
            <Typography
              variant="h4"
              component="div"
              sx={{
                textDecoration: 'line-through',
                color: 'text.secondary',
                fontSize: '1.5rem'
              }}
            >
              {totalPrice}€ <Typography variant="body1" component="span" sx={{ fontSize: '0.85rem' }}>/mois</Typography>
            </Typography>
            <Typography
              variant="h4"
              component="div"
              sx={{ fontWeight: 'bold', color: '#FF7A35' }}
            >
              {discountedPrice}€ <Typography variant="body1" component="span" sx={{ fontSize: '1rem' }}>/mois</Typography>
            </Typography>
          </>
        ) : (
          <Typography variant="h4" component="div" sx={{ fontWeight: 'bold' }}>
            {totalPrice}€ <Typography variant="body1" component="span" sx={{ fontSize: '1rem' }}>/mois</Typography>
          </Typography>
        )}
        <Typography variant="body2" color="text.secondary">
          Prix total incluant toutes les options personnalisées
          {promoCodeValid && promoCodeDetails && (
            <Typography component="span" color="success.main" sx={{ display: 'block', mt: 3, fontWeight: 'medium' }}>
              Code promo appliqué : {promoCodeDetails.code} ({promoCodeDetails.discount_type === 'percentage' ?
                `${promoCodeDetails.discount_value}% de réduction` :
                `${promoCodeDetails.discount_value}€ de réduction`})
            </Typography>
          )}
        </Typography>
      </Stack>
      <div tabIndex={0}>
        <Button
          variant="contained"
          startIcon={<DiamondIcon />}
          onClick={handleSubscribe}
          sx={{
            bgcolor: '#FF7A35',
            '&:hover': { bgcolor: '#E16B28' },
            borderRadius: '8px',
            fontWeight: 'bold',
            px: 3,
            py: 1.2,
            boxShadow: '0 4px 10px rgba(255, 122, 53, 0.25)',
            mt: 2
          }}
        >
          Mettre à jour mon abonnement
        </Button>
      </div>
    </Box>
  );

  // Récupérer la liste des codes promo utilisés/attribués à l'utilisateur
  const fetchUserPromoCodes = async () => {
    try {
      setLoadingPromoCodes(true);
      await fetchCsrfToken();
      const headers = await getCommonHeaders();
      const response = await axios.get(`${API_CONFIG.baseURL}/api/promocodes/me`, {
        headers,
        withCredentials: true
      });
      if (response.data.success) {
        setUserPromoCodes(response.data.data || []);
      }
    } catch (error) {
      setUserPromoCodes([]);
    } finally {
      setLoadingPromoCodes(false);
    }
  };

  // Charger la liste au montage
  useEffect(() => {
    fetchUserPromoCodes();
  }, []);

  // Ajout du rendu de la liste des codes promo utilisateur
  const renderUserPromoCodes = () => (
    <Box sx={{ mt: 2, mb: 2 }}>
      <Typography variant="subtitle1" fontWeight="bold" sx={{ mb: 1 }}>
        Mes codes promo utilisés ou attribués
      </Typography>
      {loadingPromoCodes ? (
        <Typography variant="body2" color="text.secondary">Chargement...</Typography>
      ) : userPromoCodes.length === 0 ? (
        <Typography variant="body2" color="text.secondary">Aucun code promo utilisé ou attribué.</Typography>
      ) : (
        <Box sx={{ overflowX: 'auto' }}>
          <table style={{ width: '100%', borderCollapse: 'collapse', background: 'white' }}>
            <thead>
              <tr style={{ background: '#FFF8F3' }}>
                <th style={{ padding: 8, borderBottom: '1px solid #FFE4BA', color: '#FF7A35', fontWeight: 700, textAlign: 'left' }}>Code</th>
                <th style={{ padding: 8, borderBottom: '1px solid #FFE4BA', color: '#FF7A35', fontWeight: 700, textAlign: 'center' }}>Type</th>
                <th style={{ padding: 8, borderBottom: '1px solid #FFE4BA', color: '#FF7A35', fontWeight: 700, textAlign: 'center' }}>Réduction</th>
                <th style={{ padding: 8, borderBottom: '1px solid #FFE4BA', color: '#FF7A35', fontWeight: 700, textAlign: 'center' }}>Durée</th>
                <th style={{ padding: 8, borderBottom: '1px solid #FFE4BA', color: '#FF7A35', fontWeight: 700, textAlign: 'center' }}>Date d'utilisation</th>
                <th style={{ padding: 8, borderBottom: '1px solid #FFE4BA', color: '#FF7A35', fontWeight: 700, textAlign: 'center' }}>Expiration</th>
              </tr>
            </thead>
            <tbody>
              {userPromoCodes.map((usage) => (
                <tr key={usage.id} style={{ borderBottom: '1px solid #f0f0f0' }}>
                  <td style={{ padding: 8, fontWeight: 600, textAlign: 'left' }}>{usage.promo_codes.code}</td>
                  <td style={{ padding: 8, textAlign: 'center' }}>{usage.promo_codes.discount_type === 'percentage' ? 'Pourcentage' : 'Montant fixe'}</td>
                  <td style={{ padding: 8, textAlign: 'center' }}>
                    {usage.promo_codes.discount_type === 'percentage'
                      ? `${usage.promo_codes.discount_value}%`
                      : `${usage.promo_codes.discount_value}€`}
                  </td>
                  <td style={{ padding: 8, textAlign: 'center' }}>{usage.promo_codes.duration_type === 'lifetime' ? 'Réutilisable' : 'Usage unique'}</td>
                  <td style={{ padding: 8, textAlign: 'center' }}>{usage.applied_at ? formatDateToFrench(usage.applied_at) : '-'}</td>
                  <td style={{ padding: 8, textAlign: 'center' }}>{usage.promo_codes.expires_at ? formatDateToFrench(usage.promo_codes.expires_at) : 'Jamais'}</td>
                </tr>
              ))}
            </tbody>
          </table>
        </Box>
      )}
    </Box>
  );

  // Fonction pour réactiver le renouvellement automatique (à placer dans le composant principal, pas dans le render)
  const handleReactivateAutoRenew = async () => {
    setLoading(true);
    try {
      const response = await subscriptionService.reactivateAutoRenew();
      if (response.success) {
        notify(response.message, 'success');
        await fetchSubscriptionData();
      } else {
        notify(response.message || 'Erreur lors de la réactivation', 'error');
      }
    } catch (error) {
      notify('Erreur réseau ou serveur', 'error');
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" sx={{ py: 6, minHeight: 400 }}>
        <Stack alignItems="center" spacing={2}>
          <LinearProgress
            color="warning"
            sx={{
              width: 200,
              height: 8,
              borderRadius: 4,
              '& .MuiLinearProgress-bar': {
                bgcolor: '#FF7A35'
              }
            }}
          />
          <Typography variant="body1" color="text.secondary">
            Chargement de vos informations d'abonnement...
          </Typography>
        </Stack>
      </Box>
    );
  }

  return (
    <Box sx={{ px: { xs: 2, md: 0 }, width: '100%' }}>
      <Box sx={{ maxWidth: '100%', px: 0 }}>
        <Box sx={{ mb: 4 }}>
          <motion.div
            initial={{ opacity: 0, y: -20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
          >
            <PageTitle variant="h1">
              Mon abonnement Premium
            </PageTitle>
          </motion.div>
        </Box>

        {/* Alert pour l'offre de lancement */}
        {subscriptionStatus?.plan !== 'premium' && (
        <Box sx={{ mb: 2 }}>
        <Alert
          icon={false}
          severity="info"
          sx={{
            bgcolor: '#FFF8F3',
            color: '#FF7A35',
            border: '1.5px solid #FF7A35',
            fontWeight: 'bold',
            fontSize: '1.1rem',
            borderRadius: 2,
            boxShadow: '0 2px 8px rgba(255, 122, 53, 0.08)',
            textAlign: 'center',
            py: 2,
            mb: 2
          }}
        >
          🎉 <b>OFFRE DE LANCEMENT :</b> <span style={{ color: '#E16B28' }}>50% de réduction à vie</span> sur tous les abonnements pour les <b>500 premiers abonnés premium</b> avec le code <span style={{ color: '#E16B28', fontWeight: 700 }}>FIZSZQMW</span>
        </Alert>
      </Box>
      )}

        {/* Navigation par onglets */}
        <Stack
          direction={isTablet ? "column" : "row"}
          spacing={2}
          sx={{ mb: 4 }}
        >
          <motion.div whileHover={{ y: -3 }} whileTap={{ scale: 0.97 }}>
            <Box
              onClick={() => handleTabChange('current')}
              sx={{
                ...tabStyle(activeTab === 'current'),
                display: 'flex',
                alignItems: 'center',
                gap: 1
              }}
            >
              <CreditCardIcon sx={{ fontSize: 20 }} />
              Mon abonnement actuel
            </Box>
          </motion.div>
          <motion.div whileHover={{ y: -3 }} whileTap={{ scale: 0.97 }}>
            <Box
              onClick={() => handleTabChange('pricing')}
              sx={{
                ...tabStyle(activeTab === 'pricing'),
                display: 'flex',
                alignItems: 'center',
                gap: 1
              }}
            >
              <StarIcon sx={{ fontSize: 20 }} />
              Nos plans
            </Box>
          </motion.div>
          <motion.div whileHover={{ y: -3 }} whileTap={{ scale: 0.97 }}>
            <Box
              onClick={() => handleTabChange('customize')}
              sx={{
                ...tabStyle(activeTab === 'customize'),
                display: 'flex',
                alignItems: 'center',
                gap: 1
              }}
            >
              <SettingsIcon sx={{ fontSize: 20 }} />
              Personnaliser mon plan
            </Box>
          </motion.div>
        </Stack>
        {/* Contenu en fonction de l'onglet actif */}
        {activeTab === 'current' && renderCurrentSubscription()}
        {activeTab === 'pricing' && renderPricingPlans()}
        {activeTab === 'customize' && renderCustomizeOptions()}
      </Box>
      {/* Ajout du dialogue de sélection des services */}
      {renderServicesSelectionDialog()}

      {/* Boîte de dialogue de confirmation de passage au plan gratuit */}
      <ModalPortal
        isOpen={showGratuitConfirmDialog}
        onBackdropClick={handleCloseGratuitConfirmDialog}
      >
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          exit={{ opacity: 0, y: -20 }}
          transition={{ duration: 0.3 }}
        >
          <Paper
            sx={{
              borderRadius: 3,
              p: 0,
              boxShadow: '0 8px 30px rgba(0, 0, 0, 0.12)',
              width: '100%',
              maxWidth: { xs: '98vw', sm: '95vw', md: '600px' },
              minWidth: { xs: '0', sm: '350px' },
              m: { xs: 0, sm: 'auto' },
              overflow: 'hidden',
              display: 'flex',
              flexDirection: 'column',
              maxHeight: { xs: '90vh', sm: '90vh', md: 'none' },
              overflowY: { xs: 'auto', sm: 'auto', md: 'visible' },
            }}
          >
            <Box
              sx={{
                fontWeight: 'bold',
                p: 3,
                pb: 2,
                borderBottom: '1px solid',
                borderColor: 'divider',
                fontSize: '1.5rem',
                color: 'error.main'
              }}
            >
              Confirmation de changement de plan
            </Box>
            <Box sx={{ p: 3, pt: 2 }}>
              <Alert severity="warning" sx={{ mb: 3 }}>
                Attention, vous êtes sur le point de passer du plan Premium au plan Gratuit.
              </Alert>
              <Typography variant="body1" sx={{ mb: 2 }}>
                En confirmant ce changement :
              </Typography>
              <Box component="ul" sx={{ pl: 2, mb: 3 }}>
                <Typography component="li" variant="body1" sx={{ mb: 1 }}>
                  Votre abonnement Premium restera actif jusqu'à la date d'expiration : <b>{subscriptionStatus?.endDate ? formatDateToFrench(subscriptionStatus.endDate) : "fin de la période en cours"}</b>
                </Typography>
                <Typography component="li" variant="body1" sx={{ mb: 1 }}>
                  Le renouvellement automatique sera désactivé
                </Typography>
                <Typography component="li" variant="body1" sx={{ mb: 1 }}>
                  Après cette date, vous passerez automatiquement au plan Gratuit
                </Typography>
                <Typography component="li" variant="body1" sx={{ mb: 1 }}>
                  Les fonctionnalités Premium ne seront plus disponibles après la fin de votre abonnement actuel
                </Typography>
              </Box>
              <Typography variant="body1" fontWeight="medium" sx={{ mb: 1 }}>
                Merci de nous indiquer la raison de votre résiliation (optionnel) :
              </Typography>
              <TextField
                autoFocus
                margin="dense"
                label="Raison de la résiliation"
                type="text"
                fullWidth
                variant="outlined"
                value={cancelReason}
                onChange={e => setCancelReason(e.target.value)}
                sx={{ mb: 2 }}
              />
              <Typography variant="body1" fontWeight="medium" sx={{ mb: 1 }}>
                Êtes-vous sûr de vouloir continuer ?
              </Typography>
            </Box>
            <Box sx={{ px: 3, py: 2, borderTop: '1px solid', borderColor: 'divider' }}>
              <Stack direction={{ xs: 'column', sm: 'row' }} spacing={2} justifyContent="flex-end" alignItems={{ xs: 'stretch', sm: 'center' }}>
                <Button
                  onClick={handleCloseGratuitConfirmDialog}
                  variant="outlined"
                  color="inherit"
                  sx={{ borderRadius: 2, width: { xs: '100%', sm: 'auto' } }}
                >
                  Annuler
                </Button>
                <Button
                  onClick={handleSubscribeGratuit}
                  variant="contained"
                  color="error"
                  sx={{ borderRadius: 2, width: { xs: '100%', sm: 'auto' } }}
                >
                  Confirmer le changement
                </Button>
              </Stack>
            </Box>
          </Paper>
        </motion.div>
      </ModalPortal>

      {/* Boîte de dialogue de confirmation de mise à jour de l'abonnement */}
      <ModalPortal
        isOpen={showUpdateConfirmDialog}
        onBackdropClick={handleCloseUpdateConfirmDialog}
      >
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          exit={{ opacity: 0, y: -20 }}
          transition={{ duration: 0.3 }}
        >
          <Paper
            sx={{
              borderRadius: 3,
              p: 0,
              boxShadow: '0 8px 30px rgba(0, 0, 0, 0.12)',
              width: '100%',
              maxWidth: { xs: '98vw', sm: '95vw', md: '600px' },
              minWidth: { xs: '0', sm: '350px' },
              m: { xs: 0, sm: 'auto' },
              overflow: 'hidden',
              display: 'flex',
              flexDirection: 'column',
              maxHeight: { xs: '90vh', sm: '90vh', md: 'none' },
              overflowY: { xs: 'auto', sm: 'auto', md: 'visible' },
            }}
          >
            <Box
              sx={{
                fontWeight: 'bold',
                p: 3,
                pb: 2,
                borderBottom: '1px solid',
                borderColor: 'divider',
                fontSize: '1.5rem',
                color: '#FF7A35',
                display: 'flex',
                alignItems: 'center',
                gap: 2
              }}
            >
              <DiamondIcon sx={{ fontSize: 28 }} />
              Confirmation de mise à jour
            </Box>
            <Box sx={{ p: 3, pt: 2 }}>
              <Alert severity="info" sx={{ mb: 3 }}>
                Vous êtes sur le point de mettre à jour votre abonnement Premium.
              </Alert>
              <Typography variant="body1" sx={{ mb: 2 }}>
                Détails de votre mise à jour :
              </Typography>
              <Box sx={{
                p: 3,
                mb: 3,
                bgcolor: '#FFF8F3',
                borderRadius: 2,
                border: '1px solid',
                borderColor: alpha('#FF7A35', 0.3)
              }}>
                <Stack spacing={1.2} alignItems="flex-start">
                  {discountedPrice !== null && promoCodeValid ? (
                    <>
                      <Typography
                        variant="h5"
                        component="div"
                        sx={{
                          textDecoration: 'line-through',
                          color: 'text.secondary',
                          fontSize: '1.3rem'
                        }}
                      >
                        {totalPrice}€ <Typography variant="body1" component="span" sx={{ fontSize: '0.85rem' }}>/mois</Typography>
                      </Typography>
                      <Typography
                        variant="h5"
                        component="div"
                        sx={{ fontWeight: 'bold', color: '#FF7A35' }}
                      >
                        {discountedPrice}€ <Typography variant="body1" component="span" sx={{ fontSize: '0.9rem' }}>/mois</Typography>
                      </Typography>
                    </>
                  ) : (
                    <Typography variant="h5" component="div" sx={{ fontWeight: 'bold', color: '#FF7A35' }}>
                      {totalPrice}€ <Typography variant="body1" component="span" sx={{ fontSize: '0.9rem' }}>/mois</Typography>
                    </Typography>
                  )}
                  <Typography variant="body2">
                    Prix total incluant toutes les options personnalisées
                    {promoCodeValid && promoCodeDetails && (
                      <Typography component="span" color="success.main" sx={{ display: 'block', mt: 1, fontWeight: 'medium' }}>
                        Code promo appliqué : {promoCodeDetails.code} ({promoCodeDetails.discount_type === 'percentage' ?
                          `${promoCodeDetails.discount_value}% de réduction` :
                          `${promoCodeDetails.discount_value}€ de réduction`})
                      </Typography>
                    )}
                  </Typography>
                </Stack>
              </Box>
              <Typography variant="body1" fontWeight="medium" sx={{ mb: 3 }}>
                Êtes-vous sûr de vouloir procéder à cette mise à jour ?
              </Typography>

              {/* Champ de mot de passe pour la sécurité */}
              <Alert severity="warning" sx={{ mb: 3 }}>
                <Typography variant="body2" fontWeight="medium">
                  Pour votre sécurité, veuillez confirmer votre mot de passe
                </Typography>
              </Alert>

              <TextField
                fullWidth
                type="password"
                label="Mot de passe"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                error={!!passwordError}
                helperText={passwordError}
                disabled={verifyingPassword}
                sx={{ mb: 2 }}
                slotProps={{
                  input: {
                    startAdornment: <LockIcon sx={{ mr: 1, color: 'action.active' }} />
                  }
                }}
              />
            </Box>
            <Box sx={{ px: 3, py: 2, borderTop: '1px solid', borderColor: 'divider' }}>
              <Stack direction={{ xs: 'column', sm: 'row' }} spacing={2} justifyContent="flex-end" alignItems={{ xs: 'stretch', sm: 'center' }}>
                <Button
                  onClick={handleCloseUpdateConfirmDialog}
                  variant="outlined"
                  color="inherit"
                  sx={{ borderRadius: 2, width: { xs: '100%', sm: 'auto' } }}
                >
                  Annuler
                </Button>
                <Button
                  onClick={confirmSubscribeUpdate}
                  variant="contained"
                  disabled={verifyingPassword || loading || !password.trim()}
                  sx={{
                    borderRadius: 2,
                    width: { xs: '100%', sm: 'auto' },
                    bgcolor: '#FF7A35',
                    '&:hover': { bgcolor: '#E16B28' }
                  }}
                >
                  {verifyingPassword ? 'Vérification...' : loading ? 'Mise à jour...' : 'Confirmer la mise à jour'}
                </Button>
              </Stack>
            </Box>
          </Paper>
        </motion.div>
      </ModalPortal>
    </Box>
  );
};

export default PremiumPage;