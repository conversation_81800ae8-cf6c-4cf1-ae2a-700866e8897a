import { fetchCsrfToken } from '../services/csrf';
import { getCommonHeaders } from './headers';
import { API_CONFIG } from '../config/api';

/**
 * Vérifie le mot de passe de l'utilisateur connecté
 * @param password - Le mot de passe à vérifier
 * @returns Promise<boolean> - true si le mot de passe est correct, false sinon
 */
export const verifyUserPassword = async (password: string): Promise<{ success: boolean; message?: string }> => {
  try {
    if (!password.trim()) {
      return { success: false, message: 'Mot de passe requis' };
    }

    await fetchCsrfToken();
    const headers = await getCommonHeaders();

    const response = await fetch(`${API_CONFIG.baseURL}/api/users/verify-password`, {
      method: 'POST',
      headers: {
        ...headers,
        'Content-Type': 'application/json',
      },
      credentials: 'include',
      body: JSON.stringify({ password }),
    });

    const data = await response.json();

    if (!data.success) {
      return { success: false, message: data.message || 'Mot de passe incorrect' };
    }

    return { success: true };
  } catch (error) {
    console.error('Erreur lors de la vérification du mot de passe:', error);
    return { success: false, message: 'Erreur lors de la vérification du mot de passe' };
  }
};
