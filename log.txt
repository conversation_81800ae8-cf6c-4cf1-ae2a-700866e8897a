info: Assistant Mission IA : <PERSON><PERSON>but de génération pour l'utilisateur: {"timestamp":"2025-09-01 16:16:52"}
info: Crédits IA récupérés depuis le cache pour l'utilisateur 58687861-4c81-4d9e-b240-9d407a83fdf7: 52 {"timestamp":"2025-09-01 16:16:52"}
info: Assistant Mission IA : Lancement de la génération avec le prompt :
      Tu es un assistant pour la création de mission sur JobPartiel, une plateforme de services entre particuliers.

      Date et heure actuelles: lundi 1 septembre 2025 à 16:16

      L'utilisateur a décrit son besoin: "Changement disque de frein renault clio 3"

      Informations sur l'utilisateur:
      - Nom: Maxime Choinet
      - Ville: Pia
      - Code postal: 66380
      - Adresse: Rue de l’alzine

      Données déjà collectées:
      {"is_urgent":false}

      Étape actuelle: Description du besoin

      Pour cette étape, tu dois analyser le besoin de l'utilisateur et générer les informations appropriées au format JSON.


        Tu es un expert en rédaction de descriptions de missions pour JobPartiel. Ta tâche est de créer une description professionnelle, détaillée et attrayante qui aidera l'utilisateur à trouver rapidement un prestataire qualifié.

        INSTRUCTIONS DÉTAILLÉES:
        1. Génère un titre court (max 70 caractères) qui soit accrocheur, précis et qui mette en avant le service principal demandé
        2. Crée une description détaillée (max 1200 caractères) qui:
           - Commence par une phrase d'accroche qui résume clairement le besoin
           - Détaille précisément les tâches à accomplir avec des paragraphes bien structurés
           - Inclut toutes les spécifications techniques pertinentes (dimensions, matériaux, équipements, etc.)
           - Mentionne les compétences ou qualifications requises pour le prestataire
           - Précise le niveau de qualité attendu et les délais si mentionnés
           - Utilise un ton professionnel mais accessible
           - Évite tout langage vague ou générique
        3. Détermine si la mission est urgente en analysant minutieusement le langage utilisé

        Règles pour is_urgent:
        - Si l'utilisateur utilise des mots comme "urgent", "rapidement", "dès que possible", "aujourd'hui", "immédiatement", mets is_urgent à true
        - Si l'utilisateur mentionne une date proche (aujourd'hui ou demain) avec un ton pressant, mets is_urgent à true
        - Si l'utilisateur a explicitement coché la case "Mission urgente" dans l'interface (non), utilise cette valeur
        - Sinon, mets is_urgent à false

        RÈGLES DE CONFIDENTIALITÉ:
        - Ne jamais inclure d'adresse complète dans la description
        - Ne jamais inclure de numéro de téléphone dans la description
        - Si l'utilisateur mentionne une adresse, utilise uniquement la ville (ex: "à Perpignan")
        - Concentre-toi sur la description du service demandé, pas sur les informations personnelles

        CONSERVATION DES INFORMATIONS IMPORTANTES:
        - Si l'utilisateur mentionne un budget précis, inclus-le OBLIGATOIREMENT dans la description (ex: "Budget de 40€ pour 6 heures")
        - Si l'utilisateur mentionne une durée, inclus-la OBLIGATOIREMENT dans la description
        - Si l'utilisateur mentionne des dates ou horaires spécifiques, inclus-les OBLIGATOIREMENT dans la description
        - Si l'utilisateur mentionne des matériaux, outils ou équipements spécifiques, inclus-les OBLIGATOIREMENT dans la description
        - Si l'utilisateur mentionne des compétences ou qualifications requises, inclus-les OBLIGATOIREMENT dans la description
        - Conserve toutes les informations importantes fournies par l'utilisateur dans ta description afin de générer une description la plus précise possible de son besoin

        INTERDICTION ABSOLUE DE TOUT COMMENTAIRE SUR L'ABSENCE D'INFORMATION:
        - Si une information (budget, durée, etc.) n'est pas connue, ne commente pas l'absence d'information.

        EXEMPLES DE TITRES EFFICACES:
        - "Création site web avec système de réservation pour restaurant"
        - "Tonte de pelouse et taille de haies sur terrain de 500m²"
        - "Garde de chat affectueux pendant 2 semaines en août"

        EXEMPLES DE DESCRIPTIONS EFFICACES:
        - "Je recherche un développeur web expérimenté pour créer un site vitrine avec système de réservation en ligne pour mon restaurant. Le site doit inclure: page d'accueil avec photos, menu interactif, système de réservation de table avec confirmation par email, page contact et formulaire. Design moderne et épuré souhaité, compatible mobile et tablette. Compétences requises: HTML/CSS, JavaScript, PHP ou équivalent. Le site doit être livré avec documentation et formation d'une heure pour la gestion du contenu. Budget de 800€ pour l'ensemble du projet."
        - "Besoin d'un jardinier qualifié pour entretenir mon jardin de 500m² à Perpignan. Les travaux comprennent: tonte complète de la pelouse, taille des 6 haies de cyprès (hauteur 2m), désherbage des massifs de fleurs et nettoyage complet après intervention. Le jardin n'a pas été entretenu depuis 2 mois. Matériel non fourni, merci de venir avec votre équipement. Intervention souhaitée le week-end prochain, durée estimée: 4-5 heures. Budget: 120€ pour l'ensemble des travaux."

        Réponds uniquement au format JSON suivant:
        {
          "titre": "Titre court et précis",
          "description": "Description détaillée du besoin",
          "is_urgent": true/false
        }












      Réponds UNIQUEMENT avec un objet JSON valide correspondant à l'étape demandée, sans aucun texte supplémentaire.
      Assure-toi que le JSON est correctement formaté et contient toutes les propriétés requises pour l'étape.
      N'ajoute pas de commentaires ou d'explications en dehors de l'objet JSON.
     {"timestamp":"2025-09-01 16:16:52"}
warn: Erreur 400 détectée, tentative avec le modèle payant. {"timestamp":"2025-09-01 16:16:55"}
error: Erreur lors de l'appel à l'API OpenRouter pour l'assistant mission: Request failed with status code 404 {"code":"ERR_BAD_REQUEST","config":{"adapter":["xhr","http","fetch"],"allowAbsoluteUrls":true,"data":"{\"model\":\"google/gemini-2.5-flash-preview\",\"messages\":[{\"role\":\"system\",\"content\":\"Tu es un assistant spécialisé dans la création de missions sur JobPartiel.\\n\\n            Tu dois répondre UNIQUEMENT au format JSON, sans aucun texte explicatif.\\n\\n            Pour l'étape \\\"Catégorie et service\\\", tu dois absolument utiliser les IDs exacts fournis dans les listes, sans les modifier.\\n            - Exemple correct: {\\\"category_id\\\": \\\"1\\\", \\\"subcategory_id\\\": \\\"1-1\\\"}\\n            - Exemple incorrect: {\\\"category_id\\\": \\\"ID de la catégorie\\\", \\\"subcategory_id\\\": \\\"ID de la sous-catégorie\\\"}\\n\\n            Pour l'étape \\\"Résumé\\\", tu dois générer un texte lisible en français qui résume toutes les informations de la mission:\\n            - Le résumé doit être un texte formaté, PAS un objet JSON échappé\\n            - N'inclus PAS les identifiants techniques (category_id, subcategory_id)\\n            - Utilise les noms des catégories et sous-catégories, pas leurs IDs\\n            - Présente les informations de manière claire et structurée\\n            - Exemple: {\\\"summary\\\": \\\"Vous avez créé une mission de jardinage pour tondre votre pelouse le samedi 10 mai à 14h. La mission se déroulera à votre domicile à Paris. Vous proposez un budget de 50€.\\\"}\\n\\n            Pour l'étape \\\"Budget\\\", respecte strictement ces règles:\\n            - Si l'utilisateur mentionne explicitement \\\"jobi\\\" ou \\\"échange\\\" UNIQUEMENT, utilise \\\"jobi\\\" comme méthode de paiement.\\n            - Si l'utilisateur mentionne explicitement \\\"euros\\\" ou \\\"€\\\" UNIQUEMENT, utilise \\\"euros\\\" comme méthode de paiement.\\n            - Si l'utilisateur mentionne les deux options OU indique qu'il accepte les deux modes de paiement, utilise \\\"both\\\".\\n            - Si l'utilisateur utilise des termes comme \\\"au choix\\\", \\\"peu importe\\\", \\\"l'un ou l'autre\\\", utilise \\\"both\\\".\\n            - Si la description contient \\\"paiement en jobi\\\" ou similaire, utilise \\\"jobi\\\".\\n            - Si aucune méthode n'est spécifiée, utilise \\\"euros\\\" par défaut.\\n\\n            Pour l'étape \\\"Description du besoin\\\", respecte strictement ces règles:\\n            - Ne jamais inclure d'adresse complète dans la description\\n            - Ne jamais inclure de numéro de téléphone dans la description\\n            - Si l'utilisateur mentionne une adresse, utilise uniquement la ville (ex: \\\"à Perpignan\\\")\\n\\n            Pour l'étape \\\"Localisation\\\", respecte strictement ces règles:\\n            - Analyse attentivement le message original de l'utilisateur pour y trouver une adresse\\n            - Si l'utilisateur a mentionné une adresse complète, utilise-la même si elle n'apparaît pas dans la description\\n            - Extrait le numéro, la rue, le code postal et la ville si ces informations sont présentes\\n\\n            Pour l'étape \\\"Horaires\\\", respecte strictement ces règles:\\n            - Utilise la date actuelle comme référence pour interpréter les expressions temporelles\\n            - Convertis les expressions comme \\\"la semaine prochaine\\\", \\\"vendredi prochain\\\" en dates précises\\n            - Fournis toujours des dates au format YYYY-MM-DD et des heures au format HH:MM\\n            - Si has_time_preference = true, le champ date_mission DOIT contenir la date du premier créneau horaire\\n            - Ne laisse JAMAIS le champ date_mission vide si has_time_preference = true\\n            - ATTENTION: Le \\\"week-end\\\" correspond STRICTEMENT au samedi et dimanche, JAMAIS au vendredi ou au lundi\\n            - Quand l'utilisateur mentionne \\\"ce week-end\\\", génère UNIQUEMENT des créneaux pour le samedi et/ou dimanche prochain\\n\\n            N'utilise jamais de texte descriptif à la place des IDs numériques.\"},{\"role\":\"user\",\"content\":\"\\n      Tu es un assistant pour la création de mission sur JobPartiel, une plateforme de services entre particuliers.\\n\\n      Date et heure actuelles: lundi 1 septembre 2025 à 16:16\\n\\n      L'utilisateur a décrit son besoin: \\\"Changement disque de frein renault clio 3\\\"\\n\\n      Informations sur l'utilisateur:\\n      - Nom: Maxime Choinet\\n      - Ville: Pia\\n      - Code postal: 66380\\n      - Adresse: Rue de l’alzine\\n\\n      Données déjà collectées:\\n      {\\\"is_urgent\\\":false}\\n\\n      Étape actuelle: Description du besoin\\n\\n      Pour cette étape, tu dois analyser le besoin de l'utilisateur et générer les informations appropriées au format JSON.\\n\\n      \\n        Tu es un expert en rédaction de descriptions de missions pour JobPartiel. Ta tâche est de créer une description professionnelle, détaillée et attrayante qui aidera l'utilisateur à trouver rapidement un prestataire qualifié.  \\n        \\n        INSTRUCTIONS DÉTAILLÉES:\\n        1. Génère un titre court (max 70 caractères) qui soit accrocheur, précis et qui mette en avant le service principal demandé\\n        2. Crée une description détaillée (max 1200 caractères) qui:\\n           - Commence par une phrase d'accroche qui résume clairement le besoin\\n           - Détaille précisément les tâches à accomplir avec des paragraphes bien structurés\\n           - Inclut toutes les spécifications techniques pertinentes (dimensions, matériaux, équipements, etc.)\\n           - Mentionne les compétences ou qualifications requises pour le prestataire\\n           - Précise le niveau de qualité attendu et les délais si mentionnés\\n           - Utilise un ton professionnel mais accessible\\n           - Évite tout langage vague ou générique\\n        3. Détermine si la mission est urgente en analysant minutieusement le langage utilisé\\n\\n        Règles pour is_urgent:\\n        - Si l'utilisateur utilise des mots comme \\\"urgent\\\", \\\"rapidement\\\", \\\"dès que possible\\\", \\\"aujourd'hui\\\", \\\"immédiatement\\\", mets is_urgent à true\\n        - Si l'utilisateur mentionne une date proche (aujourd'hui ou demain) avec un ton pressant, mets is_urgent à true\\n        - Si l'utilisateur a explicitement coché la case \\\"Mission urgente\\\" dans l'interface (non), utilise cette valeur\\n        - Sinon, mets is_urgent à false\\n\\n        RÈGLES DE CONFIDENTIALITÉ:\\n        - Ne jamais inclure d'adresse complète dans la description\\n        - Ne jamais inclure de numéro de téléphone dans la description\\n        - Si l'utilisateur mentionne une adresse, utilise uniquement la ville (ex: \\\"à Perpignan\\\")\\n        - Concentre-toi sur la description du service demandé, pas sur les informations personnelles\\n\\n        CONSERVATION DES INFORMATIONS IMPORTANTES:\\n        - Si l'utilisateur mentionne un budget précis, inclus-le OBLIGATOIREMENT dans la description (ex: \\\"Budget de 40€ pour 6 heures\\\")\\n        - Si l'utilisateur mentionne une durée, inclus-la OBLIGATOIREMENT dans la description\\n        - Si l'utilisateur mentionne des dates ou horaires spécifiques, inclus-les OBLIGATOIREMENT dans la description\\n        - Si l'utilisateur mentionne des matériaux, outils ou équipements spécifiques, inclus-les OBLIGATOIREMENT dans la description\\n        - Si l'utilisateur mentionne des compétences ou qualifications requises, inclus-les OBLIGATOIREMENT dans la description\\n        - Conserve toutes les informations importantes fournies par l'utilisateur dans ta description afin de générer une description la plus précise possible de son besoin\\n\\n        INTERDICTION ABSOLUE DE TOUT COMMENTAIRE SUR L'ABSENCE D'INFORMATION:\\n        - Si une information (budget, durée, etc.) n'est pas connue, ne commente pas l'absence d'information.\\n\\n        EXEMPLES DE TITRES EFFICACES:\\n        - \\\"Création site web avec système de réservation pour restaurant\\\"\\n        - \\\"Tonte de pelouse et taille de haies sur terrain de 500m²\\\"\\n        - \\\"Garde de chat affectueux pendant 2 semaines en août\\\"\\n\\n        EXEMPLES DE DESCRIPTIONS EFFICACES:\\n        - \\\"Je recherche un développeur web expérimenté pour créer un site vitrine avec système de réservation en ligne pour mon restaurant. Le site doit inclure: page d'accueil avec photos, menu interactif, système de réservation de table avec confirmation par email, page contact et formulaire. Design moderne et épuré souhaité, compatible mobile et tablette. Compétences requises: HTML/CSS, JavaScript, PHP ou équivalent. Le site doit être livré avec documentation et formation d'une heure pour la gestion du contenu. Budget de 800€ pour l'ensemble du projet.\\\"\\n        - \\\"Besoin d'un jardinier qualifié pour entretenir mon jardin de 500m² à Perpignan. Les travaux comprennent: tonte complète de la pelouse, taille des 6 haies de cyprès (hauteur 2m), désherbage des massifs de fleurs et nettoyage complet après intervention. Le jardin n'a pas été entretenu depuis 2 mois. Matériel non fourni, merci de venir avec votre équipement. Intervention souhaitée le week-end prochain, durée estimée: 4-5 heures. Budget: 120€ pour l'ensemble des travaux.\\\"\\n\\n        Réponds uniquement au format JSON suivant:\\n        {\\n          \\\"titre\\\": \\\"Titre court et précis\\\",\\n          \\\"description\\\": \\\"Description détaillée du besoin\\\",\\n          \\\"is_urgent\\\": true/false\\n        }\\n      \\n\\n      \\n\\n      \\n\\n      \\n\\n      \\n\\n      \\n\\n      Réponds UNIQUEMENT avec un objet JSON valide correspondant à l'étape demandée, sans aucun texte supplémentaire.\\n      Assure-toi que le JSON est correctement formaté et contient toutes les propriétés requises pour l'étape.\\n      N'ajoute pas de commentaires ou d'explications en dehors de l'objet JSON.\\n    \"}],\"temperature\":0.1,\"top_p\":0.35,\"frequency_penalty\":0,\"presence_penalty\":0,\"max_tokens\":1500,\"response_format\":{\"type\":\"json_object\"}}","env":{},"headers":{"Accept":"application/json, text/plain, */*","Accept-Encoding":"gzip, compress, deflate, br","Authorization":"Bearer 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","Content-Length":"9755","Content-Type":"application/json","HTTP-Referer":"https://jobpartiel.fr","User-Agent":"axios/1.11.0","X-Title":"JobPartiel Mission Assistant"},"maxBodyLength":-1,"maxContentLength":-1,"method":"post","signal":{},"timeout":15000,"transformRequest":[null],"transformResponse":[null],"transitional":{"clarifyTimeoutError":false,"forcedJSONParsing":true,"silentJSONParsing":true},"url":"https://openrouter.ai/api/v1/chat/completions","xsrfCookieName":"XSRF-TOKEN","xsrfHeaderName":"X-XSRF-TOKEN"},"name":"AxiosError","request":{"_closed":true,"_contentLength":"9755","_defaultKeepAlive":true,"_ended":true,"_events":{},"_eventsCount":7,"_hasBody":true,"_header":"POST /api/v1/chat/completions HTTP/1.1\r\nAccept: application/json, text/plain, */*\r\nContent-Type: application/json\r\nAuthorization: Bearer 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\r\nHTTP-Referer: https://jobpartiel.fr\r\nX-Title: JobPartiel Mission Assistant\r\nUser-Agent: axios/1.11.0\r\nContent-Length: 9755\r\nAccept-Encoding: gzip, compress, deflate, br\r\nHost: openrouter.ai\r\nConnection: keep-alive\r\n\r\n","_headerSent":true,"_keepAliveTimeout":0,"_last":false,"_redirectable":{"_currentRequest":"[Circular]","_currentUrl":"https://openrouter.ai/api/v1/chat/completions","_ended":true,"_ending":true,"_events":{"socket":[null,null]},"_eventsCount":3,"_options":{"agents":{},"beforeRedirects":{},"headers":{"Accept":"application/json, text/plain, */*","Accept-Encoding":"gzip, compress, deflate, br","Authorization":"Bearer 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","Content-Length":"9755","Content-Type":"application/json","HTTP-Referer":"https://jobpartiel.fr","User-Agent":"axios/1.11.0","X-Title":"JobPartiel Mission Assistant"},"hostname":"openrouter.ai","maxBodyLength":null,"maxRedirects":21,"method":"POST","nativeProtocols":{"http:":{"METHODS":["ACL","BIND","CHECKOUT","CONNECT","COPY","DELETE","GET","HEAD","LINK","LOCK","M-SEARCH","MERGE","MKACTIVITY","MKCALENDAR","MKCOL","MOVE","NOTIFY","OPTIONS","PATCH","POST","PROPFIND","PROPPATCH","PURGE","PUT","QUERY","REBIND","REPORT","SEARCH","SOURCE","SUBSCRIBE","TRACE","UNBIND","UNLINK","UNLOCK","UNSUBSCRIBE"],"STATUS_CODES":{"100":"Continue","101":"Switching Protocols","102":"Processing","103":"Early Hints","200":"OK","201":"Created","202":"Accepted","203":"Non-Authoritative Information","204":"No Content","205":"Reset Content","206":"Partial Content","207":"Multi-Status","208":"Already Reported","226":"IM Used","300":"Multiple Choices","301":"Moved Permanently","302":"Found","303":"See Other","304":"Not Modified","305":"Use Proxy","307":"Temporary Redirect","308":"Permanent Redirect","400":"Bad Request","401":"Unauthorized","402":"Payment Required","403":"Forbidden","404":"Not Found","405":"Method Not Allowed","406":"Not Acceptable","407":"Proxy Authentication Required","408":"Request Timeout","409":"Conflict","410":"Gone","411":"Length Required","412":"Precondition Failed","413":"Payload Too Large","414":"URI Too Long","415":"Unsupported Media Type","416":"Range Not Satisfiable","417":"Expectation Failed","418":"I'm a Teapot","421":"Misdirected Request","422":"Unprocessable Entity","423":"Locked","424":"Failed Dependency","425":"Too Early","426":"Upgrade Required","428":"Precondition Required","429":"Too Many Requests","431":"Request Header Fields Too Large","451":"Unavailable For Legal Reasons","500":"Internal Server Error","501":"Not Implemented","502":"Bad Gateway","503":"Service Unavailable","504":"Gateway Timeout","505":"HTTP Version Not Supported","506":"Variant Also Negotiates","507":"Insufficient Storage","508":"Loop Detected","509":"Bandwidth Limit Exceeded","510":"Not Extended","511":"Network Authentication Required"},"globalAgent":{"_events":{},"_eventsCount":2,"defaultPort":80,"freeSockets":{},"keepAlive":true,"keepAliveMsecs":1000,"maxFreeSockets":256,"maxSockets":null,"maxTotalSockets":null,"options":{"keepAlive":true,"noDelay":true,"path":null,"scheduling":"lifo","timeout":5000},"protocol":"http:","requests":{},"scheduling":"lifo","sockets":{},"totalSocketCount":0},"maxHeaderSize":16384},"https:":{"globalAgent":{"_events":{},"_eventsCount":2,"_sessionCache":{"list":["openrouter.ai:443:::::::::::::::::::::"],"map":{"openrouter.ai:443:::::::::::::::::::::":{"data":[48,130,5,14,2,1,1,2,2,3,4,4,2,19,2,4,32,13,228,229,61,45,5,45,139,98,124,130,234,200,153,210,194,125,158,15,117,2,177,112,193,167,108,166,203,60,157,33,37,4,48,185,115,34,255,94,179,0,131,69,53,76,108,68,205,182,183,244,175,80,154,30,241,113,52,1,4,78,74,209,164,110,164,41,237,242,39,229,51,221,25,102,232,148,87,162,254,49,99,161,6,2,4,104,181,170,215,162,4,2,2,28,32,163,130,3,174,48,130,3,170,48,130,3,80,160,3,2,1,2,2,16,20,164,8,48,247,211,4,94,17,104,186,54,234,8,85,73,48,10,6,8,42,134,72,206,61,4,3,2,48,59,49,11,48,9,6,3,85,4,6,19,2,85,83,49,30,48,28,6,3,85,4,10,19,21,71,111,111,103,108,101,32,84,114,117,115,116,32,83,101,114,118,105,99,101,115,49,12,48,10,6,3,85,4,3,19,3,87,69,49,48,30,23,13,50,53,48,56,51,49,49,51,48,51,48,51,90,23,13,50,53,49,49,50,57,49,52,48,50,53,48,90,48,24,49,22,48,20,6,3,85,4,3,19,13,111,112,101,110,114,111,117,116,101,114,46,97,105,48,89,48,19,6,7,42,134,72,206,61,2,1,6,8,42,134,72,206,61,3,1,7,3,66,0,4,158,108,172,200,253,33,93,69,124,84,149,116,35,222,91,188,174,161,118,65,156,50,200,234,42,211,23,244,247,3,213,86,119,69,140,155,201,4,151,132,162,199,166,0,38,35,127,53,176,205,22,156,32,154,46,228,205,217,50,199,41,212,186,126,163,130,2,87,48,130,2,83,48,14,6,3,85,29,15,1,1,255,4,4,3,2,7,128,48,19,6,3,85,29,37,4,12,48,10,6,8,43,6,1,5,5,7,3,1,48,12,6,3,85,29,19,1,1,255,4,2,48,0,48,29,6,3,85,29,14,4,22,4,20,100,215,24,244,23,157,116,124,207,176,3,139,4,97,230,161,97,108,196,53,48,31,6,3,85,29,35,4,24,48,22,128,20,144,119,146,53,103,196,255,168,204,169,230,123,217,128,121,123,204,147,249,56,48,94,6,8,43,6,1,5,5,7,1,1,4,82,48,80,48,39,6,8,43,6,1,5,5,7,48,1,134,27,104,116,116,112,58,47,47,111,46,112,107,105,46,103,111,111,103,47,115,47,119,101,49,47,70,75,81,48,37,6,8,43,6,1,5,5,7,48,2,134,25,104,116,116,112,58,47,47,105,46,112,107,105,46,103,111,111,103,47,119,101,49,46,99,114,116,48,41,6,3,85,29,17,4,34,48,32,130,13,111,112,101,110,114,111,117,116,101,114,46,97,105,130,15,42,46,111,112,101,110,114,111,117,116,101,114,46,97,105,48,19,6,3,85,29,32,4,12,48,10,48,8,6,6,103,129,12,1,2,1,48,54,6,3,85,29,31,4,47,48,45,48,43,160,41,160,39,134,37,104,116,116,112,58,47,47,99,46,112,107,105,46,103,111,111,103,47,119,101,49,47,116,51,76,74,98,90,105,66,116,115,85,46,99,114,108,48,130,1,4,6,10,43,6,1,4,1,214,121,2,4,2,4,129,245,4,129,242,0,240,0,118,0,204,251,15,106,133,113,9,101,254,149,155,83,206,233,178,124,34,233,133,92,13,151,141,182,169,126,84,192,254,76,13,176,0,0,1,153,0,112,80,67,0,0,4,3,0,71,48,69,2,32,121,184,184,57,232,35,2,187,100,39,108,69,216,29,25,253,12,243,182,210,115,115,179,178,236,113,240,32,75,44,64,67,2,33,0,228,225,138,150,219,152,70,138,130,97,180,56,8,148,190,173,16,168,130,177,39,29,41,150,50,130,215,85,160,52,79,252,0,118,0,221,220,202,52,149,215,225,22,5,231,149,50,250,199,159,248,61,28,80,223,219,0,58,20,18,118,10,44,172,187,200,42,0,0,1,153,0,112,80,59,0,0,4,3,0,71,48,69,2,33,0,199,60,19,22,171,236,28,33,39,147,146,188,224,190,15,153,218,67,189,137,92,179,148,15,148,26,119,163,36,195,33,127,2,32,86,216,161,230,149,74,8,220,231,206,55,91,189,154,242,81,10,23,167,139,248,72,38,170,160,44,192,232,125,55,242,247,48,10,6,8,42,134,72,206,61,4,3,2,3,72,0,48,69,2,32,10,7,42,199,142,8,70,26,245,232,39,242,55,5,154,227,254,31,100,93,170,44,173,243,100,55,107,58,194,2,111,14,2,33,0,225,109,202,110,142,66,218,156,250,181,10,23,116,213,84,61,238,218,17,24,39,26,242,159,146,154,64,85,125,217,59,102,164,2,4,0,166,15,4,13,111,112,101,110,114,111,117,116,101,114,46,97,105,169,5,2,3,0,253,32,170,129,195,4,129,192,162,24,94,85,68,73,251,75,176,138,224,179,156,118,155,6,122,10,201,131,35,146,100,30,37,53,71,144,109,220,254,8,191,149,248,24,17,38,32,16,220,255,76,193,246,236,83,79,84,116,216,45,32,32,241,84,125,174,244,250,174,157,141,5,109,61,56,92,29,202,153,159,28,238,66,156,62,183,137,86,128,31,239,214,34,107,241,85,127,46,67,122,126,15,175,248,54,24,129,150,158,4,214,69,212,153,146,254,187,29,143,249,109,26,209,184,238,247,85,149,195,177,212,63,64,78,211,121,3,78,77,250,9,243,44,190,218,138,112,163,161,14,115,57,69,183,47,35,94,239,198,85,86,161,24,126,213,196,164,44,188,111,226,46,168,173,159,66,16,158,32,52,30,219,82,67,100,82,178,96,243,86,242,120,187,25,122,149,20,197,179,4,174,6,2,4,0,254,227,171,179,3,2,1,29],"type":"Buffer"}}},"defaultPort":443,"freeSockets":{"openrouter.ai:443:::::::::::::::::::::":[{"_SNICallback":null,"_closeAfterHandlingError":false,"_controlReleased":true,"_events":{"close":[null,null,null],"timeout":[null,null]},"_eventsCount":9,"_hadError":false,"_host":"openrouter.ai","_httpMessage":null,"_newSessionPending":false,"_parent":null,"_pendingData":null,"_pendingEncoding":"","_readableState":{"awaitDrainWriters":null,"buffer":[],"bufferIndex":0,"highWaterMark":16384,"length":0,"pipes":[]},"_rejectUnauthorized":true,"_requestCert":true,"_secureEstablished":true,"_securePending":false,"_server":null,"_sockname":null,"_tlsOptions":{"isServer":false,"pipe":false,"rejectUnauthorized":true,"requestCert":true,"secureContext":{"context":{}},"session":{"data":[48,130,5,15,2,1,1,2,2,3,4,4,2,19,2,4,32,209,105,134,79,242,244,131,170,85,204,88,109,214,222,84,36,197,38,157,159,88,200,238,190,73,116,3,141,119,216,191,122,4,48,147,30,244,78,115,132,41,207,31,152,23,195,47,88,252,97,136,42,11,132,240,32,179,249,163,65,129,142,220,47,80,167,29,131,221,206,224,47,182,7,92,98,82,11,38,77,150,21,161,6,2,4,104,181,170,17,162,4,2,2,28,32,163,130,3,174,48,130,3,170,48,130,3,80,160,3,2,1,2,2,16,20,164,8,48,247,211,4,94,17,104,186,54,234,8,85,73,48,10,6,8,42,134,72,206,61,4,3,2,48,59,49,11,48,9,6,3,85,4,6,19,2,85,83,49,30,48,28,6,3,85,4,10,19,21,71,111,111,103,108,101,32,84,114,117,115,116,32,83,101,114,118,105,99,101,115,49,12,48,10,6,3,85,4,3,19,3,87,69,49,48,30,23,13,50,53,48,56,51,49,49,51,48,51,48,51,90,23,13,50,53,49,49,50,57,49,52,48,50,53,48,90,48,24,49,22,48,20,6,3,85,4,3,19,13,111,112,101,110,114,111,117,116,101,114,46,97,105,48,89,48,19,6,7,42,134,72,206,61,2,1,6,8,42,134,72,206,61,3,1,7,3,66,0,4,158,108,172,200,253,33,93,69,124,84,149,116,35,222,91,188,174,161,118,65,156,50,200,234,42,211,23,244,247,3,213,86,119,69,140,155,201,4,151,132,162,199,166,0,38,35,127,53,176,205,22,156,32,154,46,228,205,217,50,199,41,212,186,126,163,130,2,87,48,130,2,83,48,14,6,3,85,29,15,1,1,255,4,4,3,2,7,128,48,19,6,3,85,29,37,4,12,48,10,6,8,43,6,1,5,5,7,3,1,48,12,6,3,85,29,19,1,1,255,4,2,48,0,48,29,6,3,85,29,14,4,22,4,20,100,215,24,244,23,157,116,124,207,176,3,139,4,97,230,161,97,108,196,53,48,31,6,3,85,29,35,4,24,48,22,128,20,144,119,146,53,103,196,255,168,204,169,230,123,217,128,121,123,204,147,249,56,48,94,6,8,43,6,1,5,5,7,1,1,4,82,48,80,48,39,6,8,43,6,1,5,5,7,48,1,134,27,104,116,116,112,58,47,47,111,46,112,107,105,46,103,111,111,103,47,115,47,119,101,49,47,70,75,81,48,37,6,8,43,6,1,5,5,7,48,2,134,25,104,116,116,112,58,47,47,105,46,112,107,105,46,103,111,111,103,47,119,101,49,46,99,114,116,48,41,6,3,85,29,17,4,34,48,32,130,13,111,112,101,110,114,111,117,116,101,114,46,97,105,130,15,42,46,111,112,101,110,114,111,117,116,101,114,46,97,105,48,19,6,3,85,29,32,4,12,48,10,48,8,6,6,103,129,12,1,2,1,48,54,6,3,85,29,31,4,47,48,45,48,43,160,41,160,39,134,37,104,116,116,112,58,47,47,99,46,112,107,105,46,103,111,111,103,47,119,101,49,47,116,51,76,74,98,90,105,66,116,115,85,46,99,114,108,48,130,1,4,6,10,43,6,1,4,1,214,121,2,4,2,4,129,245,4,129,242,0,240,0,118,0,204,251,15,106,133,113,9,101,254,149,155,83,206,233,178,124,34,233,133,92,13,151,141,182,169,126,84,192,254,76,13,176,0,0,1,153,0,112,80,67,0,0,4,3,0,71,48,69,2,32,121,184,184,57,232,35,2,187,100,39,108,69,216,29,25,253,12,243,182,210,115,115,179,178,236,113,240,32,75,44,64,67,2,33,0,228,225,138,150,219,152,70,138,130,97,180,56,8,148,190,173,16,168,130,177,39,29,41,150,50,130,215,85,160,52,79,252,0,118,0,221,220,202,52,149,215,225,22,5,231,149,50,250,199,159,248,61,28,80,223,219,0,58,20,18,118,10,44,172,187,200,42,0,0,1,153,0,112,80,59,0,0,4,3,0,71,48,69,2,33,0,199,60,19,22,171,236,28,33,39,147,146,188,224,190,15,153,218,67,189,137,92,179,148,15,148,26,119,163,36,195,33,127,2,32,86,216,161,230,149,74,8,220,231,206,55,91,189,154,242,81,10,23,167,139,248,72,38,170,160,44,192,232,125,55,242,247,48,10,6,8,42,134,72,206,61,4,3,2,3,72,0,48,69,2,32,10,7,42,199,142,8,70,26,245,232,39,242,55,5,154,227,254,31,100,93,170,44,173,243,100,55,107,58,194,2,111,14,2,33,0,225,109,202,110,142,66,218,156,250,181,10,23,116,213,84,61,238,218,17,24,39,26,242,159,146,154,64,85,125,217,59,102,164,2,4,0,166,15,4,13,111,112,101,110,114,111,117,116,101,114,46,97,105,169,5,2,3,0,253,32,170,129,195,4,129,192,162,24,94,85,68,73,251,75,176,138,224,179,156,118,155,6,220,3,11,149,236,218,100,138,120,103,4,76,189,133,252,199,27,144,54,126,66,181,184,65,95,138,232,166,116,239,113,115,10,145,82,66,121,139,14,127,243,75,254,6,102,243,59,161,19,232,24,192,162,43,116,232,172,76,65,53,31,130,119,42,97,76,135,49,79,16,175,79,126,149,131,36,109,67,224,8,40,33,87,180,169,133,146,134,112,189,156,115,216,242,89,232,140,96,48,127,112,159,129,213,223,195,75,54,183,201,15,214,199,27,83,114,156,80,28,29,12,235,202,167,89,86,178,207,228,151,66,91,198,84,232,28,245,208,122,118,219,84,138,140,27,175,194,13,240,226,240,196,15,207,179,135,41,165,16,11,174,125,65,152,65,236,9,68,205,64,101,121,36,247,36,151,174,7,2,5,0,246,126,188,8,179,3,2,1,29],"type":"Buffer"}},"_writableState":{"bufferedIndex":0,"corked":0,"highWaterMark":16384,"length":0,"pendingcb":0,"writelen":0},"allowHalfOpen":false,"alpnProtocol":false,"authorizationError":null,"authorized":true,"autoSelectFamilyAttemptedAddresses":["************:443"],"connecting":false,"encrypted":true,"parser":null,"secureConnecting":false,"servername":"openrouter.ai","ssl":{"_parent":{"onconnection":null,"reading":true},"_parentWrap":null,"_secureContext":{"context":{}},"reading":true},"timeout":5000}]},"keepAlive":true,"keepAliveMsecs":1000,"maxCachedSessions":100,"maxFreeSockets":256,"maxSockets":null,"maxTotalSockets":null,"options":{"keepAlive":true,"noDelay":true,"path":null,"scheduling":"lifo","timeout":5000},"protocol":"https:","requests":{},"scheduling":"lifo","sockets":{},"totalSocketCount":1}}},"path":"/api/v1/chat/completions","pathname":"/api/v1/chat/completions","port":"","protocol":"https:"},"_redirectCount":0,"_redirects":[],"_requestBodyBuffers":[],"_requestBodyLength":9755,"_timeout":null,"_writableState":{"bufferedIndex":0,"corked":0,"highWaterMark":16384,"length":0,"pendingcb":0,"writelen":0}},"_removedConnection":false,"_removedContLen":false,"_removedTE":false,"_trailer":"","aborted":false,"agent":{"_events":{},"_eventsCount":2,"_sessionCache":{"list":["openrouter.ai:443:::::::::::::::::::::"],"map":{"openrouter.ai:443:::::::::::::::::::::":{"data":[48,130,5,14,2,1,1,2,2,3,4,4,2,19,2,4,32,13,228,229,61,45,5,45,139,98,124,130,234,200,153,210,194,125,158,15,117,2,177,112,193,167,108,166,203,60,157,33,37,4,48,185,115,34,255,94,179,0,131,69,53,76,108,68,205,182,183,244,175,80,154,30,241,113,52,1,4,78,74,209,164,110,164,41,237,242,39,229,51,221,25,102,232,148,87,162,254,49,99,161,6,2,4,104,181,170,215,162,4,2,2,28,32,163,130,3,174,48,130,3,170,48,130,3,80,160,3,2,1,2,2,16,20,164,8,48,247,211,4,94,17,104,186,54,234,8,85,73,48,10,6,8,42,134,72,206,61,4,3,2,48,59,49,11,48,9,6,3,85,4,6,19,2,85,83,49,30,48,28,6,3,85,4,10,19,21,71,111,111,103,108,101,32,84,114,117,115,116,32,83,101,114,118,105,99,101,115,49,12,48,10,6,3,85,4,3,19,3,87,69,49,48,30,23,13,50,53,48,56,51,49,49,51,48,51,48,51,90,23,13,50,53,49,49,50,57,49,52,48,50,53,48,90,48,24,49,22,48,20,6,3,85,4,3,19,13,111,112,101,110,114,111,117,116,101,114,46,97,105,48,89,48,19,6,7,42,134,72,206,61,2,1,6,8,42,134,72,206,61,3,1,7,3,66,0,4,158,108,172,200,253,33,93,69,124,84,149,116,35,222,91,188,174,161,118,65,156,50,200,234,42,211,23,244,247,3,213,86,119,69,140,155,201,4,151,132,162,199,166,0,38,35,127,53,176,205,22,156,32,154,46,228,205,217,50,199,41,212,186,126,163,130,2,87,48,130,2,83,48,14,6,3,85,29,15,1,1,255,4,4,3,2,7,128,48,19,6,3,85,29,37,4,12,48,10,6,8,43,6,1,5,5,7,3,1,48,12,6,3,85,29,19,1,1,255,4,2,48,0,48,29,6,3,85,29,14,4,22,4,20,100,215,24,244,23,157,116,124,207,176,3,139,4,97,230,161,97,108,196,53,48,31,6,3,85,29,35,4,24,48,22,128,20,144,119,146,53,103,196,255,168,204,169,230,123,217,128,121,123,204,147,249,56,48,94,6,8,43,6,1,5,5,7,1,1,4,82,48,80,48,39,6,8,43,6,1,5,5,7,48,1,134,27,104,116,116,112,58,47,47,111,46,112,107,105,46,103,111,111,103,47,115,47,119,101,49,47,70,75,81,48,37,6,8,43,6,1,5,5,7,48,2,134,25,104,116,116,112,58,47,47,105,46,112,107,105,46,103,111,111,103,47,119,101,49,46,99,114,116,48,41,6,3,85,29,17,4,34,48,32,130,13,111,112,101,110,114,111,117,116,101,114,46,97,105,130,15,42,46,111,112,101,110,114,111,117,116,101,114,46,97,105,48,19,6,3,85,29,32,4,12,48,10,48,8,6,6,103,129,12,1,2,1,48,54,6,3,85,29,31,4,47,48,45,48,43,160,41,160,39,134,37,104,116,116,112,58,47,47,99,46,112,107,105,46,103,111,111,103,47,119,101,49,47,116,51,76,74,98,90,105,66,116,115,85,46,99,114,108,48,130,1,4,6,10,43,6,1,4,1,214,121,2,4,2,4,129,245,4,129,242,0,240,0,118,0,204,251,15,106,133,113,9,101,254,149,155,83,206,233,178,124,34,233,133,92,13,151,141,182,169,126,84,192,254,76,13,176,0,0,1,153,0,112,80,67,0,0,4,3,0,71,48,69,2,32,121,184,184,57,232,35,2,187,100,39,108,69,216,29,25,253,12,243,182,210,115,115,179,178,236,113,240,32,75,44,64,67,2,33,0,228,225,138,150,219,152,70,138,130,97,180,56,8,148,190,173,16,168,130,177,39,29,41,150,50,130,215,85,160,52,79,252,0,118,0,221,220,202,52,149,215,225,22,5,231,149,50,250,199,159,248,61,28,80,223,219,0,58,20,18,118,10,44,172,187,200,42,0,0,1,153,0,112,80,59,0,0,4,3,0,71,48,69,2,33,0,199,60,19,22,171,236,28,33,39,147,146,188,224,190,15,153,218,67,189,137,92,179,148,15,148,26,119,163,36,195,33,127,2,32,86,216,161,230,149,74,8,220,231,206,55,91,189,154,242,81,10,23,167,139,248,72,38,170,160,44,192,232,125,55,242,247,48,10,6,8,42,134,72,206,61,4,3,2,3,72,0,48,69,2,32,10,7,42,199,142,8,70,26,245,232,39,242,55,5,154,227,254,31,100,93,170,44,173,243,100,55,107,58,194,2,111,14,2,33,0,225,109,202,110,142,66,218,156,250,181,10,23,116,213,84,61,238,218,17,24,39,26,242,159,146,154,64,85,125,217,59,102,164,2,4,0,166,15,4,13,111,112,101,110,114,111,117,116,101,114,46,97,105,169,5,2,3,0,253,32,170,129,195,4,129,192,162,24,94,85,68,73,251,75,176,138,224,179,156,118,155,6,122,10,201,131,35,146,100,30,37,53,71,144,109,220,254,8,191,149,248,24,17,38,32,16,220,255,76,193,246,236,83,79,84,116,216,45,32,32,241,84,125,174,244,250,174,157,141,5,109,61,56,92,29,202,153,159,28,238,66,156,62,183,137,86,128,31,239,214,34,107,241,85,127,46,67,122,126,15,175,248,54,24,129,150,158,4,214,69,212,153,146,254,187,29,143,249,109,26,209,184,238,247,85,149,195,177,212,63,64,78,211,121,3,78,77,250,9,243,44,190,218,138,112,163,161,14,115,57,69,183,47,35,94,239,198,85,86,161,24,126,213,196,164,44,188,111,226,46,168,173,159,66,16,158,32,52,30,219,82,67,100,82,178,96,243,86,242,120,187,25,122,149,20,197,179,4,174,6,2,4,0,254,227,171,179,3,2,1,29],"type":"Buffer"}}},"defaultPort":443,"freeSockets":{"openrouter.ai:443:::::::::::::::::::::":[{"_SNICallback":null,"_closeAfterHandlingError":false,"_controlReleased":true,"_events":{"close":[null,null,null],"timeout":[null,null]},"_eventsCount":9,"_hadError":false,"_host":"openrouter.ai","_httpMessage":null,"_newSessionPending":false,"_parent":null,"_pendingData":null,"_pendingEncoding":"","_readableState":{"awaitDrainWriters":null,"buffer":[],"bufferIndex":0,"highWaterMark":16384,"length":0,"pipes":[]},"_rejectUnauthorized":true,"_requestCert":true,"_secureEstablished":true,"_securePending":false,"_server":null,"_sockname":null,"_tlsOptions":{"isServer":false,"pipe":false,"rejectUnauthorized":true,"requestCert":true,"secureContext":{"context":{}},"session":{"data":[48,130,5,15,2,1,1,2,2,3,4,4,2,19,2,4,32,209,105,134,79,242,244,131,170,85,204,88,109,214,222,84,36,197,38,157,159,88,200,238,190,73,116,3,141,119,216,191,122,4,48,147,30,244,78,115,132,41,207,31,152,23,195,47,88,252,97,136,42,11,132,240,32,179,249,163,65,129,142,220,47,80,167,29,131,221,206,224,47,182,7,92,98,82,11,38,77,150,21,161,6,2,4,104,181,170,17,162,4,2,2,28,32,163,130,3,174,48,130,3,170,48,130,3,80,160,3,2,1,2,2,16,20,164,8,48,247,211,4,94,17,104,186,54,234,8,85,73,48,10,6,8,42,134,72,206,61,4,3,2,48,59,49,11,48,9,6,3,85,4,6,19,2,85,83,49,30,48,28,6,3,85,4,10,19,21,71,111,111,103,108,101,32,84,114,117,115,116,32,83,101,114,118,105,99,101,115,49,12,48,10,6,3,85,4,3,19,3,87,69,49,48,30,23,13,50,53,48,56,51,49,49,51,48,51,48,51,90,23,13,50,53,49,49,50,57,49,52,48,50,53,48,90,48,24,49,22,48,20,6,3,85,4,3,19,13,111,112,101,110,114,111,117,116,101,114,46,97,105,48,89,48,19,6,7,42,134,72,206,61,2,1,6,8,42,134,72,206,61,3,1,7,3,66,0,4,158,108,172,200,253,33,93,69,124,84,149,116,35,222,91,188,174,161,118,65,156,50,200,234,42,211,23,244,247,3,213,86,119,69,140,155,201,4,151,132,162,199,166,0,38,35,127,53,176,205,22,156,32,154,46,228,205,217,50,199,41,212,186,126,163,130,2,87,48,130,2,83,48,14,6,3,85,29,15,1,1,255,4,4,3,2,7,128,48,19,6,3,85,29,37,4,12,48,10,6,8,43,6,1,5,5,7,3,1,48,12,6,3,85,29,19,1,1,255,4,2,48,0,48,29,6,3,85,29,14,4,22,4,20,100,215,24,244,23,157,116,124,207,176,3,139,4,97,230,161,97,108,196,53,48,31,6,3,85,29,35,4,24,48,22,128,20,144,119,146,53,103,196,255,168,204,169,230,123,217,128,121,123,204,147,249,56,48,94,6,8,43,6,1,5,5,7,1,1,4,82,48,80,48,39,6,8,43,6,1,5,5,7,48,1,134,27,104,116,116,112,58,47,47,111,46,112,107,105,46,103,111,111,103,47,115,47,119,101,49,47,70,75,81,48,37,6,8,43,6,1,5,5,7,48,2,134,25,104,116,116,112,58,47,47,105,46,112,107,105,46,103,111,111,103,47,119,101,49,46,99,114,116,48,41,6,3,85,29,17,4,34,48,32,130,13,111,112,101,110,114,111,117,116,101,114,46,97,105,130,15,42,46,111,112,101,110,114,111,117,116,101,114,46,97,105,48,19,6,3,85,29,32,4,12,48,10,48,8,6,6,103,129,12,1,2,1,48,54,6,3,85,29,31,4,47,48,45,48,43,160,41,160,39,134,37,104,116,116,112,58,47,47,99,46,112,107,105,46,103,111,111,103,47,119,101,49,47,116,51,76,74,98,90,105,66,116,115,85,46,99,114,108,48,130,1,4,6,10,43,6,1,4,1,214,121,2,4,2,4,129,245,4,129,242,0,240,0,118,0,204,251,15,106,133,113,9,101,254,149,155,83,206,233,178,124,34,233,133,92,13,151,141,182,169,126,84,192,254,76,13,176,0,0,1,153,0,112,80,67,0,0,4,3,0,71,48,69,2,32,121,184,184,57,232,35,2,187,100,39,108,69,216,29,25,253,12,243,182,210,115,115,179,178,236,113,240,32,75,44,64,67,2,33,0,228,225,138,150,219,152,70,138,130,97,180,56,8,148,190,173,16,168,130,177,39,29,41,150,50,130,215,85,160,52,79,252,0,118,0,221,220,202,52,149,215,225,22,5,231,149,50,250,199,159,248,61,28,80,223,219,0,58,20,18,118,10,44,172,187,200,42,0,0,1,153,0,112,80,59,0,0,4,3,0,71,48,69,2,33,0,199,60,19,22,171,236,28,33,39,147,146,188,224,190,15,153,218,67,189,137,92,179,148,15,148,26,119,163,36,195,33,127,2,32,86,216,161,230,149,74,8,220,231,206,55,91,189,154,242,81,10,23,167,139,248,72,38,170,160,44,192,232,125,55,242,247,48,10,6,8,42,134,72,206,61,4,3,2,3,72,0,48,69,2,32,10,7,42,199,142,8,70,26,245,232,39,242,55,5,154,227,254,31,100,93,170,44,173,243,100,55,107,58,194,2,111,14,2,33,0,225,109,202,110,142,66,218,156,250,181,10,23,116,213,84,61,238,218,17,24,39,26,242,159,146,154,64,85,125,217,59,102,164,2,4,0,166,15,4,13,111,112,101,110,114,111,117,116,101,114,46,97,105,169,5,2,3,0,253,32,170,129,195,4,129,192,162,24,94,85,68,73,251,75,176,138,224,179,156,118,155,6,220,3,11,149,236,218,100,138,120,103,4,76,189,133,252,199,27,144,54,126,66,181,184,65,95,138,232,166,116,239,113,115,10,145,82,66,121,139,14,127,243,75,254,6,102,243,59,161,19,232,24,192,162,43,116,232,172,76,65,53,31,130,119,42,97,76,135,49,79,16,175,79,126,149,131,36,109,67,224,8,40,33,87,180,169,133,146,134,112,189,156,115,216,242,89,232,140,96,48,127,112,159,129,213,223,195,75,54,183,201,15,214,199,27,83,114,156,80,28,29,12,235,202,167,89,86,178,207,228,151,66,91,198,84,232,28,245,208,122,118,219,84,138,140,27,175,194,13,240,226,240,196,15,207,179,135,41,165,16,11,174,125,65,152,65,236,9,68,205,64,101,121,36,247,36,151,174,7,2,5,0,246,126,188,8,179,3,2,1,29],"type":"Buffer"}},"_writableState":{"bufferedIndex":0,"corked":0,"highWaterMark":16384,"length":0,"pendingcb":0,"writelen":0},"allowHalfOpen":false,"alpnProtocol":false,"authorizationError":null,"authorized":true,"autoSelectFamilyAttemptedAddresses":["************:443"],"connecting":false,"encrypted":true,"parser":null,"secureConnecting":false,"servername":"openrouter.ai","ssl":{"_parent":{"onconnection":null,"reading":true},"_parentWrap":null,"_secureContext":{"context":{}},"reading":true},"timeout":5000}]},"keepAlive":true,"keepAliveMsecs":1000,"maxCachedSessions":100,"maxFreeSockets":256,"maxSockets":null,"maxTotalSockets":null,"options":{"keepAlive":true,"noDelay":true,"path":null,"scheduling":"lifo","timeout":5000},"protocol":"https:","requests":{},"scheduling":"lifo","sockets":{},"totalSocketCount":1},"chunkedEncoding":false,"destroyed":true,"finished":true,"host":"openrouter.ai","maxHeadersCount":null,"maxRequestsOnConnectionReached":false,"method":"POST","outputData":[],"outputSize":0,"parser":null,"path":"/api/v1/chat/completions","protocol":"https:","res":{"_consuming":true,"_dumped":false,"_events":{"close":[null,null,null],"end":[null,null,null],"error":[null,null,null],"finish":[null,null]},"_eventsCount":4,"_readableState":{"awaitDrainWriters":null,"buffer":[],"bufferIndex":0,"highWaterMark":16384,"length":0,"pipes":[]},"aborted":false,"client":{"_SNICallback":null,"_closeAfterHandlingError":false,"_controlReleased":true,"_events":{"close":[null,null,null],"timeout":[null,null]},"_eventsCount":9,"_hadError":false,"_host":"openrouter.ai","_httpMessage":null,"_newSessionPending":false,"_parent":null,"_pendingData":null,"_pendingEncoding":"","_readableState":{"awaitDrainWriters":null,"buffer":[],"bufferIndex":0,"highWaterMark":16384,"length":0,"pipes":[]},"_rejectUnauthorized":true,"_requestCert":true,"_secureEstablished":true,"_securePending":false,"_server":null,"_sockname":null,"_tlsOptions":{"isServer":false,"pipe":false,"rejectUnauthorized":true,"requestCert":true,"secureContext":{"context":{}},"session":{"data":[48,130,5,15,2,1,1,2,2,3,4,4,2,19,2,4,32,209,105,134,79,242,244,131,170,85,204,88,109,214,222,84,36,197,38,157,159,88,200,238,190,73,116,3,141,119,216,191,122,4,48,147,30,244,78,115,132,41,207,31,152,23,195,47,88,252,97,136,42,11,132,240,32,179,249,163,65,129,142,220,47,80,167,29,131,221,206,224,47,182,7,92,98,82,11,38,77,150,21,161,6,2,4,104,181,170,17,162,4,2,2,28,32,163,130,3,174,48,130,3,170,48,130,3,80,160,3,2,1,2,2,16,20,164,8,48,247,211,4,94,17,104,186,54,234,8,85,73,48,10,6,8,42,134,72,206,61,4,3,2,48,59,49,11,48,9,6,3,85,4,6,19,2,85,83,49,30,48,28,6,3,85,4,10,19,21,71,111,111,103,108,101,32,84,114,117,115,116,32,83,101,114,118,105,99,101,115,49,12,48,10,6,3,85,4,3,19,3,87,69,49,48,30,23,13,50,53,48,56,51,49,49,51,48,51,48,51,90,23,13,50,53,49,49,50,57,49,52,48,50,53,48,90,48,24,49,22,48,20,6,3,85,4,3,19,13,111,112,101,110,114,111,117,116,101,114,46,97,105,48,89,48,19,6,7,42,134,72,206,61,2,1,6,8,42,134,72,206,61,3,1,7,3,66,0,4,158,108,172,200,253,33,93,69,124,84,149,116,35,222,91,188,174,161,118,65,156,50,200,234,42,211,23,244,247,3,213,86,119,69,140,155,201,4,151,132,162,199,166,0,38,35,127,53,176,205,22,156,32,154,46,228,205,217,50,199,41,212,186,126,163,130,2,87,48,130,2,83,48,14,6,3,85,29,15,1,1,255,4,4,3,2,7,128,48,19,6,3,85,29,37,4,12,48,10,6,8,43,6,1,5,5,7,3,1,48,12,6,3,85,29,19,1,1,255,4,2,48,0,48,29,6,3,85,29,14,4,22,4,20,100,215,24,244,23,157,116,124,207,176,3,139,4,97,230,161,97,108,196,53,48,31,6,3,85,29,35,4,24,48,22,128,20,144,119,146,53,103,196,255,168,204,169,230,123,217,128,121,123,204,147,249,56,48,94,6,8,43,6,1,5,5,7,1,1,4,82,48,80,48,39,6,8,43,6,1,5,5,7,48,1,134,27,104,116,116,112,58,47,47,111,46,112,107,105,46,103,111,111,103,47,115,47,119,101,49,47,70,75,81,48,37,6,8,43,6,1,5,5,7,48,2,134,25,104,116,116,112,58,47,47,105,46,112,107,105,46,103,111,111,103,47,119,101,49,46,99,114,116,48,41,6,3,85,29,17,4,34,48,32,130,13,111,112,101,110,114,111,117,116,101,114,46,97,105,130,15,42,46,111,112,101,110,114,111,117,116,101,114,46,97,105,48,19,6,3,85,29,32,4,12,48,10,48,8,6,6,103,129,12,1,2,1,48,54,6,3,85,29,31,4,47,48,45,48,43,160,41,160,39,134,37,104,116,116,112,58,47,47,99,46,112,107,105,46,103,111,111,103,47,119,101,49,47,116,51,76,74,98,90,105,66,116,115,85,46,99,114,108,48,130,1,4,6,10,43,6,1,4,1,214,121,2,4,2,4,129,245,4,129,242,0,240,0,118,0,204,251,15,106,133,113,9,101,254,149,155,83,206,233,178,124,34,233,133,92,13,151,141,182,169,126,84,192,254,76,13,176,0,0,1,153,0,112,80,67,0,0,4,3,0,71,48,69,2,32,121,184,184,57,232,35,2,187,100,39,108,69,216,29,25,253,12,243,182,210,115,115,179,178,236,113,240,32,75,44,64,67,2,33,0,228,225,138,150,219,152,70,138,130,97,180,56,8,148,190,173,16,168,130,177,39,29,41,150,50,130,215,85,160,52,79,252,0,118,0,221,220,202,52,149,215,225,22,5,231,149,50,250,199,159,248,61,28,80,223,219,0,58,20,18,118,10,44,172,187,200,42,0,0,1,153,0,112,80,59,0,0,4,3,0,71,48,69,2,33,0,199,60,19,22,171,236,28,33,39,147,146,188,224,190,15,153,218,67,189,137,92,179,148,15,148,26,119,163,36,195,33,127,2,32,86,216,161,230,149,74,8,220,231,206,55,91,189,154,242,81,10,23,167,139,248,72,38,170,160,44,192,232,125,55,242,247,48,10,6,8,42,134,72,206,61,4,3,2,3,72,0,48,69,2,32,10,7,42,199,142,8,70,26,245,232,39,242,55,5,154,227,254,31,100,93,170,44,173,243,100,55,107,58,194,2,111,14,2,33,0,225,109,202,110,142,66,218,156,250,181,10,23,116,213,84,61,238,218,17,24,39,26,242,159,146,154,64,85,125,217,59,102,164,2,4,0,166,15,4,13,111,112,101,110,114,111,117,116,101,114,46,97,105,169,5,2,3,0,253,32,170,129,195,4,129,192,162,24,94,85,68,73,251,75,176,138,224,179,156,118,155,6,220,3,11,149,236,218,100,138,120,103,4,76,189,133,252,199,27,144,54,126,66,181,184,65,95,138,232,166,116,239,113,115,10,145,82,66,121,139,14,127,243,75,254,6,102,243,59,161,19,232,24,192,162,43,116,232,172,76,65,53,31,130,119,42,97,76,135,49,79,16,175,79,126,149,131,36,109,67,224,8,40,33,87,180,169,133,146,134,112,189,156,115,216,242,89,232,140,96,48,127,112,159,129,213,223,195,75,54,183,201,15,214,199,27,83,114,156,80,28,29,12,235,202,167,89,86,178,207,228,151,66,91,198,84,232,28,245,208,122,118,219,84,138,140,27,175,194,13,240,226,240,196,15,207,179,135,41,165,16,11,174,125,65,152,65,236,9,68,205,64,101,121,36,247,36,151,174,7,2,5,0,246,126,188,8,179,3,2,1,29],"type":"Buffer"}},"_writableState":{"bufferedIndex":0,"corked":0,"highWaterMark":16384,"length":0,"pendingcb":0,"writelen":0},"allowHalfOpen":false,"alpnProtocol":false,"authorizationError":null,"authorized":true,"autoSelectFamilyAttemptedAddresses":["************:443"],"connecting":false,"encrypted":true,"parser":null,"secureConnecting":false,"servername":"openrouter.ai","ssl":{"_parent":{"onconnection":null,"reading":true},"_parentWrap":null,"_secureContext":{"context":{}},"reading":true},"timeout":5000},"complete":true,"httpVersion":"1.1","httpVersionMajor":1,"httpVersionMinor":1,"method":null,"rawHeaders":["Date","Mon, 01 Sep 2025 14:16:57 GMT","Content-Type","application/json","Transfer-Encoding","chunked","Connection","keep-alive","Content-Encoding","gzip","Access-Control-Allow-Origin","*","Vary","Accept-Encoding","Permissions-Policy","payment=(self \"https://checkout.stripe.com\" \"https://connect-js.stripe.com\" \"https://js.stripe.com\" \"https://*.js.stripe.com\" \"https://hooks.stripe.com\")","Referrer-Policy","no-referrer, strict-origin-when-cross-origin","X-Content-Type-Options","nosniff","Server","cloudflare","CF-RAY","9785636f2a03e1fd-MRS"],"rawTrailers":[],"redirects":[],"req":"[Circular]","responseUrl":"https://openrouter.ai/api/v1/chat/completions","socket":null,"statusCode":404,"statusMessage":"Not Found","upgrade":false,"url":""},"reusedSocket":true,"sendDate":false,"shouldKeepAlive":true,"strictContentLength":false,"timeoutCb":null,"upgradeOrConnect":false,"useChunkedEncodingByDefault":true,"writable":true},"response":{"config":{"adapter":["xhr","http","fetch"],"allowAbsoluteUrls":true,"data":"{\"model\":\"google/gemini-2.5-flash-preview\",\"messages\":[{\"role\":\"system\",\"content\":\"Tu es un assistant spécialisé dans la création de missions sur JobPartiel.\\n\\n            Tu dois répondre UNIQUEMENT au format JSON, sans aucun texte explicatif.\\n\\n            Pour l'étape \\\"Catégorie et service\\\", tu dois absolument utiliser les IDs exacts fournis dans les listes, sans les modifier.\\n            - Exemple correct: {\\\"category_id\\\": \\\"1\\\", \\\"subcategory_id\\\": \\\"1-1\\\"}\\n            - Exemple incorrect: {\\\"category_id\\\": \\\"ID de la catégorie\\\", \\\"subcategory_id\\\": \\\"ID de la sous-catégorie\\\"}\\n\\n            Pour l'étape \\\"Résumé\\\", tu dois générer un texte lisible en français qui résume toutes les informations de la mission:\\n            - Le résumé doit être un texte formaté, PAS un objet JSON échappé\\n            - N'inclus PAS les identifiants techniques (category_id, subcategory_id)\\n            - Utilise les noms des catégories et sous-catégories, pas leurs IDs\\n            - Présente les informations de manière claire et structurée\\n            - Exemple: {\\\"summary\\\": \\\"Vous avez créé une mission de jardinage pour tondre votre pelouse le samedi 10 mai à 14h. La mission se déroulera à votre domicile à Paris. Vous proposez un budget de 50€.\\\"}\\n\\n            Pour l'étape \\\"Budget\\\", respecte strictement ces règles:\\n            - Si l'utilisateur mentionne explicitement \\\"jobi\\\" ou \\\"échange\\\" UNIQUEMENT, utilise \\\"jobi\\\" comme méthode de paiement.\\n            - Si l'utilisateur mentionne explicitement \\\"euros\\\" ou \\\"€\\\" UNIQUEMENT, utilise \\\"euros\\\" comme méthode de paiement.\\n            - Si l'utilisateur mentionne les deux options OU indique qu'il accepte les deux modes de paiement, utilise \\\"both\\\".\\n            - Si l'utilisateur utilise des termes comme \\\"au choix\\\", \\\"peu importe\\\", \\\"l'un ou l'autre\\\", utilise \\\"both\\\".\\n            - Si la description contient \\\"paiement en jobi\\\" ou similaire, utilise \\\"jobi\\\".\\n            - Si aucune méthode n'est spécifiée, utilise \\\"euros\\\" par défaut.\\n\\n            Pour l'étape \\\"Description du besoin\\\", respecte strictement ces règles:\\n            - Ne jamais inclure d'adresse complète dans la description\\n            - Ne jamais inclure de numéro de téléphone dans la description\\n            - Si l'utilisateur mentionne une adresse, utilise uniquement la ville (ex: \\\"à Perpignan\\\")\\n\\n            Pour l'étape \\\"Localisation\\\", respecte strictement ces règles:\\n            - Analyse attentivement le message original de l'utilisateur pour y trouver une adresse\\n            - Si l'utilisateur a mentionné une adresse complète, utilise-la même si elle n'apparaît pas dans la description\\n            - Extrait le numéro, la rue, le code postal et la ville si ces informations sont présentes\\n\\n            Pour l'étape \\\"Horaires\\\", respecte strictement ces règles:\\n            - Utilise la date actuelle comme référence pour interpréter les expressions temporelles\\n            - Convertis les expressions comme \\\"la semaine prochaine\\\", \\\"vendredi prochain\\\" en dates précises\\n            - Fournis toujours des dates au format YYYY-MM-DD et des heures au format HH:MM\\n            - Si has_time_preference = true, le champ date_mission DOIT contenir la date du premier créneau horaire\\n            - Ne laisse JAMAIS le champ date_mission vide si has_time_preference = true\\n            - ATTENTION: Le \\\"week-end\\\" correspond STRICTEMENT au samedi et dimanche, JAMAIS au vendredi ou au lundi\\n            - Quand l'utilisateur mentionne \\\"ce week-end\\\", génère UNIQUEMENT des créneaux pour le samedi et/ou dimanche prochain\\n\\n            N'utilise jamais de texte descriptif à la place des IDs numériques.\"},{\"role\":\"user\",\"content\":\"\\n      Tu es un assistant pour la création de mission sur JobPartiel, une plateforme de services entre particuliers.\\n\\n      Date et heure actuelles: lundi 1 septembre 2025 à 16:16\\n\\n      L'utilisateur a décrit son besoin: \\\"Changement disque de frein renault clio 3\\\"\\n\\n      Informations sur l'utilisateur:\\n      - Nom: Maxime Choinet\\n      - Ville: Pia\\n      - Code postal: 66380\\n      - Adresse: Rue de l’alzine\\n\\n      Données déjà collectées:\\n      {\\\"is_urgent\\\":false}\\n\\n      Étape actuelle: Description du besoin\\n\\n      Pour cette étape, tu dois analyser le besoin de l'utilisateur et générer les informations appropriées au format JSON.\\n\\n      \\n        Tu es un expert en rédaction de descriptions de missions pour JobPartiel. Ta tâche est de créer une description professionnelle, détaillée et attrayante qui aidera l'utilisateur à trouver rapidement un prestataire qualifié.  \\n        \\n        INSTRUCTIONS DÉTAILLÉES:\\n        1. Génère un titre court (max 70 caractères) qui soit accrocheur, précis et qui mette en avant le service principal demandé\\n        2. Crée une description détaillée (max 1200 caractères) qui:\\n           - Commence par une phrase d'accroche qui résume clairement le besoin\\n           - Détaille précisément les tâches à accomplir avec des paragraphes bien structurés\\n           - Inclut toutes les spécifications techniques pertinentes (dimensions, matériaux, équipements, etc.)\\n           - Mentionne les compétences ou qualifications requises pour le prestataire\\n           - Précise le niveau de qualité attendu et les délais si mentionnés\\n           - Utilise un ton professionnel mais accessible\\n           - Évite tout langage vague ou générique\\n        3. Détermine si la mission est urgente en analysant minutieusement le langage utilisé\\n\\n        Règles pour is_urgent:\\n        - Si l'utilisateur utilise des mots comme \\\"urgent\\\", \\\"rapidement\\\", \\\"dès que possible\\\", \\\"aujourd'hui\\\", \\\"immédiatement\\\", mets is_urgent à true\\n        - Si l'utilisateur mentionne une date proche (aujourd'hui ou demain) avec un ton pressant, mets is_urgent à true\\n        - Si l'utilisateur a explicitement coché la case \\\"Mission urgente\\\" dans l'interface (non), utilise cette valeur\\n        - Sinon, mets is_urgent à false\\n\\n        RÈGLES DE CONFIDENTIALITÉ:\\n        - Ne jamais inclure d'adresse complète dans la description\\n        - Ne jamais inclure de numéro de téléphone dans la description\\n        - Si l'utilisateur mentionne une adresse, utilise uniquement la ville (ex: \\\"à Perpignan\\\")\\n        - Concentre-toi sur la description du service demandé, pas sur les informations personnelles\\n\\n        CONSERVATION DES INFORMATIONS IMPORTANTES:\\n        - Si l'utilisateur mentionne un budget précis, inclus-le OBLIGATOIREMENT dans la description (ex: \\\"Budget de 40€ pour 6 heures\\\")\\n        - Si l'utilisateur mentionne une durée, inclus-la OBLIGATOIREMENT dans la description\\n        - Si l'utilisateur mentionne des dates ou horaires spécifiques, inclus-les OBLIGATOIREMENT dans la description\\n        - Si l'utilisateur mentionne des matériaux, outils ou équipements spécifiques, inclus-les OBLIGATOIREMENT dans la description\\n        - Si l'utilisateur mentionne des compétences ou qualifications requises, inclus-les OBLIGATOIREMENT dans la description\\n        - Conserve toutes les informations importantes fournies par l'utilisateur dans ta description afin de générer une description la plus précise possible de son besoin\\n\\n        INTERDICTION ABSOLUE DE TOUT COMMENTAIRE SUR L'ABSENCE D'INFORMATION:\\n        - Si une information (budget, durée, etc.) n'est pas connue, ne commente pas l'absence d'information.\\n\\n        EXEMPLES DE TITRES EFFICACES:\\n        - \\\"Création site web avec système de réservation pour restaurant\\\"\\n        - \\\"Tonte de pelouse et taille de haies sur terrain de 500m²\\\"\\n        - \\\"Garde de chat affectueux pendant 2 semaines en août\\\"\\n\\n        EXEMPLES DE DESCRIPTIONS EFFICACES:\\n        - \\\"Je recherche un développeur web expérimenté pour créer un site vitrine avec système de réservation en ligne pour mon restaurant. Le site doit inclure: page d'accueil avec photos, menu interactif, système de réservation de table avec confirmation par email, page contact et formulaire. Design moderne et épuré souhaité, compatible mobile et tablette. Compétences requises: HTML/CSS, JavaScript, PHP ou équivalent. Le site doit être livré avec documentation et formation d'une heure pour la gestion du contenu. Budget de 800€ pour l'ensemble du projet.\\\"\\n        - \\\"Besoin d'un jardinier qualifié pour entretenir mon jardin de 500m² à Perpignan. Les travaux comprennent: tonte complète de la pelouse, taille des 6 haies de cyprès (hauteur 2m), désherbage des massifs de fleurs et nettoyage complet après intervention. Le jardin n'a pas été entretenu depuis 2 mois. Matériel non fourni, merci de venir avec votre équipement. Intervention souhaitée le week-end prochain, durée estimée: 4-5 heures. Budget: 120€ pour l'ensemble des travaux.\\\"\\n\\n        Réponds uniquement au format JSON suivant:\\n        {\\n          \\\"titre\\\": \\\"Titre court et précis\\\",\\n          \\\"description\\\": \\\"Description détaillée du besoin\\\",\\n          \\\"is_urgent\\\": true/false\\n        }\\n      \\n\\n      \\n\\n      \\n\\n      \\n\\n      \\n\\n      \\n\\n      Réponds UNIQUEMENT avec un objet JSON valide correspondant à l'étape demandée, sans aucun texte supplémentaire.\\n      Assure-toi que le JSON est correctement formaté et contient toutes les propriétés requises pour l'étape.\\n      N'ajoute pas de commentaires ou d'explications en dehors de l'objet JSON.\\n    \"}],\"temperature\":0.1,\"top_p\":0.35,\"frequency_penalty\":0,\"presence_penalty\":0,\"max_tokens\":1500,\"response_format\":{\"type\":\"json_object\"}}","env":{},"headers":{"Accept":"application/json, text/plain, */*","Accept-Encoding":"gzip, compress, deflate, br","Authorization":"Bearer 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","Content-Length":"9755","Content-Type":"application/json","HTTP-Referer":"https://jobpartiel.fr","User-Agent":"axios/1.11.0","X-Title":"JobPartiel Mission Assistant"},"maxBodyLength":-1,"maxContentLength":-1,"method":"post","signal":{},"timeout":15000,"transformRequest":[null],"transformResponse":[null],"transitional":{"clarifyTimeoutError":false,"forcedJSONParsing":true,"silentJSONParsing":true},"url":"https://openrouter.ai/api/v1/chat/completions","xsrfCookieName":"XSRF-TOKEN","xsrfHeaderName":"X-XSRF-TOKEN"},"data":{"error":{"code":404,"message":"No endpoints found for google/gemini-2.5-flash-preview."},"user_id":"user_2q7gR1dbemOA3uD9HjOkXQ3zHL4"},"headers":{"access-control-allow-origin":"*","cf-ray":"9785636f2a03e1fd-MRS","connection":"keep-alive","content-type":"application/json","date":"Mon, 01 Sep 2025 14:16:57 GMT","permissions-policy":"payment=(self \"https://checkout.stripe.com\" \"https://connect-js.stripe.com\" \"https://js.stripe.com\" \"https://*.js.stripe.com\" \"https://hooks.stripe.com\")","referrer-policy":"no-referrer, strict-origin-when-cross-origin","server":"cloudflare","transfer-encoding":"chunked","vary":"Accept-Encoding","x-content-type-options":"nosniff"},"request":{"_closed":true,"_contentLength":"9755","_defaultKeepAlive":true,"_ended":true,"_events":{},"_eventsCount":7,"_hasBody":true,"_header":"POST /api/v1/chat/completions HTTP/1.1\r\nAccept: application/json, text/plain, */*\r\nContent-Type: application/json\r\nAuthorization: Bearer 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\r\nHTTP-Referer: https://jobpartiel.fr\r\nX-Title: JobPartiel Mission Assistant\r\nUser-Agent: axios/1.11.0\r\nContent-Length: 9755\r\nAccept-Encoding: gzip, compress, deflate, br\r\nHost: openrouter.ai\r\nConnection: keep-alive\r\n\r\n","_headerSent":true,"_keepAliveTimeout":0,"_last":false,"_redirectable":{"_currentRequest":"[Circular]","_currentUrl":"https://openrouter.ai/api/v1/chat/completions","_ended":true,"_ending":true,"_events":{"socket":[null,null]},"_eventsCount":3,"_options":{"agents":{},"beforeRedirects":{},"headers":{"Accept":"application/json, text/plain, */*","Accept-Encoding":"gzip, compress, deflate, br","Authorization":"Bearer 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","Content-Length":"9755","Content-Type":"application/json","HTTP-Referer":"https://jobpartiel.fr","User-Agent":"axios/1.11.0","X-Title":"JobPartiel Mission Assistant"},"hostname":"openrouter.ai","maxBodyLength":null,"maxRedirects":21,"method":"POST","nativeProtocols":{"http:":{"METHODS":["ACL","BIND","CHECKOUT","CONNECT","COPY","DELETE","GET","HEAD","LINK","LOCK","M-SEARCH","MERGE","MKACTIVITY","MKCALENDAR","MKCOL","MOVE","NOTIFY","OPTIONS","PATCH","POST","PROPFIND","PROPPATCH","PURGE","PUT","QUERY","REBIND","REPORT","SEARCH","SOURCE","SUBSCRIBE","TRACE","UNBIND","UNLINK","UNLOCK","UNSUBSCRIBE"],"STATUS_CODES":{"100":"Continue","101":"Switching Protocols","102":"Processing","103":"Early Hints","200":"OK","201":"Created","202":"Accepted","203":"Non-Authoritative Information","204":"No Content","205":"Reset Content","206":"Partial Content","207":"Multi-Status","208":"Already Reported","226":"IM Used","300":"Multiple Choices","301":"Moved Permanently","302":"Found","303":"See Other","304":"Not Modified","305":"Use Proxy","307":"Temporary Redirect","308":"Permanent Redirect","400":"Bad Request","401":"Unauthorized","402":"Payment Required","403":"Forbidden","404":"Not Found","405":"Method Not Allowed","406":"Not Acceptable","407":"Proxy Authentication Required","408":"Request Timeout","409":"Conflict","410":"Gone","411":"Length Required","412":"Precondition Failed","413":"Payload Too Large","414":"URI Too Long","415":"Unsupported Media Type","416":"Range Not Satisfiable","417":"Expectation Failed","418":"I'm a Teapot","421":"Misdirected Request","422":"Unprocessable Entity","423":"Locked","424":"Failed Dependency","425":"Too Early","426":"Upgrade Required","428":"Precondition Required","429":"Too Many Requests","431":"Request Header Fields Too Large","451":"Unavailable For Legal Reasons","500":"Internal Server Error","501":"Not Implemented","502":"Bad Gateway","503":"Service Unavailable","504":"Gateway Timeout","505":"HTTP Version Not Supported","506":"Variant Also Negotiates","507":"Insufficient Storage","508":"Loop Detected","509":"Bandwidth Limit Exceeded","510":"Not Extended","511":"Network Authentication Required"},"globalAgent":{"_events":{},"_eventsCount":2,"defaultPort":80,"freeSockets":{},"keepAlive":true,"keepAliveMsecs":1000,"maxFreeSockets":256,"maxSockets":null,"maxTotalSockets":null,"options":{"keepAlive":true,"noDelay":true,"path":null,"scheduling":"lifo","timeout":5000},"protocol":"http:","requests":{},"scheduling":"lifo","sockets":{},"totalSocketCount":0},"maxHeaderSize":16384},"https:":{"globalAgent":{"_events":{},"_eventsCount":2,"_sessionCache":{"list":["openrouter.ai:443:::::::::::::::::::::"],"map":{"openrouter.ai:443:::::::::::::::::::::":{"data":[48,130,5,14,2,1,1,2,2,3,4,4,2,19,2,4,32,13,228,229,61,45,5,45,139,98,124,130,234,200,153,210,194,125,158,15,117,2,177,112,193,167,108,166,203,60,157,33,37,4,48,185,115,34,255,94,179,0,131,69,53,76,108,68,205,182,183,244,175,80,154,30,241,113,52,1,4,78,74,209,164,110,164,41,237,242,39,229,51,221,25,102,232,148,87,162,254,49,99,161,6,2,4,104,181,170,215,162,4,2,2,28,32,163,130,3,174,48,130,3,170,48,130,3,80,160,3,2,1,2,2,16,20,164,8,48,247,211,4,94,17,104,186,54,234,8,85,73,48,10,6,8,42,134,72,206,61,4,3,2,48,59,49,11,48,9,6,3,85,4,6,19,2,85,83,49,30,48,28,6,3,85,4,10,19,21,71,111,111,103,108,101,32,84,114,117,115,116,32,83,101,114,118,105,99,101,115,49,12,48,10,6,3,85,4,3,19,3,87,69,49,48,30,23,13,50,53,48,56,51,49,49,51,48,51,48,51,90,23,13,50,53,49,49,50,57,49,52,48,50,53,48,90,48,24,49,22,48,20,6,3,85,4,3,19,13,111,112,101,110,114,111,117,116,101,114,46,97,105,48,89,48,19,6,7,42,134,72,206,61,2,1,6,8,42,134,72,206,61,3,1,7,3,66,0,4,158,108,172,200,253,33,93,69,124,84,149,116,35,222,91,188,174,161,118,65,156,50,200,234,42,211,23,244,247,3,213,86,119,69,140,155,201,4,151,132,162,199,166,0,38,35,127,53,176,205,22,156,32,154,46,228,205,217,50,199,41,212,186,126,163,130,2,87,48,130,2,83,48,14,6,3,85,29,15,1,1,255,4,4,3,2,7,128,48,19,6,3,85,29,37,4,12,48,10,6,8,43,6,1,5,5,7,3,1,48,12,6,3,85,29,19,1,1,255,4,2,48,0,48,29,6,3,85,29,14,4,22,4,20,100,215,24,244,23,157,116,124,207,176,3,139,4,97,230,161,97,108,196,53,48,31,6,3,85,29,35,4,24,48,22,128,20,144,119,146,53,103,196,255,168,204,169,230,123,217,128,121,123,204,147,249,56,48,94,6,8,43,6,1,5,5,7,1,1,4,82,48,80,48,39,6,8,43,6,1,5,5,7,48,1,134,27,104,116,116,112,58,47,47,111,46,112,107,105,46,103,111,111,103,47,115,47,119,101,49,47,70,75,81,48,37,6,8,43,6,1,5,5,7,48,2,134,25,104,116,116,112,58,47,47,105,46,112,107,105,46,103,111,111,103,47,119,101,49,46,99,114,116,48,41,6,3,85,29,17,4,34,48,32,130,13,111,112,101,110,114,111,117,116,101,114,46,97,105,130,15,42,46,111,112,101,110,114,111,117,116,101,114,46,97,105,48,19,6,3,85,29,32,4,12,48,10,48,8,6,6,103,129,12,1,2,1,48,54,6,3,85,29,31,4,47,48,45,48,43,160,41,160,39,134,37,104,116,116,112,58,47,47,99,46,112,107,105,46,103,111,111,103,47,119,101,49,47,116,51,76,74,98,90,105,66,116,115,85,46,99,114,108,48,130,1,4,6,10,43,6,1,4,1,214,121,2,4,2,4,129,245,4,129,242,0,240,0,118,0,204,251,15,106,133,113,9,101,254,149,155,83,206,233,178,124,34,233,133,92,13,151,141,182,169,126,84,192,254,76,13,176,0,0,1,153,0,112,80,67,0,0,4,3,0,71,48,69,2,32,121,184,184,57,232,35,2,187,100,39,108,69,216,29,25,253,12,243,182,210,115,115,179,178,236,113,240,32,75,44,64,67,2,33,0,228,225,138,150,219,152,70,138,130,97,180,56,8,148,190,173,16,168,130,177,39,29,41,150,50,130,215,85,160,52,79,252,0,118,0,221,220,202,52,149,215,225,22,5,231,149,50,250,199,159,248,61,28,80,223,219,0,58,20,18,118,10,44,172,187,200,42,0,0,1,153,0,112,80,59,0,0,4,3,0,71,48,69,2,33,0,199,60,19,22,171,236,28,33,39,147,146,188,224,190,15,153,218,67,189,137,92,179,148,15,148,26,119,163,36,195,33,127,2,32,86,216,161,230,149,74,8,220,231,206,55,91,189,154,242,81,10,23,167,139,248,72,38,170,160,44,192,232,125,55,242,247,48,10,6,8,42,134,72,206,61,4,3,2,3,72,0,48,69,2,32,10,7,42,199,142,8,70,26,245,232,39,242,55,5,154,227,254,31,100,93,170,44,173,243,100,55,107,58,194,2,111,14,2,33,0,225,109,202,110,142,66,218,156,250,181,10,23,116,213,84,61,238,218,17,24,39,26,242,159,146,154,64,85,125,217,59,102,164,2,4,0,166,15,4,13,111,112,101,110,114,111,117,116,101,114,46,97,105,169,5,2,3,0,253,32,170,129,195,4,129,192,162,24,94,85,68,73,251,75,176,138,224,179,156,118,155,6,122,10,201,131,35,146,100,30,37,53,71,144,109,220,254,8,191,149,248,24,17,38,32,16,220,255,76,193,246,236,83,79,84,116,216,45,32,32,241,84,125,174,244,250,174,157,141,5,109,61,56,92,29,202,153,159,28,238,66,156,62,183,137,86,128,31,239,214,34,107,241,85,127,46,67,122,126,15,175,248,54,24,129,150,158,4,214,69,212,153,146,254,187,29,143,249,109,26,209,184,238,247,85,149,195,177,212,63,64,78,211,121,3,78,77,250,9,243,44,190,218,138,112,163,161,14,115,57,69,183,47,35,94,239,198,85,86,161,24,126,213,196,164,44,188,111,226,46,168,173,159,66,16,158,32,52,30,219,82,67,100,82,178,96,243,86,242,120,187,25,122,149,20,197,179,4,174,6,2,4,0,254,227,171,179,3,2,1,29],"type":"Buffer"}}},"defaultPort":443,"freeSockets":{"openrouter.ai:443:::::::::::::::::::::":[{"_SNICallback":null,"_closeAfterHandlingError":false,"_controlReleased":true,"_events":{"close":[null,null,null],"timeout":[null,null]},"_eventsCount":9,"_hadError":false,"_host":"openrouter.ai","_httpMessage":null,"_newSessionPending":false,"_parent":null,"_pendingData":null,"_pendingEncoding":"","_readableState":{"awaitDrainWriters":null,"buffer":[],"bufferIndex":0,"highWaterMark":16384,"length":0,"pipes":[]},"_rejectUnauthorized":true,"_requestCert":true,"_secureEstablished":true,"_securePending":false,"_server":null,"_sockname":null,"_tlsOptions":{"isServer":false,"pipe":false,"rejectUnauthorized":true,"requestCert":true,"secureContext":{"context":{}},"session":{"data":[48,130,5,15,2,1,1,2,2,3,4,4,2,19,2,4,32,209,105,134,79,242,244,131,170,85,204,88,109,214,222,84,36,197,38,157,159,88,200,238,190,73,116,3,141,119,216,191,122,4,48,147,30,244,78,115,132,41,207,31,152,23,195,47,88,252,97,136,42,11,132,240,32,179,249,163,65,129,142,220,47,80,167,29,131,221,206,224,47,182,7,92,98,82,11,38,77,150,21,161,6,2,4,104,181,170,17,162,4,2,2,28,32,163,130,3,174,48,130,3,170,48,130,3,80,160,3,2,1,2,2,16,20,164,8,48,247,211,4,94,17,104,186,54,234,8,85,73,48,10,6,8,42,134,72,206,61,4,3,2,48,59,49,11,48,9,6,3,85,4,6,19,2,85,83,49,30,48,28,6,3,85,4,10,19,21,71,111,111,103,108,101,32,84,114,117,115,116,32,83,101,114,118,105,99,101,115,49,12,48,10,6,3,85,4,3,19,3,87,69,49,48,30,23,13,50,53,48,56,51,49,49,51,48,51,48,51,90,23,13,50,53,49,49,50,57,49,52,48,50,53,48,90,48,24,49,22,48,20,6,3,85,4,3,19,13,111,112,101,110,114,111,117,116,101,114,46,97,105,48,89,48,19,6,7,42,134,72,206,61,2,1,6,8,42,134,72,206,61,3,1,7,3,66,0,4,158,108,172,200,253,33,93,69,124,84,149,116,35,222,91,188,174,161,118,65,156,50,200,234,42,211,23,244,247,3,213,86,119,69,140,155,201,4,151,132,162,199,166,0,38,35,127,53,176,205,22,156,32,154,46,228,205,217,50,199,41,212,186,126,163,130,2,87,48,130,2,83,48,14,6,3,85,29,15,1,1,255,4,4,3,2,7,128,48,19,6,3,85,29,37,4,12,48,10,6,8,43,6,1,5,5,7,3,1,48,12,6,3,85,29,19,1,1,255,4,2,48,0,48,29,6,3,85,29,14,4,22,4,20,100,215,24,244,23,157,116,124,207,176,3,139,4,97,230,161,97,108,196,53,48,31,6,3,85,29,35,4,24,48,22,128,20,144,119,146,53,103,196,255,168,204,169,230,123,217,128,121,123,204,147,249,56,48,94,6,8,43,6,1,5,5,7,1,1,4,82,48,80,48,39,6,8,43,6,1,5,5,7,48,1,134,27,104,116,116,112,58,47,47,111,46,112,107,105,46,103,111,111,103,47,115,47,119,101,49,47,70,75,81,48,37,6,8,43,6,1,5,5,7,48,2,134,25,104,116,116,112,58,47,47,105,46,112,107,105,46,103,111,111,103,47,119,101,49,46,99,114,116,48,41,6,3,85,29,17,4,34,48,32,130,13,111,112,101,110,114,111,117,116,101,114,46,97,105,130,15,42,46,111,112,101,110,114,111,117,116,101,114,46,97,105,48,19,6,3,85,29,32,4,12,48,10,48,8,6,6,103,129,12,1,2,1,48,54,6,3,85,29,31,4,47,48,45,48,43,160,41,160,39,134,37,104,116,116,112,58,47,47,99,46,112,107,105,46,103,111,111,103,47,119,101,49,47,116,51,76,74,98,90,105,66,116,115,85,46,99,114,108,48,130,1,4,6,10,43,6,1,4,1,214,121,2,4,2,4,129,245,4,129,242,0,240,0,118,0,204,251,15,106,133,113,9,101,254,149,155,83,206,233,178,124,34,233,133,92,13,151,141,182,169,126,84,192,254,76,13,176,0,0,1,153,0,112,80,67,0,0,4,3,0,71,48,69,2,32,121,184,184,57,232,35,2,187,100,39,108,69,216,29,25,253,12,243,182,210,115,115,179,178,236,113,240,32,75,44,64,67,2,33,0,228,225,138,150,219,152,70,138,130,97,180,56,8,148,190,173,16,168,130,177,39,29,41,150,50,130,215,85,160,52,79,252,0,118,0,221,220,202,52,149,215,225,22,5,231,149,50,250,199,159,248,61,28,80,223,219,0,58,20,18,118,10,44,172,187,200,42,0,0,1,153,0,112,80,59,0,0,4,3,0,71,48,69,2,33,0,199,60,19,22,171,236,28,33,39,147,146,188,224,190,15,153,218,67,189,137,92,179,148,15,148,26,119,163,36,195,33,127,2,32,86,216,161,230,149,74,8,220,231,206,55,91,189,154,242,81,10,23,167,139,248,72,38,170,160,44,192,232,125,55,242,247,48,10,6,8,42,134,72,206,61,4,3,2,3,72,0,48,69,2,32,10,7,42,199,142,8,70,26,245,232,39,242,55,5,154,227,254,31,100,93,170,44,173,243,100,55,107,58,194,2,111,14,2,33,0,225,109,202,110,142,66,218,156,250,181,10,23,116,213,84,61,238,218,17,24,39,26,242,159,146,154,64,85,125,217,59,102,164,2,4,0,166,15,4,13,111,112,101,110,114,111,117,116,101,114,46,97,105,169,5,2,3,0,253,32,170,129,195,4,129,192,162,24,94,85,68,73,251,75,176,138,224,179,156,118,155,6,220,3,11,149,236,218,100,138,120,103,4,76,189,133,252,199,27,144,54,126,66,181,184,65,95,138,232,166,116,239,113,115,10,145,82,66,121,139,14,127,243,75,254,6,102,243,59,161,19,232,24,192,162,43,116,232,172,76,65,53,31,130,119,42,97,76,135,49,79,16,175,79,126,149,131,36,109,67,224,8,40,33,87,180,169,133,146,134,112,189,156,115,216,242,89,232,140,96,48,127,112,159,129,213,223,195,75,54,183,201,15,214,199,27,83,114,156,80,28,29,12,235,202,167,89,86,178,207,228,151,66,91,198,84,232,28,245,208,122,118,219,84,138,140,27,175,194,13,240,226,240,196,15,207,179,135,41,165,16,11,174,125,65,152,65,236,9,68,205,64,101,121,36,247,36,151,174,7,2,5,0,246,126,188,8,179,3,2,1,29],"type":"Buffer"}},"_writableState":{"bufferedIndex":0,"corked":0,"highWaterMark":16384,"length":0,"pendingcb":0,"writelen":0},"allowHalfOpen":false,"alpnProtocol":false,"authorizationError":null,"authorized":true,"autoSelectFamilyAttemptedAddresses":["************:443"],"connecting":false,"encrypted":true,"parser":null,"secureConnecting":false,"servername":"openrouter.ai","ssl":{"_parent":{"onconnection":null,"reading":true},"_parentWrap":null,"_secureContext":{"context":{}},"reading":true},"timeout":5000}]},"keepAlive":true,"keepAliveMsecs":1000,"maxCachedSessions":100,"maxFreeSockets":256,"maxSockets":null,"maxTotalSockets":null,"options":{"keepAlive":true,"noDelay":true,"path":null,"scheduling":"lifo","timeout":5000},"protocol":"https:","requests":{},"scheduling":"lifo","sockets":{},"totalSocketCount":1}}},"path":"/api/v1/chat/completions","pathname":"/api/v1/chat/completions","port":"","protocol":"https:"},"_redirectCount":0,"_redirects":[],"_requestBodyBuffers":[],"_requestBodyLength":9755,"_timeout":null,"_writableState":{"bufferedIndex":0,"corked":0,"highWaterMark":16384,"length":0,"pendingcb":0,"writelen":0}},"_removedConnection":false,"_removedContLen":false,"_removedTE":false,"_trailer":"","aborted":false,"agent":{"_events":{},"_eventsCount":2,"_sessionCache":{"list":["openrouter.ai:443:::::::::::::::::::::"],"map":{"openrouter.ai:443:::::::::::::::::::::":{"data":[48,130,5,14,2,1,1,2,2,3,4,4,2,19,2,4,32,13,228,229,61,45,5,45,139,98,124,130,234,200,153,210,194,125,158,15,117,2,177,112,193,167,108,166,203,60,157,33,37,4,48,185,115,34,255,94,179,0,131,69,53,76,108,68,205,182,183,244,175,80,154,30,241,113,52,1,4,78,74,209,164,110,164,41,237,242,39,229,51,221,25,102,232,148,87,162,254,49,99,161,6,2,4,104,181,170,215,162,4,2,2,28,32,163,130,3,174,48,130,3,170,48,130,3,80,160,3,2,1,2,2,16,20,164,8,48,247,211,4,94,17,104,186,54,234,8,85,73,48,10,6,8,42,134,72,206,61,4,3,2,48,59,49,11,48,9,6,3,85,4,6,19,2,85,83,49,30,48,28,6,3,85,4,10,19,21,71,111,111,103,108,101,32,84,114,117,115,116,32,83,101,114,118,105,99,101,115,49,12,48,10,6,3,85,4,3,19,3,87,69,49,48,30,23,13,50,53,48,56,51,49,49,51,48,51,48,51,90,23,13,50,53,49,49,50,57,49,52,48,50,53,48,90,48,24,49,22,48,20,6,3,85,4,3,19,13,111,112,101,110,114,111,117,116,101,114,46,97,105,48,89,48,19,6,7,42,134,72,206,61,2,1,6,8,42,134,72,206,61,3,1,7,3,66,0,4,158,108,172,200,253,33,93,69,124,84,149,116,35,222,91,188,174,161,118,65,156,50,200,234,42,211,23,244,247,3,213,86,119,69,140,155,201,4,151,132,162,199,166,0,38,35,127,53,176,205,22,156,32,154,46,228,205,217,50,199,41,212,186,126,163,130,2,87,48,130,2,83,48,14,6,3,85,29,15,1,1,255,4,4,3,2,7,128,48,19,6,3,85,29,37,4,12,48,10,6,8,43,6,1,5,5,7,3,1,48,12,6,3,85,29,19,1,1,255,4,2,48,0,48,29,6,3,85,29,14,4,22,4,20,100,215,24,244,23,157,116,124,207,176,3,139,4,97,230,161,97,108,196,53,48,31,6,3,85,29,35,4,24,48,22,128,20,144,119,146,53,103,196,255,168,204,169,230,123,217,128,121,123,204,147,249,56,48,94,6,8,43,6,1,5,5,7,1,1,4,82,48,80,48,39,6,8,43,6,1,5,5,7,48,1,134,27,104,116,116,112,58,47,47,111,46,112,107,105,46,103,111,111,103,47,115,47,119,101,49,47,70,75,81,48,37,6,8,43,6,1,5,5,7,48,2,134,25,104,116,116,112,58,47,47,105,46,112,107,105,46,103,111,111,103,47,119,101,49,46,99,114,116,48,41,6,3,85,29,17,4,34,48,32,130,13,111,112,101,110,114,111,117,116,101,114,46,97,105,130,15,42,46,111,112,101,110,114,111,117,116,101,114,46,97,105,48,19,6,3,85,29,32,4,12,48,10,48,8,6,6,103,129,12,1,2,1,48,54,6,3,85,29,31,4,47,48,45,48,43,160,41,160,39,134,37,104,116,116,112,58,47,47,99,46,112,107,105,46,103,111,111,103,47,119,101,49,47,116,51,76,74,98,90,105,66,116,115,85,46,99,114,108,48,130,1,4,6,10,43,6,1,4,1,214,121,2,4,2,4,129,245,4,129,242,0,240,0,118,0,204,251,15,106,133,113,9,101,254,149,155,83,206,233,178,124,34,233,133,92,13,151,141,182,169,126,84,192,254,76,13,176,0,0,1,153,0,112,80,67,0,0,4,3,0,71,48,69,2,32,121,184,184,57,232,35,2,187,100,39,108,69,216,29,25,253,12,243,182,210,115,115,179,178,236,113,240,32,75,44,64,67,2,33,0,228,225,138,150,219,152,70,138,130,97,180,56,8,148,190,173,16,168,130,177,39,29,41,150,50,130,215,85,160,52,79,252,0,118,0,221,220,202,52,149,215,225,22,5,231,149,50,250,199,159,248,61,28,80,223,219,0,58,20,18,118,10,44,172,187,200,42,0,0,1,153,0,112,80,59,0,0,4,3,0,71,48,69,2,33,0,199,60,19,22,171,236,28,33,39,147,146,188,224,190,15,153,218,67,189,137,92,179,148,15,148,26,119,163,36,195,33,127,2,32,86,216,161,230,149,74,8,220,231,206,55,91,189,154,242,81,10,23,167,139,248,72,38,170,160,44,192,232,125,55,242,247,48,10,6,8,42,134,72,206,61,4,3,2,3,72,0,48,69,2,32,10,7,42,199,142,8,70,26,245,232,39,242,55,5,154,227,254,31,100,93,170,44,173,243,100,55,107,58,194,2,111,14,2,33,0,225,109,202,110,142,66,218,156,250,181,10,23,116,213,84,61,238,218,17,24,39,26,242,159,146,154,64,85,125,217,59,102,164,2,4,0,166,15,4,13,111,112,101,110,114,111,117,116,101,114,46,97,105,169,5,2,3,0,253,32,170,129,195,4,129,192,162,24,94,85,68,73,251,75,176,138,224,179,156,118,155,6,122,10,201,131,35,146,100,30,37,53,71,144,109,220,254,8,191,149,248,24,17,38,32,16,220,255,76,193,246,236,83,79,84,116,216,45,32,32,241,84,125,174,244,250,174,157,141,5,109,61,56,92,29,202,153,159,28,238,66,156,62,183,137,86,128,31,239,214,34,107,241,85,127,46,67,122,126,15,175,248,54,24,129,150,158,4,214,69,212,153,146,254,187,29,143,249,109,26,209,184,238,247,85,149,195,177,212,63,64,78,211,121,3,78,77,250,9,243,44,190,218,138,112,163,161,14,115,57,69,183,47,35,94,239,198,85,86,161,24,126,213,196,164,44,188,111,226,46,168,173,159,66,16,158,32,52,30,219,82,67,100,82,178,96,243,86,242,120,187,25,122,149,20,197,179,4,174,6,2,4,0,254,227,171,179,3,2,1,29],"type":"Buffer"}}},"defaultPort":443,"freeSockets":{"openrouter.ai:443:::::::::::::::::::::":[{"_SNICallback":null,"_closeAfterHandlingError":false,"_controlReleased":true,"_events":{"close":[null,null,null],"timeout":[null,null]},"_eventsCount":9,"_hadError":false,"_host":"openrouter.ai","_httpMessage":null,"_newSessionPending":false,"_parent":null,"_pendingData":null,"_pendingEncoding":"","_readableState":{"awaitDrainWriters":null,"buffer":[],"bufferIndex":0,"highWaterMark":16384,"length":0,"pipes":[]},"_rejectUnauthorized":true,"_requestCert":true,"_secureEstablished":true,"_securePending":false,"_server":null,"_sockname":null,"_tlsOptions":{"isServer":false,"pipe":false,"rejectUnauthorized":true,"requestCert":true,"secureContext":{"context":{}},"session":{"data":[48,130,5,15,2,1,1,2,2,3,4,4,2,19,2,4,32,209,105,134,79,242,244,131,170,85,204,88,109,214,222,84,36,197,38,157,159,88,200,238,190,73,116,3,141,119,216,191,122,4,48,147,30,244,78,115,132,41,207,31,152,23,195,47,88,252,97,136,42,11,132,240,32,179,249,163,65,129,142,220,47,80,167,29,131,221,206,224,47,182,7,92,98,82,11,38,77,150,21,161,6,2,4,104,181,170,17,162,4,2,2,28,32,163,130,3,174,48,130,3,170,48,130,3,80,160,3,2,1,2,2,16,20,164,8,48,247,211,4,94,17,104,186,54,234,8,85,73,48,10,6,8,42,134,72,206,61,4,3,2,48,59,49,11,48,9,6,3,85,4,6,19,2,85,83,49,30,48,28,6,3,85,4,10,19,21,71,111,111,103,108,101,32,84,114,117,115,116,32,83,101,114,118,105,99,101,115,49,12,48,10,6,3,85,4,3,19,3,87,69,49,48,30,23,13,50,53,48,56,51,49,49,51,48,51,48,51,90,23,13,50,53,49,49,50,57,49,52,48,50,53,48,90,48,24,49,22,48,20,6,3,85,4,3,19,13,111,112,101,110,114,111,117,116,101,114,46,97,105,48,89,48,19,6,7,42,134,72,206,61,2,1,6,8,42,134,72,206,61,3,1,7,3,66,0,4,158,108,172,200,253,33,93,69,124,84,149,116,35,222,91,188,174,161,118,65,156,50,200,234,42,211,23,244,247,3,213,86,119,69,140,155,201,4,151,132,162,199,166,0,38,35,127,53,176,205,22,156,32,154,46,228,205,217,50,199,41,212,186,126,163,130,2,87,48,130,2,83,48,14,6,3,85,29,15,1,1,255,4,4,3,2,7,128,48,19,6,3,85,29,37,4,12,48,10,6,8,43,6,1,5,5,7,3,1,48,12,6,3,85,29,19,1,1,255,4,2,48,0,48,29,6,3,85,29,14,4,22,4,20,100,215,24,244,23,157,116,124,207,176,3,139,4,97,230,161,97,108,196,53,48,31,6,3,85,29,35,4,24,48,22,128,20,144,119,146,53,103,196,255,168,204,169,230,123,217,128,121,123,204,147,249,56,48,94,6,8,43,6,1,5,5,7,1,1,4,82,48,80,48,39,6,8,43,6,1,5,5,7,48,1,134,27,104,116,116,112,58,47,47,111,46,112,107,105,46,103,111,111,103,47,115,47,119,101,49,47,70,75,81,48,37,6,8,43,6,1,5,5,7,48,2,134,25,104,116,116,112,58,47,47,105,46,112,107,105,46,103,111,111,103,47,119,101,49,46,99,114,116,48,41,6,3,85,29,17,4,34,48,32,130,13,111,112,101,110,114,111,117,116,101,114,46,97,105,130,15,42,46,111,112,101,110,114,111,117,116,101,114,46,97,105,48,19,6,3,85,29,32,4,12,48,10,48,8,6,6,103,129,12,1,2,1,48,54,6,3,85,29,31,4,47,48,45,48,43,160,41,160,39,134,37,104,116,116,112,58,47,47,99,46,112,107,105,46,103,111,111,103,47,119,101,49,47,116,51,76,74,98,90,105,66,116,115,85,46,99,114,108,48,130,1,4,6,10,43,6,1,4,1,214,121,2,4,2,4,129,245,4,129,242,0,240,0,118,0,204,251,15,106,133,113,9,101,254,149,155,83,206,233,178,124,34,233,133,92,13,151,141,182,169,126,84,192,254,76,13,176,0,0,1,153,0,112,80,67,0,0,4,3,0,71,48,69,2,32,121,184,184,57,232,35,2,187,100,39,108,69,216,29,25,253,12,243,182,210,115,115,179,178,236,113,240,32,75,44,64,67,2,33,0,228,225,138,150,219,152,70,138,130,97,180,56,8,148,190,173,16,168,130,177,39,29,41,150,50,130,215,85,160,52,79,252,0,118,0,221,220,202,52,149,215,225,22,5,231,149,50,250,199,159,248,61,28,80,223,219,0,58,20,18,118,10,44,172,187,200,42,0,0,1,153,0,112,80,59,0,0,4,3,0,71,48,69,2,33,0,199,60,19,22,171,236,28,33,39,147,146,188,224,190,15,153,218,67,189,137,92,179,148,15,148,26,119,163,36,195,33,127,2,32,86,216,161,230,149,74,8,220,231,206,55,91,189,154,242,81,10,23,167,139,248,72,38,170,160,44,192,232,125,55,242,247,48,10,6,8,42,134,72,206,61,4,3,2,3,72,0,48,69,2,32,10,7,42,199,142,8,70,26,245,232,39,242,55,5,154,227,254,31,100,93,170,44,173,243,100,55,107,58,194,2,111,14,2,33,0,225,109,202,110,142,66,218,156,250,181,10,23,116,213,84,61,238,218,17,24,39,26,242,159,146,154,64,85,125,217,59,102,164,2,4,0,166,15,4,13,111,112,101,110,114,111,117,116,101,114,46,97,105,169,5,2,3,0,253,32,170,129,195,4,129,192,162,24,94,85,68,73,251,75,176,138,224,179,156,118,155,6,220,3,11,149,236,218,100,138,120,103,4,76,189,133,252,199,27,144,54,126,66,181,184,65,95,138,232,166,116,239,113,115,10,145,82,66,121,139,14,127,243,75,254,6,102,243,59,161,19,232,24,192,162,43,116,232,172,76,65,53,31,130,119,42,97,76,135,49,79,16,175,79,126,149,131,36,109,67,224,8,40,33,87,180,169,133,146,134,112,189,156,115,216,242,89,232,140,96,48,127,112,159,129,213,223,195,75,54,183,201,15,214,199,27,83,114,156,80,28,29,12,235,202,167,89,86,178,207,228,151,66,91,198,84,232,28,245,208,122,118,219,84,138,140,27,175,194,13,240,226,240,196,15,207,179,135,41,165,16,11,174,125,65,152,65,236,9,68,205,64,101,121,36,247,36,151,174,7,2,5,0,246,126,188,8,179,3,2,1,29],"type":"Buffer"}},"_writableState":{"bufferedIndex":0,"corked":0,"highWaterMark":16384,"length":0,"pendingcb":0,"writelen":0},"allowHalfOpen":false,"alpnProtocol":false,"authorizationError":null,"authorized":true,"autoSelectFamilyAttemptedAddresses":["************:443"],"connecting":false,"encrypted":true,"parser":null,"secureConnecting":false,"servername":"openrouter.ai","ssl":{"_parent":{"onconnection":null,"reading":true},"_parentWrap":null,"_secureContext":{"context":{}},"reading":true},"timeout":5000}]},"keepAlive":true,"keepAliveMsecs":1000,"maxCachedSessions":100,"maxFreeSockets":256,"maxSockets":null,"maxTotalSockets":null,"options":{"keepAlive":true,"noDelay":true,"path":null,"scheduling":"lifo","timeout":5000},"protocol":"https:","requests":{},"scheduling":"lifo","sockets":{},"totalSocketCount":1},"chunkedEncoding":false,"destroyed":true,"finished":true,"host":"openrouter.ai","maxHeadersCount":null,"maxRequestsOnConnectionReached":false,"method":"POST","outputData":[],"outputSize":0,"parser":null,"path":"/api/v1/chat/completions","protocol":"https:","res":{"_consuming":true,"_dumped":false,"_events":{"close":[null,null,null],"end":[null,null,null],"error":[null,null,null],"finish":[null,null]},"_eventsCount":4,"_readableState":{"awaitDrainWriters":null,"buffer":[],"bufferIndex":0,"highWaterMark":16384,"length":0,"pipes":[]},"aborted":false,"client":{"_SNICallback":null,"_closeAfterHandlingError":false,"_controlReleased":true,"_events":{"close":[null,null,null],"timeout":[null,null]},"_eventsCount":9,"_hadError":false,"_host":"openrouter.ai","_httpMessage":null,"_newSessionPending":false,"_parent":null,"_pendingData":null,"_pendingEncoding":"","_readableState":{"awaitDrainWriters":null,"buffer":[],"bufferIndex":0,"highWaterMark":16384,"length":0,"pipes":[]},"_rejectUnauthorized":true,"_requestCert":true,"_secureEstablished":true,"_securePending":false,"_server":null,"_sockname":null,"_tlsOptions":{"isServer":false,"pipe":false,"rejectUnauthorized":true,"requestCert":true,"secureContext":{"context":{}},"session":{"data":[48,130,5,15,2,1,1,2,2,3,4,4,2,19,2,4,32,209,105,134,79,242,244,131,170,85,204,88,109,214,222,84,36,197,38,157,159,88,200,238,190,73,116,3,141,119,216,191,122,4,48,147,30,244,78,115,132,41,207,31,152,23,195,47,88,252,97,136,42,11,132,240,32,179,249,163,65,129,142,220,47,80,167,29,131,221,206,224,47,182,7,92,98,82,11,38,77,150,21,161,6,2,4,104,181,170,17,162,4,2,2,28,32,163,130,3,174,48,130,3,170,48,130,3,80,160,3,2,1,2,2,16,20,164,8,48,247,211,4,94,17,104,186,54,234,8,85,73,48,10,6,8,42,134,72,206,61,4,3,2,48,59,49,11,48,9,6,3,85,4,6,19,2,85,83,49,30,48,28,6,3,85,4,10,19,21,71,111,111,103,108,101,32,84,114,117,115,116,32,83,101,114,118,105,99,101,115,49,12,48,10,6,3,85,4,3,19,3,87,69,49,48,30,23,13,50,53,48,56,51,49,49,51,48,51,48,51,90,23,13,50,53,49,49,50,57,49,52,48,50,53,48,90,48,24,49,22,48,20,6,3,85,4,3,19,13,111,112,101,110,114,111,117,116,101,114,46,97,105,48,89,48,19,6,7,42,134,72,206,61,2,1,6,8,42,134,72,206,61,3,1,7,3,66,0,4,158,108,172,200,253,33,93,69,124,84,149,116,35,222,91,188,174,161,118,65,156,50,200,234,42,211,23,244,247,3,213,86,119,69,140,155,201,4,151,132,162,199,166,0,38,35,127,53,176,205,22,156,32,154,46,228,205,217,50,199,41,212,186,126,163,130,2,87,48,130,2,83,48,14,6,3,85,29,15,1,1,255,4,4,3,2,7,128,48,19,6,3,85,29,37,4,12,48,10,6,8,43,6,1,5,5,7,3,1,48,12,6,3,85,29,19,1,1,255,4,2,48,0,48,29,6,3,85,29,14,4,22,4,20,100,215,24,244,23,157,116,124,207,176,3,139,4,97,230,161,97,108,196,53,48,31,6,3,85,29,35,4,24,48,22,128,20,144,119,146,53,103,196,255,168,204,169,230,123,217,128,121,123,204,147,249,56,48,94,6,8,43,6,1,5,5,7,1,1,4,82,48,80,48,39,6,8,43,6,1,5,5,7,48,1,134,27,104,116,116,112,58,47,47,111,46,112,107,105,46,103,111,111,103,47,115,47,119,101,49,47,70,75,81,48,37,6,8,43,6,1,5,5,7,48,2,134,25,104,116,116,112,58,47,47,105,46,112,107,105,46,103,111,111,103,47,119,101,49,46,99,114,116,48,41,6,3,85,29,17,4,34,48,32,130,13,111,112,101,110,114,111,117,116,101,114,46,97,105,130,15,42,46,111,112,101,110,114,111,117,116,101,114,46,97,105,48,19,6,3,85,29,32,4,12,48,10,48,8,6,6,103,129,12,1,2,1,48,54,6,3,85,29,31,4,47,48,45,48,43,160,41,160,39,134,37,104,116,116,112,58,47,47,99,46,112,107,105,46,103,111,111,103,47,119,101,49,47,116,51,76,74,98,90,105,66,116,115,85,46,99,114,108,48,130,1,4,6,10,43,6,1,4,1,214,121,2,4,2,4,129,245,4,129,242,0,240,0,118,0,204,251,15,106,133,113,9,101,254,149,155,83,206,233,178,124,34,233,133,92,13,151,141,182,169,126,84,192,254,76,13,176,0,0,1,153,0,112,80,67,0,0,4,3,0,71,48,69,2,32,121,184,184,57,232,35,2,187,100,39,108,69,216,29,25,253,12,243,182,210,115,115,179,178,236,113,240,32,75,44,64,67,2,33,0,228,225,138,150,219,152,70,138,130,97,180,56,8,148,190,173,16,168,130,177,39,29,41,150,50,130,215,85,160,52,79,252,0,118,0,221,220,202,52,149,215,225,22,5,231,149,50,250,199,159,248,61,28,80,223,219,0,58,20,18,118,10,44,172,187,200,42,0,0,1,153,0,112,80,59,0,0,4,3,0,71,48,69,2,33,0,199,60,19,22,171,236,28,33,39,147,146,188,224,190,15,153,218,67,189,137,92,179,148,15,148,26,119,163,36,195,33,127,2,32,86,216,161,230,149,74,8,220,231,206,55,91,189,154,242,81,10,23,167,139,248,72,38,170,160,44,192,232,125,55,242,247,48,10,6,8,42,134,72,206,61,4,3,2,3,72,0,48,69,2,32,10,7,42,199,142,8,70,26,245,232,39,242,55,5,154,227,254,31,100,93,170,44,173,243,100,55,107,58,194,2,111,14,2,33,0,225,109,202,110,142,66,218,156,250,181,10,23,116,213,84,61,238,218,17,24,39,26,242,159,146,154,64,85,125,217,59,102,164,2,4,0,166,15,4,13,111,112,101,110,114,111,117,116,101,114,46,97,105,169,5,2,3,0,253,32,170,129,195,4,129,192,162,24,94,85,68,73,251,75,176,138,224,179,156,118,155,6,220,3,11,149,236,218,100,138,120,103,4,76,189,133,252,199,27,144,54,126,66,181,184,65,95,138,232,166,116,239,113,115,10,145,82,66,121,139,14,127,243,75,254,6,102,243,59,161,19,232,24,192,162,43,116,232,172,76,65,53,31,130,119,42,97,76,135,49,79,16,175,79,126,149,131,36,109,67,224,8,40,33,87,180,169,133,146,134,112,189,156,115,216,242,89,232,140,96,48,127,112,159,129,213,223,195,75,54,183,201,15,214,199,27,83,114,156,80,28,29,12,235,202,167,89,86,178,207,228,151,66,91,198,84,232,28,245,208,122,118,219,84,138,140,27,175,194,13,240,226,240,196,15,207,179,135,41,165,16,11,174,125,65,152,65,236,9,68,205,64,101,121,36,247,36,151,174,7,2,5,0,246,126,188,8,179,3,2,1,29],"type":"Buffer"}},"_writableState":{"bufferedIndex":0,"corked":0,"highWaterMark":16384,"length":0,"pendingcb":0,"writelen":0},"allowHalfOpen":false,"alpnProtocol":false,"authorizationError":null,"authorized":true,"autoSelectFamilyAttemptedAddresses":["************:443"],"connecting":false,"encrypted":true,"parser":null,"secureConnecting":false,"servername":"openrouter.ai","ssl":{"_parent":{"onconnection":null,"reading":true},"_parentWrap":null,"_secureContext":{"context":{}},"reading":true},"timeout":5000},"complete":true,"httpVersion":"1.1","httpVersionMajor":1,"httpVersionMinor":1,"method":null,"rawHeaders":["Date","Mon, 01 Sep 2025 14:16:57 GMT","Content-Type","application/json","Transfer-Encoding","chunked","Connection","keep-alive","Content-Encoding","gzip","Access-Control-Allow-Origin","*","Vary","Accept-Encoding","Permissions-Policy","payment=(self \"https://checkout.stripe.com\" \"https://connect-js.stripe.com\" \"https://js.stripe.com\" \"https://*.js.stripe.com\" \"https://hooks.stripe.com\")","Referrer-Policy","no-referrer, strict-origin-when-cross-origin","X-Content-Type-Options","nosniff","Server","cloudflare","CF-RAY","9785636f2a03e1fd-MRS"],"rawTrailers":[],"redirects":[],"req":"[Circular]","responseUrl":"https://openrouter.ai/api/v1/chat/completions","socket":null,"statusCode":404,"statusMessage":"Not Found","upgrade":false,"url":""},"reusedSocket":true,"sendDate":false,"shouldKeepAlive":true,"strictContentLength":false,"timeoutCb":null,"upgradeOrConnect":false,"useChunkedEncodingByDefault":true,"writable":true},"status":404,"statusText":"Not Found"},"stack":"AxiosError: Request failed with status code 404\n    at settle (C:\\Users\\<USER>\\CascadeProjects\\Github OK\\backend\\node_modules\\axios\\lib\\core\\settle.js:19:12)\n    at Unzip.handleStreamEnd (C:\\Users\\<USER>\\CascadeProjects\\Github OK\\backend\\node_modules\\axios\\lib\\adapters\\http.js:599:11)\n    at Unzip.emit (node:events:536:35)\n    at endReadableNT (node:internal/streams/readable:1698:12)\n    at process.processTicksAndRejections (node:internal/process/task_queues:90:21)\n    at Axios.request (C:\\Users\\<USER>\\CascadeProjects\\Github OK\\backend\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async generateMissionStep (C:\\Users\\<USER>\\CascadeProjects\\Github OK\\backend\\src\\controllers\\missionAssistantController.ts:578:22)","status":404,"timestamp":"2025-09-01 16:16:55"}
warn: Restitution des crédits suite à une erreur API d'assistant mission {"timestamp":"2025-09-01 16:16:55"}
info: Crédits IA restitués pour l'utilisateur 58687861-4c81-4d9e-b240-9d407a83fdf7: 52 {"timestamp":"2025-09-01 16:16:55"}
error: [API_ERROR] Erreur serveur {"duration":"2954ms","ip":"::1","method":"POST","path":"/generate-step","status":500,"timestamp":"2025-09-01 16:16:55","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"58687861-4c81-4d9e-b240-9d407a83fdf7"}